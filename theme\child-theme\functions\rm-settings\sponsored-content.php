<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Callback function for the sponsored content settings page
function sponsored_content_settings_callback() {
    ?>
    <div class="wrap">
        <h1>Sponsored Content Settings</h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('sponsored_content_settings_group');
            do_settings_sections('sponsored-content-settings');
            submit_button();
            ?>
        </form>
    </div>
    <?php
}

// Register settings
add_action('admin_init', 'sponsored_content_settings_init');
function sponsored_content_settings_init() {
    register_setting('sponsored_content_settings_group', 'sponsored_content_settings');

    add_settings_section(
        'sponsored_content_section_general',
        'General Settings',
        null,
        'sponsored-content-settings'
    );

    $placements = [
        'manga_search' => 'Manga Search (Search Page)',
        'content_reading_list_above' => 'Content Reading List (Above Content)',
        'content_reading_list_below' => 'Content Reading List (Below Content)',
        'recently_updated' => 'Recently Updated (Homepage)',
        'manga_content' => 'Manga Content (Details Page)',
        'socials' => 'Socials Section (Homepage)',
    ];

    foreach ($placements as $key => $label) {
        add_settings_field(
            'sponsored_content_enable_' . $key,
            'Enable ' . $label,
            'sponsored_content_enable_callback',
            'sponsored-content-settings',
            'sponsored_content_section_general',
            ['key' => $key, 'label' => $label]
        );

        add_settings_field(
            'sponsored_content_link_' . $key,
            $label . ' Link',
            'sponsored_content_link_callback',
            'sponsored-content-settings',
            'sponsored_content_section_general',
            ['key' => $key]
        );

        if ($key !== 'socials') {
            add_settings_field(
                'sponsored_content_image_' . $key,
                $label . ' Image URL',
                'sponsored_content_image_callback',
                'sponsored-content-settings',
                'sponsored_content_section_general',
                ['key' => $key]
            );
        } else {
            // Fields specific to Socials
            add_settings_field(
                'sponsored_content_socials_title',
                $label . ' Title',
                'sponsored_content_socials_title_callback',
                'sponsored-content-settings',
                'sponsored_content_section_general',
                ['key' => $key]
            );
            add_settings_field(
                'sponsored_content_socials_subtitle',
                $label . ' Subtitle',
                'sponsored_content_socials_subtitle_callback',
                'sponsored-content-settings',
                'sponsored_content_section_general',
                ['key' => $key]
            );
            add_settings_field(
                'sponsored_content_socials_button_text',
                $label . ' Button Text',
                'sponsored_content_socials_button_text_callback',
                'sponsored-content-settings',
                'sponsored_content_section_general',
                ['key' => $key]
            );
            add_settings_field(
                'sponsored_content_socials_icon_url',
                $label . ' Icon URL',
                'sponsored_content_socials_icon_url_callback',
                'sponsored-content-settings',
                'sponsored_content_section_general',
                ['key' => $key]
            );
        }
    }
}

function sponsored_content_enable_callback($args) {
    $options = get_option('sponsored_content_settings');
    $key = $args['key'];
    $checked = isset($options['enable_' . $key]) ? 'checked' : '';
    echo "<div class=\"setting-item\"><input type='checkbox' id='sponsored_content_enable_{$key}' name='sponsored_content_settings[enable_{$key}]' value='1' {$checked}></div>";
}

function sponsored_content_link_callback($args) {
    $options = get_option('sponsored_content_settings');
    $key = $args['key'];
    $value = isset($options['link_' . $key]) ? esc_attr($options['link_' . $key]) : '';
    echo "<div class=\"setting-item\"><input type='url' id='sponsored_content_link_{$key}' name='sponsored_content_settings[link_{$key}]' value='{$value}' class='regular-text'></div>";
}

function sponsored_content_image_callback($args) {
    $options = get_option('sponsored_content_settings');
    $key = $args['key'];
    $value = isset($options['image_' . $key]) ? esc_attr($options['image_' . $key]) : '';
    echo "<div class=\"setting-item\"><input type='url' id='sponsored_content_image_{$key}' name='sponsored_content_settings[image_{$key}]' value='{$value}' class='regular-text'> <p class='description'>Enter the full URL for the image.</p></div>";
}

// Callbacks for Socials specific fields
function sponsored_content_socials_title_callback($args) {
    $options = get_option('sponsored_content_settings');
    $key = $args['key'];
    $value = isset($options['socials_title']) ? esc_attr($options['socials_title']) : '';
    echo "<div class=\"setting-item\"><input type='text' id='sponsored_content_socials_title' name='sponsored_content_settings[socials_title]' value='{$value}' class='regular-text'></div>";
}

function sponsored_content_socials_subtitle_callback($args) {
    $options = get_option('sponsored_content_settings');
    $key = $args['key'];
    $value = isset($options['socials_subtitle']) ? esc_attr($options['socials_subtitle']) : '';
    echo "<div class=\"setting-item\"><input type='text' id='sponsored_content_socials_subtitle' name='sponsored_content_settings[socials_subtitle]' value='{$value}' class='regular-text'></div>";
}

function sponsored_content_socials_button_text_callback($args) {
    $options = get_option('sponsored_content_settings');
    $key = $args['key'];
    $value = isset($options['socials_button_text']) ? esc_attr($options['socials_button_text']) : '';
    echo "<div class=\"setting-item\"><input type='text' id='sponsored_content_socials_button_text' name='sponsored_content_settings[socials_button_text]' value='{$value}' class='regular-text'></div>";
}

function sponsored_content_socials_icon_url_callback($args) {
    $options = get_option('sponsored_content_settings');
    $key = $args['key'];
    $value = isset($options['socials_icon_url']) ? esc_attr($options['socials_icon_url']) : '';
    echo "<div class=\"setting-item\"><input type='url' id='sponsored_content_socials_icon_url' name='sponsored_content_settings[socials_icon_url]' value='{$value}' class='regular-text'> <p class='description'>Enter the full URL for the icon image.</p></div>";
}

?>
