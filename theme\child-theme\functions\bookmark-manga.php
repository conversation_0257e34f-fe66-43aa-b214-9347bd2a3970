<?php

function child_manga_toggle_bookmark() {
// Check if user is logged in
if (!is_user_logged_in()) {
wp_send_json_error(array('message' => __('Please log in to use this feature', 'child-mfire')));
return;
}

// Get current user ID and manga post ID
$user_id = get_current_user_id();
$post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;

if (!$post_id || get_post_type($post_id) !== 'wp-manga') {
wp_send_json_error(array('message' => __('Invalid manga ID.', 'child-mfire')));
return;
}

// Get the current bookmarks
$raw_bookmarked_data = get_user_meta($user_id, 'bookmarked_mangas', true);
$bookmarked_manga_ids = array(); // Initialize as an empty array for simple IDs

if (is_array($raw_bookmarked_data) && !empty($raw_bookmarked_data)) {
    // Check the first element to determine structure
    // Ensure $raw_bookmarked_data is not empty before calling reset
    $first_element = reset($raw_bookmarked_data); 

    if (is_array($first_element) && isset($first_element['id'])) {
        // Complex format: array of associative arrays, extract IDs
        foreach ($raw_bookmarked_data as $item) {
            if (is_array($item) && isset($item['id'])) {
                $bookmarked_manga_ids[] = (int)$item['id'];
            }
        }
        // Remove duplicates that might have arisen
        $bookmarked_manga_ids = array_values(array_unique($bookmarked_manga_ids));
    } elseif (is_numeric($first_element)) { // Simple format: array of IDs
        // Filter out non-numeric items and ensure all are positive integers
        $numeric_ids = array_filter($raw_bookmarked_data, 'is_numeric');
        $bookmarked_manga_ids = array_map('intval', $numeric_ids);
        $bookmarked_manga_ids = array_filter($bookmarked_manga_ids, function($id) { return $id > 0; });
        $bookmarked_manga_ids = array_values(array_unique($bookmarked_manga_ids));
    }
    // If $raw_bookmarked_data was empty, an unhandled format, or an array of empty arrays, 
    // $bookmarked_manga_ids might still be empty or contain what was parseable.
}
// At this point, $bookmarked_manga_ids should be a flat array of integer IDs.

// Add or remove the post ID from the simple list of IDs
if (in_array($post_id, $bookmarked_manga_ids)) {
// Remove bookmark
$bookmarked_manga_ids = array_diff($bookmarked_manga_ids, array($post_id));
$message = __('Retiré des favoris', 'child-mfire');
} else {
// Add bookmark
$bookmarked_manga_ids[] = $post_id;
$message = __('Ajouté aux favoris', 'child-mfire');
}

// Ensure the array is re-indexed if elements were removed and remove duplicates again
$bookmarked_manga_ids = array_values(array_unique($bookmarked_manga_ids));

// Update user meta with the new (always simple) bookmarked manga IDs
update_user_meta($user_id, 'bookmarked_mangas', $bookmarked_manga_ids);

// Send success response (using the processed $bookmarked_manga_ids for the 'bookmarked' status)
wp_send_json_success(array('message' => $message, 'bookmarked' => in_array($post_id, $bookmarked_manga_ids)));
}
add_action('wp_ajax_toggle_bookmark', 'child_manga_toggle_bookmark');
add_action('wp_ajax_nopriv_toggle_bookmark', 'child_manga_toggle_bookmark'); // Allow non-logged in users (will send error)