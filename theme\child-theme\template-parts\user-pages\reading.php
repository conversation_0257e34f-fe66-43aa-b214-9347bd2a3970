<main class="user-panel">
    <div class="container">
        <div class="main-inner">
            <aside class="sidebar">
                <ul class="user-nav">
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=account-settings'); ?>">
                            <i class="fa-solid fa-user"></i> <span>Compte</span>
                        </a>
                    </li>
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=bookmark'); ?>">
                            <i class="fa-solid fa-bookmark"></i> <span>Favoris</span>
                        </a>
                    </li>

                    <?php /* if( get_option('allow_post', 1) ) : ?>
                        <li>
                            <a class="" href="<?php echo site_url('/user/?tab=add-manga'); ?>">
                                <i class="fa-solid fa-circle-plus"></i> <span>Ajouter un manga</span>
                            </a>
                        </li>
                        <li>
                            <a class="" href="<?php echo site_url('/user/?tab=chapters'); ?>">
                                <i class="fa-solid fa-book-open-reader"></i> <span>Liste des chapitres</span>
                            </a>
                        </li>
                    <?php endif; */ ?>

                    <li>
                        <a class="active" href="<?php echo site_url('/user/?tab=reading'); ?>">
                            <i class="fa-solid fa-clock-rotate-left"></i> <span>Historique</span>
                        </a>
                    </li>
                </ul>
            </aside>

            <aside class="content">
                <section>
                    <?php
                    global $wpdb;
                    // Fetch user reading history
                    $reading_history = get_user_meta(get_current_user_id(), 'reading_history', true);
                    ?>
                    <div class="head">
                        <h2>Historique de lecture</h2>
                        <?php if (is_array($reading_history) && count($reading_history) > 0): ?>
                            <button id="clear-reading-history" class="btn btn-danger">
                                <i class="fa-solid fa-trash"></i> Effacer l'historique
                            </button>
                            <!-- Add Modal HTML -->
                            <div id="clearHistoryModal" class="modal fade">
                                <div class="modal-dialog modal-confirm">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <div class="icon-box">
                                                <i class="fa-solid fa-trash"></i>
                                            </div>
                                            <h4 class="modal-title">Êtes-vous sûr ?</h4>
                                        </div>
                                        <div class="modal-body">
                                            <p>Voulez-vous vraiment supprimer tout votre historique ? Ce processus ne peut pas être annulé.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                                            <button type="button" class="btn btn-danger" id="confirmClear">Oui, effacez-le !</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="original card-lg reading">
                        <?php
                        $per_page = 10;
                        $valid_entries = [];

                        if ($reading_history) {
                            // Process valid entries
                            foreach ($reading_history as $entry) {
                                $manga_id = $entry[0];
                                $chapter_id = $entry[1];
                                $last_read = isset($entry[2]) ? $entry[2] : current_time('timestamp');

                                // Get post details
                                $post = get_post($manga_id);

                                // Skip invalid entries
                                if (!$post) {
                                    continue;
                                }

                                // Check if the manga is adult content
                                $adult_content_meta = get_post_meta($manga_id, 'manga_adult_content', true);
                                $is_adult = is_array(maybe_unserialize($adult_content_meta)) && in_array('yes', maybe_unserialize($adult_content_meta));

                                if (!$show_adult && $is_adult) {
                                    continue;
                                }

                                // Query to get the chapter data
                                $chapter_data = $wpdb->get_row($wpdb->prepare(
                                    "SELECT chapter_name, chapter_slug 
                                    FROM {$wpdb->prefix}manga_chapters 
                                    WHERE chapter_id = %d",
                                    $chapter_id
                                ));

                                if ($chapter_data && !empty($chapter_data->chapter_name)) {
                                    $valid_entries[] = [$manga_id, $chapter_id, $last_read];
                                }
                            }

                            $valid_entries = array_reverse($valid_entries);
                            
                            // Limit to 20 entries
                            if (count($valid_entries) > 20) {
                                $valid_entries = array_slice($valid_entries, 0, 20);
                                // Update the user meta with the limited entries
                                update_user_meta(get_current_user_id(), 'reading_history', array_reverse($valid_entries));
                            }

                            // Pagination setup
                            $total_entries = count($valid_entries);
                            $total_pages = ceil($total_entries / $per_page);
                            $current_page = get_query_var('paged') ? get_query_var('paged') : 1;
                            $current_page = max(1, min($current_page, $total_pages));
                            $start_index = ($current_page - 1) * $per_page;
                            $valid_entries_slice = array_slice($valid_entries, $start_index, $per_page);

                            foreach ($valid_entries_slice as $entry) {
                                $manga_id = $entry[0];
                                $chapter_id = $entry[1];
                                $last_read = $entry[2];

                                $post = get_post($manga_id);
                                $thumbnail = get_the_post_thumbnail_url($manga_id, 'manga_cover');
                                $manga_title = get_the_title($manga_id);

                                // Get chapter data
                                $chapter_data = $wpdb->get_row($wpdb->prepare(
                                    "SELECT chapter_name, chapter_slug 
                                    FROM {$wpdb->prefix}manga_chapters 
                                    WHERE chapter_id = %d",
                                    $chapter_id
                                ));

                                // Get the latest chapter
                                $latest_chapter = $wpdb->get_var($wpdb->prepare(
                                    "SELECT chapter_name 
                                    FROM {$wpdb->prefix}manga_chapters 
                                    WHERE post_id = %d 
                                    AND chapter_status != 3
                                    ORDER BY CAST(chapter_name AS DECIMAL(10,2)) DESC 
                                    LIMIT 1",
                                    $manga_id
                                ));

                                if ($chapter_data) {
                                    $chapter_name = $chapter_data->chapter_name;
                                    $current_chapter = floatval($chapter_name);
                                    $latest_chapter_num = floatval($latest_chapter);
                                    $progress_percentage = ($latest_chapter_num > 0) ? ($current_chapter / $latest_chapter_num) * 100 : 0;
                                    ?>
                                    <div class="unit">
                                        <div class="inner">
                                            <button class="reading-remove" data-manga-id="<?php echo esc_attr($manga_id); ?>" data-chapter-id="<?php echo esc_attr($chapter_id); ?>">
                                                <i class="fa-solid fa-xmark"></i>
                                            </button>
                                            <a href="<?php echo esc_url(get_chapter_link($chapter_id)); ?>">
                                                <div class="hposter">
                                                    <div class="poster-image-wrapper">
                                                        <?php
                                                        // Get comic type and flag
                                                        $types = wp_get_post_terms($manga_id, 'wp-manga-type');
                                                        $comic_type = !empty($types) ? $types[0]->name : '';
                                                        $flag_path = get_stylesheet_directory_uri() . '/assets/images/flags/';
                                                        
                                                        // Get flag image path
                                                        $type_to_flag = [
                                                            'manga'  => 'jp.svg',
                                                            'manhwa' => 'kr.svg',
                                                            'manhua' => 'cn.svg'
                                                        ];
                                                        $flag_image = isset($type_to_flag[strtolower($comic_type)]) 
                                                            ? $flag_path . $type_to_flag[strtolower($comic_type)]
                                                            : '';
                                                        
                                                        if($flag_image): ?>
                                                        <div class="manga-flag width-limit" data-type="<?php echo esc_attr(strtolower($comic_type)); ?>">
                                                            <img src="<?php echo esc_url($flag_image); ?>" alt="<?php echo esc_attr($comic_type); ?>" title="<?php echo esc_attr($comic_type); ?>" class="flag-icon">
                                                        </div>
                                                        <?php endif; ?>
                                                        <div><img src="<?php echo esc_url($thumbnail); ?>" alt="<?php echo esc_attr($manga_title); ?>" /></div>
                                                    </div>
                                                </div>
                                                <div class="info">
                                                    <div><?php echo esc_html($manga_title); ?></div>
                                                    <div class="last-read">
                                                        <i class="fa-regular fa-clock"></i>
                                                        <span><?php 
                                                            // Convert the stored datetime to timestamp
                                                            $last_read_timestamp = strtotime($last_read);
                                                            $current_timestamp = current_time('timestamp');
                                                            
                                                            // Calculate time difference
                                                            $diff = $current_timestamp - $last_read_timestamp;
                                                            
                                                            if ($diff < 60) {
                                                                echo __('A l\'instant', 'child-mfire');
                                                            } elseif ($diff < 3600) {
                                                                $mins = floor($diff / 60);
                                                                echo sprintf(_n('il y a %d minute', 'il y a %d minutes', $mins, 'child-mfire'), $mins);
                                                            } elseif ($diff < 86400) {
                                                                $hours = floor($diff / 3600);
                                                                echo sprintf(_n('il y a %d heure', 'il y a %d heures', $hours, 'child-mfire'), $hours);
                                                            } elseif ($diff < 604800) { // 7 days
                                                                $days = floor($diff / 86400);
                                                                echo sprintf(_n('il y a %d jour', 'il y a %d jours', $days, 'child-mfire'), $days);
                                                            } else {
                                                                echo date_i18n(get_option('date_format'), $last_read_timestamp);
                                                            }
                                                        ?></span>
                                                    </div>
                                                    <div class="manga-progress">
                                                        <div class="progress-text">
                                                            <span class="current">Ch. <?php echo esc_html($chapter_name); ?></span>
                                                            <span>Ch. <?php echo esc_html($latest_chapter); ?></span>
                                                        </div>
                                                        <div class="progress-bar">
                                                            <div class="progress-fill" style="width: <?php echo esc_attr($progress_percentage); ?>%"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <?php
                                }
                            }
                        }

                        if (empty($valid_entries_slice)) {
                            // Remove this message since we'll handle it below
                        }
                        ?>
                    </div>

                    <?php
                    // Initialize pagination variables with defaults
                    $total_entries = count($valid_entries ?? []);
                    $per_page = 10;
                    $total_pages = max(1, ceil($total_entries / $per_page));
                    $current_page = get_query_var('paged') ? get_query_var('paged') : 1;
                    $current_page = max(1, min($current_page, $total_pages));

                    // Single check for empty reading history
                    if (empty($valid_entries)) {
                        echo '<p>' . __('Vous n\'avez pas encore lu de chapitres', 'child-mfire') . '</p>';
                    } else {
                        ?>
                        <nav class="navigation">
                            <ul class="pagination">
                                <li class="page-item <?php echo $current_page <= 1 ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="<?php echo $current_page <= 1 ? '#' : site_url('/user/page/1/?tab=reading'); ?>" rel="first">«</a>
                                </li>
                                <li class="page-item <?php echo $current_page <= 1 ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="<?php echo $current_page <= 1 ? '#' : site_url('/user/page/' . max(1, $current_page - 1) . '/?tab=reading'); ?>" rel="prev" aria-label="« Previous">‹</a>
                                </li>

                                <?php
                                // Calculate the start and end for the pagination display
                                $start_page = max(1, $current_page - 2);
                                $end_page = min($total_pages, $current_page + 2);

                                // Adjust the start and end to ensure we always show 5 pages if possible
                                if ($end_page - $start_page < 4) {
                                    if ($start_page == 1) {
                                        $end_page = min(5, $total_pages);
                                    } elseif ($end_page == $total_pages) {
                                        $start_page = max(1, $total_pages - 4);
                                    }
                                }

                                // Loop to display the page numbers
                                for ($i = $start_page; $i <= $end_page; $i++) {
                                    if ($i == $current_page) {
                                        echo '<li class="page-item active" aria-current="page"><span class="page-link">' . esc_html($i) . '</span></li>';
                                    } else {
                                        echo '<li class="page-item"><a class="page-link" href="' . site_url('/user/page/' . $i . '/?tab=reading') . '">' . esc_html($i) . '</a></li>';
                                    }
                                }
                                ?>

                                <li class="page-item <?php echo $current_page >= $total_pages ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="<?php echo $current_page >= $total_pages ? '#' : site_url('/user/page/' . min($total_pages, $current_page + 1) . '/?tab=reading'); ?>" rel="next" aria-label="Next »">›</a>
                                </li>
                                <li class="page-item <?php echo $current_page >= $total_pages ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="<?php echo $current_page >= $total_pages ? '#' : site_url('/user/page/' . $total_pages . '/?tab=reading'); ?>" rel="last">»</a>
                                </li>
                            </ul>
                        </nav>
                    <?php } ?>
                </section>
            </aside>
        </div>
    </div>
</main>