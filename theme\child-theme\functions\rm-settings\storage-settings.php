<?php
function storage_settings_callback() { 

    // Save the selected option
    if (isset($_POST['manga_storage_type']) && check_admin_referer('manga_storage_settings_save')) {
        $selected_option = sanitize_text_field($_POST['manga_storage_type']);
        update_option('manga_storage_type', $selected_option);

        // Save FTP settings if the external server is selected
        if ($selected_option === 'external-ftp-server') {
            update_option('ftp_host', sanitize_text_field($_POST['ftp_host']));
            update_option('ftp_username', sanitize_text_field($_POST['ftp_username']));
            update_option('ftp_password', sanitize_text_field($_POST['ftp_password']));
            update_option('ftp_port', sanitize_text_field($_POST['ftp_port']));
            update_option('ftp_domain', sanitize_text_field($_POST['ftp_domain']));
            update_option('ftp_storage_path', sanitize_text_field($_POST['ftp_storage_path']));
        }

        echo '<div class="updated"><p>Storage option saved successfully.</p></div>';
    }

    // Get the current storage option
    $current_option = get_option('manga_storage_type', 'local'); // Default to 'local'

    // Get FTP settings if the external server is selected
    $ftp_host = get_option('ftp_host', '');
    $ftp_username = get_option('ftp_username', '');
    $ftp_password = get_option('ftp_password', '');
    $ftp_port = get_option('ftp_port', '21'); // Default FTP port
    $ftp_domain = get_option('ftp_domain', '');
    $ftp_storage_path = get_option('ftp_storage_path', '/'); // Default storage path

    ?>
    <div class="wrap">
        <h1>Manga Storage Settings</h1>
        <form method="post" action="">
            <?php wp_nonce_field('manga_storage_settings_save'); ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><label for="manga_storage_type">Select Storage Type</label></th>
                    <td>
                        <select id="manga_storage_type" name="manga_storage_type" onchange="toggleFtpFields(this.value)">
                            <option value="local" <?php selected($current_option, 'local'); ?>>Local</option>
                            <option value="external-ftp-server" <?php selected($current_option, 'external-ftp-server'); ?>>External FTP Server</option>
                        </select>
                    </td>
                </tr>
            </table>

            <div id="ftp-settings" style="display: <?php echo $current_option === 'external-ftp-server' ? 'block' : 'none'; ?>;">
                <h2>FTP Settings</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="ftp_host">Host</label></th>
                        <td><input type="text" id="ftp_host" name="ftp_host" value="<?php echo esc_attr($ftp_host); ?>" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="ftp_username">Username</label></th>
                        <td><input type="text" id="ftp_username" name="ftp_username" value="<?php echo esc_attr($ftp_username); ?>" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="ftp_password">Password</label></th>
                        <td><input type="password" id="ftp_password" name="ftp_password" value="<?php echo esc_attr($ftp_password); ?>" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="ftp_port">Port</label></th>
                        <td><input type="text" id="ftp_port" name="ftp_port" value="<?php echo esc_attr($ftp_port); ?>" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="ftp_domain">Domain</label></th>
                        <td><input type="text" id="ftp_domain" name="ftp_domain" value="<?php echo esc_attr($ftp_domain); ?>" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="ftp_storage_path">Storage Path</label></th>
                        <td><input type="text" id="ftp_storage_path" name="ftp_storage_path" value="<?php echo esc_attr($ftp_storage_path); ?>" class="regular-text"></td>
                    </tr>
                </table>
            </div>

            <p class="submit">
                <input type="submit" class="button-primary" value="Save Changes">
                <button type="button" class="button" id="test-connection">Test Connection</button>
            </p>
        </form>
        <div id="test-result" style="margin-top: 15px;"></div>
    </div>
    <script>
        function toggleFtpFields(value) {
            document.getElementById('ftp-settings').style.display = (value === 'external-ftp-server') ? 'block' : 'none';
        }

        document.getElementById('test-connection').addEventListener('click', function() {
            const host = document.getElementById('ftp_host').value;
            const username = document.getElementById('ftp_username').value;
            const password = document.getElementById('ftp_password').value;
            const port = document.getElementById('ftp_port').value;

            const data = {
                action: 'test_ftp_connection',
                host: host,
                username: username,
                password: password,
                port: port
            };

            // Clear previous result
            document.getElementById('test-result').innerHTML = '';

            jQuery.post(ajaxurl, data, function(response) {
                if (response.success) {
                    document.getElementById('test-result').innerHTML = '<span style="color: green;">' + response.data + '</span>';
                } else {
                    document.getElementById('test-result').innerHTML = '<span style="color: red;">' + response.data + '</span>';
                }
            }).fail(function() {
                document.getElementById('test-result').innerHTML = '<span style="color: red;">An error occurred while testing the connection. Please try again.</span>';
            });
        });
    </script>

<?php } 

// AJAX handler to test FTP connection
function test_ftp_connection() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Unauthorized access.');
    }

    $host = sanitize_text_field($_POST['host']);
    $username = sanitize_text_field($_POST['username']);
    $password = sanitize_text_field($_POST['password']);
    $port = intval($_POST['port']);
    
    // Create a unique folder name
    $unique_id = uniqid('test_', true);
    $ftp_path = '/' . $unique_id;

    // Establish an FTP connection
    $conn_id = @ftp_connect($host, $port);
    if ($conn_id === false) {
        wp_send_json_error('Could not connect to the FTP server. Please check the host and port.');
    }

    // Login to FTP server
    if (@ftp_login($conn_id, $username, $password)) {
        // Create a directory on the FTP server
        if (@ftp_mkdir($conn_id, $unique_id)) {
            // Comment out or remove this line to keep the directory
            // ftp_rmdir($conn_id, $unique_id); // Remove the test directory after creating it
            ftp_close($conn_id); // Close the connection
            wp_send_json_success('Connection successful! Test directory created: ' . $ftp_path);
        } else {
            ftp_close($conn_id);
            wp_send_json_error('Could not create test directory. Check your permissions.');
        }
    } else {
        ftp_close($conn_id);
        wp_send_json_error('Login failed. Please check your username and password.');
    }
}
add_action('wp_ajax_test_ftp_connection', 'test_ftp_connection');