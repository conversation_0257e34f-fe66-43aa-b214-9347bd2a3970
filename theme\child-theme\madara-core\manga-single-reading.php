<?php 
require_once __DIR__ . '/../functions/cached-functions.php';
global $wp_manga_functions, $wp_manga, $wpdb;
$reading_chapter = madara_permalink_reading_chapter();
$manga_id = get_the_ID();
$chapter_id = isset($reading_chapter['chapter_id']) ? esc_attr($reading_chapter['chapter_id']) : '';
$modified = get_post_field('post_modified', $manga_id);
$cache_key = 'manga_reading_' . $manga_id . '_' . $chapter_id . '_' . strtotime($modified);
$cached = manga_cache_get($cache_key);
if ($cached !== false) {
    echo $cached;
    return;
}
ob_start();
?>
<?php 

function output_chapter_list($chapters_output) {
    ?>
    <div class="chapter-list" style="display: none; position: absolute; top: 100%; margin-top: 8px;">
        <div class="chapter-search" style="padding: 10px;">
            <div style="position: relative;">
                <i class="fa-light fa-magnifying-glass"></i>
                <input type="text" 
                       id="chapterSearch" 
                       placeholder="Trouver un chapitre...">
            </div>
        </div>
        <ul>
            <?php if (!empty($chapters_output)): ?>
                <?php foreach ($chapters_output as $chapter): ?>
                    <li class="chapter-item">
                        <a href="<?php echo $chapter['url']; ?>" 
                        data-number="<?php echo $chapter['number']; ?>" 
                        data-id="<?php echo $chapter['id']; ?>" 
                        title="<?php echo $chapter['title']; ?>" 
                        <?php echo $chapter['active']; ?>>
                            Chapitre <?php echo $chapter['number'] . ' ' . $chapter['title']; ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            <?php else: ?>
                <li>Aucun chapitre disponible</li>
            <?php endif; ?>
        </ul>
    </div>
    <?php
}

header('X-Content-Type-Options: nosniff');
header('Link: </wp-content/themes/raijin-child/style.css>; rel=preload; as=style');

global $wp_manga_functions, $wp_manga, $wpdb; // Use the WordPress database global

// Get the current chapter
$reading_chapter = madara_permalink_reading_chapter();
if ($reading_chapter) {
    $cur_chap_slug = $reading_chapter['chapter_slug'];
}

// Get current manga and chapter ID
$manga_id = get_the_ID();
$chapter_id = esc_attr($reading_chapter['chapter_id']);

// Optimize database queries by combining them
$table_name = $wpdb->prefix . 'manga_chapters';
$chapter_data = $wpdb->get_row($wpdb->prepare(
    "SELECT chapter_status, COALESCE(views, 0) as views 
     FROM $table_name 
     WHERE chapter_id = %d",
    $chapter_id
));

// Check chapter status
if ($chapter_data->chapter_status == 3 && !current_user_can('administrator') && !current_user_can('editor')) {
    nocache_headers();
    include(get_query_template('404'));
    exit;
}

// Update views in a single query without checking first
$wpdb->query($wpdb->prepare(
    "UPDATE $table_name SET views = views + 1 WHERE chapter_id = %d",
    $chapter_id
));

$cur_host = isset($_GET['host']) ? $_GET['host'] : null;
$current_vol_all_chaps = $wp_manga_functions->get_all_chapters($manga_id);
$next_chap = null;
$prev_chap = null;
$chapters = $current_vol_all_chaps[0]['chapters'] ?? [];

// Filter out chapters with status 3
$filtered_chapters = array_filter($chapters, function($chapter) {
    return $chapter['chapter_status'] != 3; // Exclude chapters with status 3
});

$filtered_chapters = array_values($filtered_chapters); // Re-index the array

// Find next and previous chapters
foreach ($filtered_chapters as $index => $chapter) {
    if ($chapter['chapter_slug'] === $cur_chap_slug) {
        $next_chap = ($index > 0) ? $filtered_chapters[$index - 1] : null;
        $prev_chap = ($index < count($filtered_chapters) - 1) ? $filtered_chapters[$index + 1] : null;
        break;
    }
}

$chapters_output = [];
if (!empty($current_vol_all_chaps)) {
    foreach ($filtered_chapters as $chapter) {
        $chapters_output[] = [
            'url' => esc_url($wp_manga_functions->build_chapter_url(get_the_ID(), $chapter['chapter_slug'] ?? '')),
            'number' => esc_attr($chapter['chapter_name'] ?? 'Unnamed Chapter'),
            'id' => esc_attr($chapter['chapter_id'] ?? 'N/A'),
            'title' => esc_attr($chapter['chapter_name_extend'] ?? 'Unnamed Chapter Title'),
            'active' => ($chapter['chapter_slug'] ?? '') === $cur_chap_slug ? 'class="active"' : ''
        ];
    }
}

$ts_id = isset($reading_chapter['user_id']) ? $reading_chapter['user_id'] : '0';
$cur_chap = $reading_chapter['chapter_slug'];

// Retrieve existing reading history
$reading_history = get_user_meta(get_current_user_id(), 'reading_history', true);

// Initialize reading history if not set
if (!$reading_history) {
    $reading_history = [];
}

// Remove any existing entry for the current manga
foreach ($reading_history as $key => $entry) {
    if ($entry[0] == $manga_id) {
        unset($reading_history[$key]); // Remove the existing entry
        break;
    }
}

// Add the current manga and chapter to the top of the history
$reading_history = array_values($reading_history); // Re-index the array after unsetting
$reading_history[] = [$manga_id, $chapter_id, current_time('mysql')];

// Store the updated reading history back to user meta
update_user_meta(get_current_user_id(), 'reading_history', $reading_history);

// Add proper SEO title and meta tags
add_filter('pre_get_document_title', function($title) {
    $reading_chapter = madara_permalink_reading_chapter();
    if (!$reading_chapter) return $title;
    
    $manga_title = get_the_title();
    $chapter_name = $reading_chapter['chapter_name'];
    $chapter_extend = !empty($reading_chapter['chapter_name_extend']) 
        ? ' - ' . $reading_chapter['chapter_name_extend'] 
        : '';
    $site_name = get_bloginfo('name');
    
    // SEO-optimized title format
    return sprintf(
        '%s Chapter %s%s | Read Manga Online | %s',
        esc_html($manga_title),
        esc_html($chapter_name),
        esc_html($chapter_extend),
        esc_html($site_name)
    );
}, 999);

// Enhanced meta tags for better SEO
add_action('wp_head', function() {
    $reading_chapter = madara_permalink_reading_chapter();
    if (!$reading_chapter) return;
    
    $manga_title = get_the_title();
    $chapter_name = $reading_chapter['chapter_name'];
    $chapter_extend = !empty($reading_chapter['chapter_name_extend']) 
        ? ' - ' . $reading_chapter['chapter_name_extend'] 
        : '';
    $site_name = get_bloginfo('name');
    
    // Generate meta description without author
    $description = sprintf(
        'Read %s Chapter %s%s online for free. Latest chapter updated with high-quality pages. Experience the best manga reading experience.',
        esc_attr($manga_title),
        esc_attr($chapter_name),
        esc_attr($chapter_extend)
    );
    
    // Output enhanced meta tags without author information
    ?>
    <meta name="description" content="<?php echo $description; ?>">
    <meta name="keywords" content="<?php echo esc_attr($manga_title); ?> Chapter <?php echo esc_attr($chapter_name); ?>, read manga, manga online, free manga">
    
    <!-- Open Graph Tags -->
    <meta property="og:title" content="<?php echo esc_attr($manga_title); ?> Chapter <?php echo esc_attr($chapter_name); ?>">
    <meta property="og:description" content="<?php echo esc_attr($description); ?>">
    <meta property="og:type" content="article">
    <meta property="og:site_name" content="<?php echo esc_attr($site_name); ?>">
    <meta property="og:url" content="<?php echo esc_url($_SERVER['REQUEST_URI']); ?>">
    
    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="<?php echo esc_attr($manga_title); ?> Chapter <?php echo esc_attr($chapter_name); ?>">
    <meta name="twitter:description" content="<?php echo esc_attr($description); ?>">
    
    <!-- Additional SEO Meta -->
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo esc_url($_SERVER['REQUEST_URI']); ?>">
    <?php
}, 1);

?>
<!-- Move viewport meta tag before wp_head() -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="preconnect" href="<?php echo site_url(); ?>">
<link rel="dns-prefetch" href="<?php echo site_url(); ?>">
<?php 
// Add async loading for non-critical scripts
add_filter('script_loader_tag', function($tag, $handle) {
    if (!is_admin()) {
        // Add defer to non-critical scripts
        if (strpos($handle, 'jquery') === false) {
            return str_replace(' src', ' defer src', $tag);
        }
    }
    return $tag;
}, 10, 2);

wp_head(); 
?>
<body class="read ctrl-menu-active keyboard-navigate" chapter-id="<?php echo esc_html($reading_chapter['chapter_id']); ?>">
    <div class="wrapper">
        <main>
            <div class="m-content">
                <!-- Reading Content - Load first -->
                <div id="page-wrapper-x">
                    <?php get_header(); ?>
                    <div class="reading-content">
                        <!-- Top Navigation Bar with all controls -->
                        <div class="chapter-navigation-bar" id="page-header">
                            <div class="nav-header">
                                <a href="<?php the_permalink(); ?>" class="manga-title"><?php the_title(); ?></a>
                            </div>

                            <div class="nav-controls">
                                <!-- Chapter Navigation -->
                                <div class="chapter-buttons" style="position: relative;">
                                    <button id="number-go-right" 
                                    <?php if ($prev_chap): ?>
                                        onclick="window.location.href='<?php echo esc_url($wp_manga_functions->build_chapter_url($manga_id, $prev_chap['chapter_slug'])); ?>'"
                                    <?php else: ?>
                                        disabled
                                    <?php endif; ?>>
                                        <i class="fa-regular fa-chevron-left"></i> Précédent
                                    </button>
                                    <button class="number-toggler">
                                        <b class="current-type-number text-title">Chapitre <?php echo esc_html($reading_chapter['chapter_name']); ?></b>
                                        <i class="fa-solid fa-sort fa-sm"></i>
                                    </button>
                                    <button id="number-go-left" 
                                    <?php if ($next_chap): ?>
                                        onclick="window.location.href='<?php echo esc_url($wp_manga_functions->build_chapter_url($manga_id, $next_chap['chapter_slug'])); ?>'"
                                    <?php else: ?>
                                        disabled
                                    <?php endif; ?>>
                                        Suivant <i class="fa-regular fa-chevron-right"></i>
                                    </button>
                                    
                                    <!-- Chapter List -->
                                    <?php output_chapter_list($chapters_output); ?>
                                </div>
                            </div>

                            <?php if ($ts_id): ?>
                                <div class="translator-info">
                                    <span>Traducteur:</span>
                                    <b><?php echo get_the_author_meta('display_name', $ts_id); ?></b>
                                    <?php echo get_avatar($ts_id, 30, '', '', array('loading' => 'lazy')); ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Chapter Images -->
                        <div id="ch-images" style="contain: content;">
                            <div id="chapter-content">
                                <?php 
                                add_filter('wp_manga_chapter_image_html', function($html) {
                                    return str_replace('<img', '<img loading="lazy" decoding="async"', $html);
                                });
                                do_action('wp_manga_chapter_content', $cur_chap, $manga_id);
                                ?>
                            </div>
                        </div>
                        <!-- Bottom Navigation Bar -->
                        <div class="chapter-navigation-bar">
                            <div class="nav-controls">
                                <!-- Chapter Navigation -->
                                <div class="chapter-buttons" style="position: relative;">
                                    <button id="number-go-right" 
                                    <?php if ($prev_chap): ?>
                                        onclick="window.location.href='<?php echo esc_url($wp_manga_functions->build_chapter_url($manga_id, $prev_chap['chapter_slug'])); ?>'"
                                    <?php else: ?>
                                        disabled
                                    <?php endif; ?>>
                                        <i class="fa-regular fa-chevron-left"></i> Précédent
                                    </button>
                                    <button class="number-toggler">
                                        <b class="current-type-number text-title">Chapitre <?php echo esc_html($reading_chapter['chapter_name']); ?></b>
                                        <i class="fa-solid fa-sort fa-sm"></i>
                                    </button>
                                    <button id="number-go-left" 
                                    <?php if ($next_chap): ?>
                                        onclick="window.location.href='<?php echo esc_url($wp_manga_functions->build_chapter_url($manga_id, $next_chap['chapter_slug'])); ?>'"
                                    <?php else: ?>
                                        disabled
                                    <?php endif; ?>>
                                        Suivant <i class="fa-regular fa-chevron-right"></i>
                                    </button>
                                    
                                    <!-- Chapter List -->
                                    <?php output_chapter_list($chapters_output); ?>
                                </div>

                                <div class="ch-settings">
                                    <?php if( get_option('enable_chapter_reports', 1) ) : ?>
                                        <button class="jb-btn" data-toggle="modal" data-target="#report">
                                            <i class="fa-light fa-lg fa-triangle-exclamation"></i>
                                            <span>Signaler une erreur</span>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if ($ts_id): ?>
                                <div class="translator-info">
                                    <span>Traducteur :</span>
                                    <b><?php echo get_the_author_meta('display_name', $ts_id); ?></b>
                                    <?php echo get_avatar($ts_id, 30, '', '', array('loading' => 'lazy')); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                         <!-- Comments Section -->
                        <div class="comments-container">
                            <?php do_action('wp_manga_discussion'); ?>
                        </div>
                        <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            // Clear local storage if it's nearly full
                            try {
                                if (localStorage.length > 0) {
                                    localStorage.clear();
                                }
                            } catch (e) {
                                console.warn('Storage clear failed:', e);
                            }

                            // Ensure the main content area is focused for keyboard scrolling
                            setTimeout(function() {
                                const pageWrapper = document.getElementById('page-wrapper-x');
                                if (pageWrapper) {
                                    pageWrapper.setAttribute('tabindex', '-1'); // Make it focusable
                                    pageWrapper.focus();
                                } else if (document.body) {
                                    // Fallback to body if pageWrapper is not found
                                    // Ensure body is focusable if it wasn't already
                                    if (document.body.getAttribute('tabindex') === null) {
                                        document.body.setAttribute('tabindex', '-1');
                                    }
                                    document.body.focus();
                                }
                            }, 100); // Small delay (100ms)

                            // Function to reload Disqus
                            function reloadDisqus() {
                                if (window.DISQUS) {
                                    DISQUS.reset({
                                        reload: true,
                                        config: function() {
                                            this.page.identifier = window.location.pathname;
                                            this.page.url = window.location.href;
                                            this.callbacks.onReady = [function() {
                                                const disqusFrame = document.getElementById('disqus_thread');
                                                if (disqusFrame) {
                                                    disqusFrame.style.isolation = 'isolate';
                                                    disqusFrame.style.zIndex = '1';
                                                }
                                            }];
                                        }
                                    });
                                }
                            }

                            // Monitor Disqus loading
                            let attempts = 0;
                            const maxAttempts = 3;
                            
                            const checkDisqus = setInterval(function() {
                                const disqusFrame = document.getElementById('disqus_thread');
                                if (disqusFrame) {
                                    const iframe = disqusFrame.querySelector('iframe');
                                    
                                    if (!iframe || !iframe.contentWindow) {
                                        attempts++;
                                        if (attempts >= maxAttempts) {
                                            clearInterval(checkDisqus);
                                            // Force reload Disqus after max attempts
                                            reloadDisqus();
                                        }
                                        return;
                                    }
                                    
                                    clearInterval(checkDisqus);
                                    
                                    // Setup focus management
                                    disqusFrame.addEventListener('focusin', function(e) {
                                        e.stopPropagation();
                                        if (iframe) {
                                            iframe.style.pointerEvents = 'auto';
                                        }
                                    }, true);

                                    // Handle clicks outside
                                    document.addEventListener('mousedown', function(e) {
                                        if (!disqusFrame.contains(e.target)) {
                                            if (iframe && iframe.contentWindow.document.activeElement) {
                                                e.preventDefault();
                                            }
                                        }
                                    }, true);

                                    // Add reload button (hidden by default)
                                    const reloadButton = document.createElement('button');
                                    reloadButton.innerHTML = 'Reload Comments';
                                    reloadButton.style.display = 'none';
                                    reloadButton.className = 'disqus-reload-btn';
                                    reloadButton.onclick = reloadDisqus;
                                    disqusFrame.parentNode.insertBefore(reloadButton, disqusFrame);

                                    // Show reload button if Disqus fails to load properly
                                    setTimeout(() => {
                                        if (!iframe || !iframe.contentWindow || !window.DISQUS) {
                                            reloadButton.style.display = 'block';
                                        }
                                    }, 5000);
                                }
                            }, 1000);

                            // Handle page visibility changes
                            document.addEventListener('visibilitychange', function() {
                                if (document.visibilityState === 'visible') {
                                    const disqusFrame = document.getElementById('disqus_thread');
                                    if (disqusFrame && (!disqusFrame.querySelector('iframe') || !window.DISQUS)) {
                                        reloadDisqus();
                                    }
                                }
                            });

                            // Force reload on network status change
                            window.addEventListener('online', function() {
                                setTimeout(reloadDisqus, 1000);
                            });
                        });
                        </script>

                        <style>
                            .disqus-reload-btn {
                                display: none;
                                margin: 10px auto;
                                padding: 8px 16px;
                                background: #2e9fff;
                                color: white;
                                border: none;
                                border-radius: 4px;
                                cursor: pointer;
                            }
                            .disqus-reload-btn:hover {
                                background: #2680d9;
                            }
                            #disqus_thread {
                                position: relative;
                                z-index: 1;
                                isolation: isolate;
                                min-height: 100px;
                            }
                        </style>
                    <?php get_footer(); ?>
                </div>
            </div>
        </main>
    </div>
    <div class="progress-group">
    <div class="progress-wrap">
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
        </svg>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const progressWrap = document.querySelector('.progress-wrap');
    const progressPath = document.querySelector('.progress-wrap path');
    const pageWrapper = document.getElementById('page-wrapper-x');
    
    if (!progressWrap || !progressPath) {
        console.error('Progress elements not found');
        return;
    }

    const pathLength = progressPath.getTotalLength();
    
    // Initialize path properties
    progressPath.style.transition = 'none';
    progressPath.style.strokeDasharray = pathLength + ' ' + pathLength;
    progressPath.style.strokeDashoffset = pathLength;
    progressPath.getBoundingClientRect();
    progressPath.style.transition = 'stroke-dashoffset 10ms linear';

    const updateProgress = () => {
        // Get scroll positions from both window and page wrapper
        const windowScroll = window.scrollY || document.documentElement.scrollTop;
        const wrapperScroll = pageWrapper ? pageWrapper.scrollTop : 0;
        const scroll = Math.max(windowScroll, wrapperScroll);
        
        // Calculate total scrollable height
        const windowHeight = document.documentElement.scrollHeight - window.innerHeight;
        const wrapperHeight = pageWrapper ? pageWrapper.scrollHeight - pageWrapper.clientHeight : 0;
        const height = Math.max(windowHeight, wrapperHeight);
        
        // Calculate progress
        const progress = pathLength - (scroll * pathLength / height);
        progressPath.style.strokeDashoffset = progress;
        
        // Add/remove active class
        if (scroll > 50) {
            progressWrap.classList.add('active-progress');
        } else {
            progressWrap.classList.remove('active-progress');
        }
    }

    // Listen to both window and page wrapper scroll events
    window.addEventListener('scroll', updateProgress, { passive: true });
    if (pageWrapper) {
        pageWrapper.addEventListener('scroll', updateProgress, { passive: true });
    }
    
    // Force initial check
    setTimeout(updateProgress, 100);

    // Scroll to top functionality
    progressWrap.addEventListener('click', (e) => {
        e.preventDefault();
        if (pageWrapper) {
            pageWrapper.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
});
</script>
    <?php if ( get_option('enable_chapter_reports', 1) ) include get_stylesheet_directory() . '/template-parts/chapter-reading/report.php'; ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const numberTogglers = document.querySelectorAll('.number-toggler');
            
            numberTogglers.forEach(numberToggler => {
                const chapterList = numberToggler.parentElement.querySelector('.chapter-list');
                
                if (numberToggler && chapterList) {
                    numberToggler.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        // Toggle display
                        const isHidden = chapterList.style.display === 'none';
                        chapterList.style.display = isHidden ? 'block' : 'none';
                        
                        if (isHidden) {
                            chapterList.style.zIndex = '1000';
                            
                            // Calculate available space
                            const togglerRect = numberToggler.getBoundingClientRect();
                            const listHeight = chapterList.offsetHeight;
                            const spaceBelow = window.innerHeight - togglerRect.bottom;
                            const spaceAbove = togglerRect.top;
                            
                            // Position the list
                            if (spaceBelow < listHeight && spaceAbove > spaceBelow) {
                                // Show above if there's more space there
                                chapterList.style.top = 'auto';
                                chapterList.style.bottom = '100%';
                                chapterList.style.marginTop = '0';
                                chapterList.style.marginBottom = '8px';
                            } else {
                                // Show below
                                chapterList.style.top = '100%';
                                chapterList.style.bottom = 'auto';
                                chapterList.style.marginTop = '8px';
                                chapterList.style.marginBottom = '0';
                            }
                        }
                    });

                    document.addEventListener('click', function(e) {
                        if (!numberToggler.contains(e.target) && !chapterList.contains(e.target)) {
                            chapterList.style.display = 'none';
                        }
                    });
                }
            });

            const searchBoxes = document.querySelectorAll('#chapterSearch');
            
            searchBoxes.forEach(searchBox => {
                searchBox.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const chapterList = this.closest('.chapter-list').querySelectorAll('.chapter-item');
                    
                    chapterList.forEach(item => {
                        const text = item.textContent.toLowerCase();
                        if (text.includes(searchTerm)) {
                            item.style.display = '';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
    <?php 
$content = ob_get_clean();
manga_cache_set($cache_key, $content, 86400); // Cache for 24 hours
echo $content;