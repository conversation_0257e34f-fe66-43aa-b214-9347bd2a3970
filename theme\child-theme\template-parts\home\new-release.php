<section class="home-swiper">
  <div class="head">
    <h2>Latest Work</h2>
    <div class="tabs">
      <div class="s-pagi">
          <div class="complete-button-next" tabindex="0" role="button" aria-label="Next slide" aria-disabled="false">
            <i class="fa-solid fa-square-chevron-right fa-lg"></i>
          </div>
        <div class="complete-button-prev swiper-button-disabled" tabindex="-1" role="button" aria-label="Previous slide" aria-disabled="true">
          <i class="fa-solid fa-square-chevron-left fa-lg"></i>
        </div>
      </div>
    </div>
  </div>

 <div class="swiper-container">
 <div class="swiper swiper-nrs completed swiper-container-initialized swiper-container-horizontal">
 <div class="card-md swiper-wrapper">
 <?php
 $args = array(
 'post_type' => 'wp-manga',
 'posts_per_page' => 15,
 'orderby' => 'date',
 'order' => 'DESC'
 );

 // Check if the user wants to show adult manga
 $show_adult = isset($_COOKIE['show_adult']) && $_COOKIE['show_adult'] === '1';

 // Meta query for filtering adult manga
 $meta_query = array();
 if (!$show_adult) {
 $meta_query[] = array(
 'key' => 'manga_adult_content',
 'value' => 'yes',
 'compare' => 'NOT LIKE'
 );
 }

 // Add meta_query to query arguments if it's set
 if (!empty($meta_query)) {
 $args['meta_query'] = $meta_query;
 }

 $wp_manga_query = new WP_Query($args);

 if ($wp_manga_query->have_posts()) :
 while ($wp_manga_query->have_posts()) : $wp_manga_query->the_post();
 $manga_id = get_the_ID();
 $thumbnail = get_the_post_thumbnail_url($manga_id, 'manga_cover');
 $title = get_the_title();
 $link = get_permalink();
 ?>
 <div class="swiper-slide unit">
 <a href="<?php echo esc_url($link); ?>">
 <div class="poster">
 <div>
 <?php
 // Get comic type and flag
 $types = wp_get_post_terms($manga_id, 'wp-manga-type');
 $comic_type = !empty($types) ? $types[0]->name : '';
 $flag_path = get_stylesheet_directory_uri() . '/assets/images/flags/';
 
 // Get flag image path
 $type_to_flag = [
 'manga'  => 'jp.svg',
 'manhwa' => 'kr.svg',
 'manhua' => 'cn.svg'
 ];
 $flag_image = isset($type_to_flag[strtolower($comic_type)]) 
 ? $flag_path . $type_to_flag[strtolower($comic_type)]
 : '';
 
 if($flag_image): ?>
 <div class="manga-flag" data-type="<?php echo esc_attr(strtolower($comic_type)); ?>">
 <img src="<?php echo esc_url($flag_image); ?>" alt="<?php echo esc_attr($comic_type); ?>" title="<?php echo esc_attr($comic_type); ?>" class="flag-icon">
 </div>
 <?php endif; ?>
 <img src="<?php echo esc_url($thumbnail); ?>" alt="<?php echo esc_attr($title); ?>" />
 </div>
 </div>
 <div class="info">
 <div class="flags">
 <?php
 // Check if it's adult content
 $is_adult = get_post_meta($manga_id, 'manga_adult_content', true);
 if ($is_adult === 'yes') {
 echo '<span class="flag adult">18+</span>';
 }
 ?>
 </div>
 <span class="title"><?php echo esc_html($title); ?></span>
 <?php
 $genres = wp_get_post_terms($manga_id, 'wp-manga-genre');
 if (!empty($genres) && !is_wp_error($genres)) {
 echo '<div class="genres">';
 // Display only first 2 genres
 for ($i = 0; $i < min(2, count($genres)); $i++) {
 echo '<span class="genre">' . esc_html($genres[$i]->name) . '</span>';
 }
 echo '</div>';
 }
 ?>
 </div>
 </a>
 </div>
 <?php endwhile; wp_reset_postdata(); endif; ?>
 </div>
 <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
 </div>

 <div class="completed-pagination swiper-pagination-clickable swiper-pagination-bullets"></div>
 </div>
</section>