<?php
global $wpdb; // Access the WordPress database object

// Get the user's reading history
$reading_history = get_user_meta(get_current_user_id(), 'reading_history', true);

// Check if there are any chapters read
if ($reading_history):
 // Reverse the reading history array to show the latest first
 $reading_history = array_reverse($reading_history);

 // Initialize a counter for how many manga we will display
 $displayed_count = 0;
 ?>
 <div id="continue-reading">
 <section>
 <div class="head">
 <h2 class="head-t"><?php _e('Continuer à lire', 'child-mfire'); ?></h2>
 <a href="<?php echo site_url('/user/?tab=reading'); ?>"><?php _e('Voir tout', 'child-mfire'); ?> <i class="fa-solid fa-xs fa-arrow-right"></i></a>
 </div>
 <div class="original card-lg reading horizontal-scroll">
 <div class="scroll-content">

 <?php
 foreach ($reading_history as $entry) {
 // Extract manga ID and chapter ID
 $manga_id = $entry[0];
 $chapter_id = $entry[1];
 $last_read = isset($entry[2]) ? $entry[2] : current_time('timestamp');

 // Get post details
 $post = get_post($manga_id);

 // Check if the manga exists
 if (!$post) {
 continue; // Skip if the manga doesn't exist
 }

 // Check if the manga is adult content
 $adult_content_meta = get_post_meta($manga_id, 'manga_adult_content', true);
 $is_adult = is_array(maybe_unserialize($adult_content_meta)) && in_array('yes', maybe_unserialize($adult_content_meta));

 // If not showing adult content and the manga is adult, skip this entry
 if (!$show_adult && $is_adult) {
 continue;
 }

 // Query to get the chapter number from the '{$wpdb->prefix}manga_chapters' table
 $chapter_data = $wpdb->get_row($wpdb->prepare(
 "SELECT chapter_name, chapter_slug
 FROM {$wpdb->prefix}manga_chapters
 WHERE chapter_id = %d",
 $chapter_id
 ));

 //If chapter data is found
 if ($chapter_data) {
 $chapter_name = $chapter_data->chapter_name; //Chapter name
 $chapter_slug = $chapter_data->chapter_slug; // Chapter slug (for future use)

 // Get the latest chapter instead of total chapters count
 $latest_chapter = $wpdb->get_var($wpdb->prepare(
     "SELECT chapter_name 
     FROM {$wpdb->prefix}manga_chapters 
     WHERE post_id = %d 
     AND chapter_status != 3
     ORDER BY CAST(chapter_name AS DECIMAL(10,2)) DESC 
     LIMIT 1",
     $manga_id
 ));

 // Calculate progress percentage using latest chapter instead of total
 $current_chapter = floatval($chapter_name);
 $latest_chapter_num = floatval($latest_chapter);
 $progress_percentage = ($latest_chapter_num > 0) ? ($current_chapter / $latest_chapter_num) * 100 : 0;
 } else {
 continue; // Skip if chapter data is not found
 }

 // Only show up to 7 manga
 if ($displayed_count >= 7) {
   break;
 }

 $thumbnail = get_the_post_thumbnail_url($manga_id, 'manga_cover');
 $manga_title = get_the_title($manga_id); // Get post title
 $type = get_manga_type($manga_id);

 // Display the chapter info
 ?>
 <div class="unit">
    <div class="inner">
        <button class="reading-remove" data-manga-id="<?php echo esc_attr($manga_id); ?>" data-chapter-id="<?php echo esc_attr($chapter_id); ?>">
            <i class="fa-solid fa-xmark"></i>
        </button>
        <a href="<?php echo esc_url(get_chapter_link($chapter_id)); ?>">
            <div class="hposter">
                <div class="poster-image-wrapper shine-effect">
                    <?php
                    // Get comic type and flag
                    $types = wp_get_post_terms($manga_id, 'wp-manga-type');
                    $comic_type = !empty($types) ? $types[0]->name : '';
                    $flag_path = get_stylesheet_directory_uri() . '/assets/images/flags/';
                    
                    // Get flag image path
                    $type_to_flag = [
                        'manga'  => 'jp.svg',
                        'manhwa' => 'kr.svg',
                        'manhua' => 'cn.svg'
                    ];
                    $flag_image = isset($type_to_flag[strtolower($comic_type)]) 
                    ? $flag_path . $type_to_flag[strtolower($comic_type)]
                    : '';
                    
                    if($flag_image): ?>
                    <div class="manga-flag width-limit" data-type="<?php echo esc_attr(strtolower($comic_type)); ?>">
                        <img src="<?php echo esc_url($flag_image); ?>" alt="<?php echo esc_attr($comic_type); ?>" title="<?php echo esc_attr($comic_type); ?>" class="flag-icon">
                    </div>
                    <?php endif; ?>
                    <div><img src="<?php echo esc_url($thumbnail); ?>" alt="<?php echo esc_attr($manga_title); ?>" /></div>
                </div>
            </div>
            <div class="info">
                <div><?php echo esc_html($manga_title); ?></div>
                <div class="last-read">
                    <i class="fa-regular fa-clock"></i>
                    <span><?php 
                        $last_read = isset($entry[2]) ? $entry[2] : date('Y-m-d H:i:s');
                        
                        // Convert the stored datetime to timestamp
                        $last_read_timestamp = strtotime($last_read);
                        $current_timestamp = current_time('timestamp');
                        
                        // Calculate time difference
                        $diff = $current_timestamp - $last_read_timestamp;
                        
                        if ($diff < 60) {
                            echo __('A l\'instant', 'child-mfire');
                        } elseif ($diff < 3600) {
                            $mins = floor($diff / 60);
                            echo sprintf(_n('il y a %d minute', 'il y a %d minutes', $mins, 'child-mfire'), $mins);
                        } elseif ($diff < 86400) {
                            $hours = floor($diff / 3600);
                            echo sprintf(_n('il y a %d heure', 'il y a %d heures', $hours, 'child-mfire'), $hours);
                        } elseif ($diff < 604800) { // 7 days
                            $days = floor($diff / 86400);
                            echo sprintf(_n('il y a %d jour', 'il y a %d jours', $days, 'child-mfire'), $days);
                        } else {
                            echo date_i18n(get_option('date_format'), $last_read_timestamp);
                        }
                    ?></span>
                </div>
                <div class="manga-progress">
                    <div class="progress-text">
                        <span class="current">Ch. <?php echo esc_html($chapter_name); ?></span>
                        <span>Ch. <?php echo esc_html($latest_chapter); ?></span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?php echo esc_attr($progress_percentage); ?>%; -webkit-transform: translateX(0); transform: translateX(0);"></div>
                    </div>
                </div>
            </div>
        </a>
    </div>
</div>
 <?php

 // Increment the count of displayed manga 
 $displayed_count++;
 }
 ?>
 </div>
 </div>
 </section>
 </div>
<?php endif; ?>