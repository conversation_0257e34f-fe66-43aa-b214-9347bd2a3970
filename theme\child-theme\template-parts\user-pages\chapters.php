<?php if( get_option('allow_post', 1) ) : ?>

<main class="user-panel">
    <div class="container">
        <div class="main-inner">
            <aside class="sidebar">
                <ul class="user-nav">
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=account-settings'); ?>">
                            <i class="fa-solid fa-user"></i> <span>Account Settings</span>
                        </a>
                    </li>
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=bookmark'); ?>">
                            <i class="fa-solid fa-bookmark"></i> <span>Favorites</span>
                        </a>
                    </li>

                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=add-manga'); ?>">
                            <i class="fa-solid fa-circle-plus"></i> <span>Add Manga</span>
                        </a>
                    </li>
                    <li>
                        <a class="ative" href="<?php echo site_url('/user/?tab=chapters'); ?>">
                            <i class="fa-solid fa-book-open-reader"></i> <span>Chapters List</span>
                        </a>
                    </li>

                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=reading'); ?>">
                            <i class="fa-solid fa-clock-rotate-left"></i> <span>Reading History</span>
                        </a>
                    </li>
                </ul>
            </aside>

            <aside class="content">
                <section class="up-chts">
                    <div class="head">
                        <h2>Your Releases</h2>
                    </div>
                    <div class="original card-lg reading">

                        <?php
                        global $wpdb;
                        $user_id = get_current_user_id(); // Get current user ID
                        
                        // Pagination setup
                        $chapters_per_page = 24;
                        $current_page = max(1, get_query_var('paged', 1)); // Get the current page from WordPress query variable
                        $offset = ($current_page - 1) * $chapters_per_page; // Calculate offset for SQL query

                        // Fetch user uploaded chapters with published post status
                        $uploaded_chapters = $wpdb->get_results(
                            $wpdb->prepare(
                                "SELECT ch.* FROM {$wpdb->prefix}manga_chapters AS ch
                                 INNER JOIN {$wpdb->prefix}posts AS p ON ch.post_id = p.ID
                                 WHERE ch.user_id = %d AND p.post_status = 'publish'
                                 ORDER BY ch.chapter_id DESC LIMIT %d OFFSET %d",
                                $user_id,
                                $chapters_per_page,
                                $offset
                            )
                        );

                        // Get total number of published chapters for pagination
                        $total_chapters = $wpdb->get_var(
                            $wpdb->prepare(
                                "SELECT COUNT(*) FROM {$wpdb->prefix}manga_chapters AS ch
                                 INNER JOIN {$wpdb->prefix}posts AS p ON ch.post_id = p.ID
                                 WHERE ch.user_id = %d AND p.post_status = 'publish'",
                                $user_id
                            )
                        );

                        // Check if chapters exist
                        if ($uploaded_chapters) {
                            foreach ($uploaded_chapters as $chapter) {
                                // Get the post title and thumbnail using post_id                                
                                $manga = get_post($chapter->post_id);
                                $thumbnail = get_the_post_thumbnail_url($manga, 'manga_cover');

                                // Determine the class based on chapter_status
                                $unit_class = 'unit';
                                if ($chapter->chapter_status == '3') {
                                    $unit_class .= ' private'; // Add 'private' class if chapter_status is '3'
                                }
                                ?>

                                <a href="<?php echo get_chapter_link($chapter->chapter_id); ?>" class="<?php echo esc_attr($unit_class); ?>">
                                    <div class="inner">
                                        <div class="poster">
                                            <div><img src="<?php echo esc_url($thumbnail); ?>" alt="<?php echo esc_attr($manga->post_title); ?>"></div>
                                        </div>
                                        <div class="info">
                                            <span class="type"><?php echo get_manga_type($manga->ID); ?></span>
                                            <div><?php echo esc_html($manga->post_title); ?></div>
                                            <p>Chapter <?php echo esc_html($chapter->chapter_name); ?></p>
                                        </div>
                                    </div>
                                </a>

                                <?php
                            }
                        } else { ?>

                            <p>You haven't uploaded any chapters yet</p><br>
                            <p>You can upload chapters from the manga page</p>
                            <p>All your releases will appear here</p>

                        <?php } ?>

                    </div>

                    <!-- Pagination Logic -->
                    <nav class="navigation">
                        <ul class="pagination">
                            <?php
                            $total_pages = ceil($total_chapters / $chapters_per_page); // Calculate total pages

                            // First Page Link
                            if ($current_page > 1) {
                                echo '<li class="page-item"><a class="page-link" href="?tab=chapters&paged=1" rel="first">«</a></li>';
                            } else {
                                echo '<li class="page-item disabled"><span class="page-link">«</span></li>';
                            }

                            // Previous Page Link
                            if ($current_page > 1) {
                                echo '<li class="page-item"><a class="page-link" href="?tab=chapters&paged=' . ($current_page - 1) . '" rel="prev" aria-label="« Previous">‹</a></li>';
                            } else {
                                echo '<li class="page-item disabled"><span class="page-link">‹</span></li>';
                            }

                            // Page Number Links
                            $start_page = max(1, $current_page - 2); // Start 2 pages before current
                            $end_page = min($total_pages, $current_page + 2); // End 2 pages after current

                            // Output page links
                            for ($i = $start_page; $i <= $end_page; $i++) {
                                if ($i === $current_page) {
                                    echo '<li class="page-item active" aria-current="page"><span class="page-link">' . $i . '</span></li>';
                                } else {
                                    echo '<li class="page-item"><a class="page-link" href="?tab=chapters&paged=' . $i . '">' . $i . '</a></li>';
                                }
                            }

                            // Next Page Link
                            if ($current_page < $total_pages) {
                                echo '<li class="page-item"><a class="page-link" href="?tab=chapters&paged=' . ($current_page + 1) . '" rel="next" aria-label="Next »">›</a></li>';
                            } else {
                                echo '<li class="page-item disabled"><span class="page-link">›</span></li>';
                            }

                            // Last Page Link
                            if ($current_page < $total_pages) {
                                echo '<li class="page-item"><a class="page-link" href="?tab=chapters&paged=' . $total_pages . '" rel="last">»</a></li>';
                            } else {
                                echo '<li class="page-item disabled"><span class="page-link">»</span></li>';
                            }
                            ?>
                        </ul>
                    </nav>

                </section>
            </aside>

        </div>
    </div>
</main>
<?php else: 

nocache_headers(); // Ensure page is not cached
include( get_query_template( '404' ) ); // Load the 404 template
exit; // Stop execution of the page

endif; ?>