<div class="sub-panel scroll-sm" id="number-panel">
    <div class="head">
        <form autocomplete="off" onsubmit="return false">
            <div class="form-group"><i class="fa-regular fa-magnifying-glass"></i>
            <input type="text" class="form-control" placeholder="Chapter number..." /></div>
        </form>
        <button class="close-primary btn btn-secondary1" id="number-close"><i class="fa-solid fa-chevron-right"></i></button>
    </div>
    <ul>
        <?php if (!empty($chapters_output)): ?>
            <?php foreach ($chapters_output as $chapter): ?>
                <li>
                    <a href="<?php echo $chapter['url']; ?>" 
                    data-number="<?php echo $chapter['number']; ?>" 
                    data-id="<?php echo $chapter['id']; ?>" 
                    title="<?php echo $chapter['title']; ?>" 
                    <?php echo $chapter['active']; ?>>
                        <?php echo $chapter['number'] . ' ' . $chapter['title']; ?>
                    </a>
                </li>
            <?php endforeach; ?>
        <?php else: ?>
            <li>No chapters available</li>
        <?php endif; ?>
    </ul>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const input = document.querySelector('.form-control');
    const listItems = document.querySelectorAll('ul li');

    input.addEventListener('input', function() {
        const filter = input.value.toLowerCase();
        
        listItems.forEach(item => {
            const link = item.querySelector('a');
            const chapterNumber = link ? link.getAttribute('data-number') : '';
            
            if (chapterNumber && chapterNumber.includes(filter)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });
});
</script>
