jQuery(document).ready(function($) {

    // Select all checkboxes functionality
    $('#select-all').on('click', function() {
        $('input[name="request_ids[]"]').prop('checked', this.checked);
    });

    // Bulk delete functionality
    $('#bulk-delete-button').on('click', function(e) {
        e.preventDefault(); // Prevent default form submission

        var selected = [];
        $('input[name="request_ids[]"]:checked').each(function() {
            selected.push($(this).val()); // Add selected request IDs to array
        });

        if (selected.length === 0) {
            alert('No requests selected.');
            return;
        }

        if (!confirm('Are you sure you want to delete the selected requests?')) {
            return;
        }

        $.ajax({
            type: 'POST',
            url: ajax_manga_params.ajax_url,
            data: {
                action: 'bulk_delete_requests', // Bulk delete action in PHP
                request_ids: selected,
                nonce: ajax_manga_params.nonce // Nonce for security
            },
            success: function(response) {
                if (response.success) {
                    // Remove deleted rows from the table dynamically
                    selected.forEach(function(id) {
                        $('#request-row-' + id).remove();
                    });
                    alert(response.data); // Show success message
                } else {
                    alert('Failed to delete requests: ' + response.data);
                }
            },
            error: function(xhr, status, error) {
                alert('Error: ' + xhr.responseText);
            }
        });
    });
});
