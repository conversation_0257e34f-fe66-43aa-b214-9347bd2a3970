<?php

if( !get_option('allow_post', 1) ) {
    return; // Just exit without doing anything if posting is not allowed
}

add_action('wp_ajax_nopriv_add_manga', 'handle_add_manga');
add_action('wp_ajax_add_manga', 'handle_add_manga');

function handle_add_manga() {
    // Check if the user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(['message' => 'سجل الدخول أولا']);
        exit;
    }

    // Check if form data is received
    if (isset($_POST['manga_name']) && isset($_FILES['imagefile'])) {
        $manga_name = sanitize_text_field($_POST['manga_name']);
        $uploaded_file = $_FILES['imagefile'];

        // Validate Manga Name (required)
        if (empty($manga_name)) {
            wp_send_json_error(['message' => 'إسم المانجا مطلوب']);
            exit;
        }

        // Validate Cover image (required)
        if ($uploaded_file['error'] == UPLOAD_ERR_NO_FILE) {
            wp_send_json_error(['message' => 'غلاف المانجا مطلوب']);
            exit;
        } elseif ($uploaded_file['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(['message' => 'حدث خطأ أثناء تحميل صورة الغلاف.']);
            exit;
        }

        // Validate Alternative Name (optional)
        $alternative = sanitize_text_field($_POST['alternative']);
        if (!empty($alternative) && strlen($alternative) < 1) {
            wp_send_json_error(['message' => 'يجب أن يكون حقل "أسماء أخرى" صالحا']);
            exit;
        }

        // Validate Summary (optional)
        $summary = sanitize_textarea_field($_POST['summary']);
        if (!empty($summary) && strlen($summary) < 1) {
            wp_send_json_error(['message' => 'يجب أن يكون حقل "القصة" صالحا']);
            exit;
        }

        // Validate Release Year (optional)
        $release_year = sanitize_text_field($_POST['release_year']);
        if (!empty($release_year) && !preg_match('/^\d{4}$/', $release_year)) {
            wp_send_json_error(['message' => 'يجب أن تكون سنة الإصدار سنة صالحة (4 أرقام).']);
            exit;
        }

        // Validate Authors (optional)
        $authors = sanitize_text_field($_POST['authors']);
        if (!empty($authors) && strlen($authors) < 1) {
            wp_send_json_error(['message' => 'يجب أن يكون حقل "المؤلفون" صالحا']);
            exit;
        }

        // Validate Manga Type (optional)
        $manga_type = sanitize_text_field($_POST['manga_type']);
        if (!empty($manga_type) && !term_exists($manga_type, 'manga_type')) {
            wp_send_json_error(['message' => 'تم اختيار نوع مانجا غير صالح']);
            exit;
        }

        // Validate Manga Status (optional)
        $manga_status = sanitize_text_field($_POST['manga_status']);
        if (!empty($manga_status) && !in_array($manga_status, ['on-going', 'end', 'canceled', 'on-hold', 'upcoming'])) {
            wp_send_json_error(['message' => 'تم اختيار حالة مانجا غير صالحة']);
            exit;
        }

        // Validate Genres (optional)
        $genres = isset($_POST['manga_genres']) ? array_map('sanitize_text_field', $_POST['manga_genres']) : [];
        if (!empty($genres)) {
            foreach ($genres as $genre) {
                if (!term_exists($genre, 'wp-manga-genre')) {
                    wp_send_json_error(['message' => 'تم اختيار تصنيف مانجا غير صالح']);
                    exit;
                }
            }
        }

        // Validate Adult Content (optional)
        $is_adult = sanitize_text_field($_POST['is_adult']);
        if (!in_array($is_adult, ['yes', 'no'], true)) {
            wp_send_json_error(['message' => 'قيمة غير صالحة للمحتوى المخصص للبالغين']);
            exit;
        }

        // Prepare data for post creation
        $post_data = array(
            'post_title'   => $manga_name,
            'post_content' => $summary,
            'post_status'  => 'publish',
            'post_type'    => 'wp-manga',
            'meta_input'   => array(
                '_wp_manga_alternative' => $alternative,
                '_wp_manga_status'      => $manga_status,
                '_wp_manga_chapter_type' => 'manga',
                'manga_adult_content'    => $is_adult === 'yes' ? ['yes'] : [], // Save as serialized
                'manga_unique_id'        => 'manga_' . uniqid() // Set the unique ID
            ),
        );

        // Insert the post into the database
        $post_id = wp_insert_post($post_data);

        if ($post_id) {
            // Set the authors taxonomy
            if (!empty($authors)) {
                wp_set_post_terms($post_id, $authors, 'wp-manga-author', true);
            }

            // Set the release year taxonomy
            if (!empty($release_year)) {
                wp_set_post_terms($post_id, $release_year, 'wp-manga-release', true);
            }

            // Process genres correctly by directly assigning the terms
            if (!empty($genres)) {
                wp_set_object_terms($post_id, $genres, 'wp-manga-genre', false);
            }

            // Set the manga type (custom taxonomy)
            if (!empty($manga_type)) {
                wp_set_post_terms($post_id, $manga_type, 'manga_type', false);
            }

            // Handle cover image upload
            if ($uploaded_file['error'] == 0) {
                $upload_overrides = array('test_form' => false);
                $movefile = wp_handle_upload($uploaded_file, $upload_overrides);

                if ($movefile && !isset($movefile['error'])) {
                    $attachment_id = wp_insert_attachment(array(
                        'guid' => $movefile['url'],
                        'post_mime_type' => $movefile['type'],
                        'post_title' => sanitize_file_name($uploaded_file['name']),
                        'post_content' => '',
                        'post_status' => 'inherit'
                    ), $movefile['file'], $post_id);

                    require_once(ABSPATH . 'wp-admin/includes/image.php');
                    $attach_data = wp_generate_attachment_metadata($attachment_id, $movefile['file']);
                    wp_update_attachment_metadata($attachment_id, $attach_data);
                    set_post_thumbnail($post_id, $attachment_id); // Set the featured image
                }
            }

            // Success response
            wp_send_json_success(['message' => 'تم إضافة المانجا بنجاح']);
        } else {
            // Error response if post was not created
            wp_send_json_error(['message' => 'فشل إضافة المانجا']);
        }
    } else {
        wp_send_json_error(['message' => 'الحقول المطلوبة مفقودة']);
    }

    exit; // Always exit after processing AJAX
}
