<?php

function ajax_load_manga() {
    check_ajax_referer('load_manga_nonce', 'nonce');

    $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $posts_per_page = 14;
    $initial_static_posts = 21;
    $offset = $initial_static_posts + (($page - 1) * $posts_per_page);

    // Determine whether to include adult manga based on cookie
    $show_adult = isset($_COOKIE['show_adult']) && $_COOKIE['show_adult'] === '1';
    $cache_key_suffix = $show_adult ? '_adult_visible' : '_adult_hidden';
    $sorted_manga_ids_cache_key = 'recently_updated_sorted_ids' . $cache_key_suffix;

    // Try to get the sorted list of manga IDs from cache
    $manga_ids_sorted_by_date = get_transient($sorted_manga_ids_cache_key);

    if (false === $manga_ids_sorted_by_date) {
        // If not in cache, generate the list
        $args_all_manga = array(
            'post_type' => 'wp-manga',
            'post_status' => 'publish',
            'posts_per_page' => -1,
        );
        $all_manga_query = new WP_Query($args_all_manga);
        $manga_latest_chapter_dates = [];

        if ($all_manga_query->have_posts()) {
            while ($all_manga_query->have_posts()) {
                $all_manga_query->the_post();
                $manga_id = get_the_ID();
                $adult_content_meta = get_post_meta($manga_id, 'manga_adult_content', true);
                $is_adult = is_array(maybe_unserialize($adult_content_meta)) && in_array('yes', maybe_unserialize($adult_content_meta));
                if (!$show_adult && $is_adult) {
                    continue;
                }
                $chapters = get_latest_chapters($manga_id, 1);
                if ($chapters) {
                    $manga_latest_chapter_dates[$manga_id] = strtotime($chapters[0]->date);
                }
            }
            wp_reset_postdata();
        }
        arsort($manga_latest_chapter_dates); // Sort by date descending
        $manga_ids_sorted_by_date = array_keys($manga_latest_chapter_dates);
        // Cache for 15 minutes (keep this for the sorted IDs only)
        set_transient($sorted_manga_ids_cache_key, $manga_ids_sorted_by_date, 15 * MINUTE_IN_SECONDS);
    }

    $total_posts = count($manga_ids_sorted_by_date);
    $total_pages = ceil(max(0, $total_posts - $initial_static_posts) / $posts_per_page);
    $has_more = ($page < $total_pages);

    // Slice the sorted array for the current page
    $manga_ids_for_page = array_slice($manga_ids_sorted_by_date, $offset, $posts_per_page);

    $manga_html = '';
    ob_start();
    if (!empty($manga_ids_for_page)) {
        $args_current_page = array(
            'post_type' => 'wp-manga',
            'post_status' => 'publish',
            'post__in' => $manga_ids_for_page,
            'posts_per_page' => $posts_per_page,
            'orderby' => 'post__in',
        );
        $manga_page_query = new WP_Query($args_current_page);
        if ($manga_page_query->have_posts()) {
            while ($manga_page_query->have_posts()) : $manga_page_query->the_post();
                // ...existing code for rendering each manga item...
                $manga_id = get_the_ID();
                $manga_title = get_the_title();
                $manga_link = get_permalink();
                $thumbnail = apply_filters('jetpack_photon_url', get_the_post_thumbnail_url(get_the_ID(), 'medium'));
                $chapters = get_latest_chapters($manga_id, 2);
                ?>
                <div class="unit item-<?php echo esc_attr($manga_id); ?>">
                    <div class="inner">
                        <a href="<?php echo esc_url($manga_link); ?>" class="poster" data-tip="<?php echo esc_attr($manga_id); ?>">
                            <div class="poster-image-wrapper shine-effect">
                                <?php
                                $types = wp_get_post_terms($manga_id, 'wp-manga-type');
                                $comic_type = !empty($types) ? $types[0]->name : '';
                                $flag_path = get_stylesheet_directory_uri() . '/assets/images/flags/';
                                $type_to_flag = [
                                    'manga'  => 'jp.svg',
                                    'manhwa' => 'kr.svg',
                                    'manhua' => 'cn.svg'
                                ];
                                $flag_image = isset($type_to_flag[strtolower($comic_type)]) 
                                    ? $flag_path . $type_to_flag[strtolower($comic_type)]
                                    : '';
                                if($flag_image): ?>
                                <div class="manga-flag width-limit" data-type="<?php echo esc_attr(strtolower($comic_type)); ?>">
                                    <img src="<?php echo esc_url($flag_image); ?>" alt="<?php echo esc_attr($comic_type); ?>" title="<?php echo esc_attr($comic_type); ?>" class="flag-icon">
                                </div>
                                <?php endif; ?>
                                <img src="<?php echo get_the_post_thumbnail_url( get_the_ID(), 'manga_cover' ); ?>" 
                                                 alt="<?php the_title(); ?>" 
                                                 style="height: 100%;object-fit: cover;"/>
                            </div>
                        </a>
                        <div class="info">
                            <a href="<?php echo esc_url($manga_link); ?>" class="rm-title title" title="<?php echo esc_attr($manga_title); ?>"><?php echo esc_html($manga_title); ?></a>
                            <ul class="content" data-name="chap">
                                <?php
                                if ($chapters) {
                                    foreach ($chapters as $chapter) {
                                        $time_diff = (current_time('timestamp') - strtotime($chapter->date)) / 108000;
                                        ?>
                                        <li>
                                            <a href="<?php echo esc_url(get_permalink($manga_id) . $chapter->chapter_slug); ?>" 
                                               class="<?php echo ($time_diff < 1) ? 'fire-chapter' : ''; ?>">
                                                <span class="ch-num">Ch. <?php echo esc_html($chapter->chapter_name); ?></span>
                                                <span class="ch-date">
                                                    <?php if ($time_diff < 1): ?>
                                                        <?php 
                                                        // Apply filter to allow plugins to modify the chapter tag
                                                        $chapter_tag = apply_filters('wp_manga_chapter_tag', 
                                                            '<span class="new-chapter"><i class="fa-solid fa-fire"></i> New</span>', 
                                                            $chapter, 
                                                            $manga_id
                                                        );
                                                        echo $chapter_tag;
                                                        ?>
                                                    <?php else: ?>
                                                        <?php echo esc_html(time_ago($chapter->date)); ?>
                                                    <?php endif; ?>
                                                </span>
                                            </a>
                                        </li>
                                    <?php }
                                }
                                ?>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php
            endwhile;
            wp_reset_postdata();
        } else {
            $manga_html = '<p>Il n\'y a plus de mangas à afficher.</p>';
        }
        $manga_html = ob_get_clean();
    } else {
        if ($page > 1) {
            $manga_html = '<p>Vous êtes arrivé au bout.</p>';
        } else {
            $manga_html = '<p>Il n\'y a pas de mangas à afficher pour le moment.</p>';
        }
    }
    wp_send_json_success(array(
        'manga_html' => $manga_html,
        'total_pages' => $total_pages,
        'current_page' => $page,
        'total_posts' => $total_posts,
        'has_more' => $has_more
    ));
}
add_action('wp_ajax_load_manga', 'ajax_load_manga');
add_action('wp_ajax_nopriv_load_manga', 'ajax_load_manga');

if (!function_exists('ajax_scripts')) {
    function ajax_scripts() {
        wp_enqueue_script(
            'ajax-manga', 
            get_stylesheet_directory_uri() . '/js/ajax.js', 
            array('jquery-js'), 
            time(), 
            true
        );
        
        wp_localize_script('ajax-manga', 'ajax_manga_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('load_manga_nonce')
        ));
    }
}
add_action('wp_enqueue_scripts', 'ajax_scripts', 10);

