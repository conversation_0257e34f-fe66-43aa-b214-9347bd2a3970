<?php
	/**
	 * The Template for content of a manga chapter, listing layout (ie. all images in 1 page), in a Chapter Reading page
	 *
	 * This template can be overridden by copying it to your-child-theme/madara-core/reading-content/content-reading-list.php
	 *
	 * HOWEVER, on occasion Mad<PERSON> will need to update template files and you
	 * (the theme developer) will need to copy the new files to your theme to
	 * maintain compatibility. We try to do this as little as possible, but it does
	 * happen. When this occurs the version of the template file will be bumped and
	 * we will list any important changes on our theme release logs.
	 * @package Madara
	 * @version 1.7.2.2
	 */

	use App\Madara;

	// Initialize required variables
	$wp_manga_functions = madara_get_global_wp_manga_functions();
	$post_id = get_the_ID();
	$reading_chapter = function_exists('madara_permalink_reading_chapter') ? madara_permalink_reading_chapter() : false;

	// Early return if no chapter found
	if (!$reading_chapter) {
		if ($chapter_slug = get_query_var('chapter')) {
			$reading_chapter = $wp_manga_functions->get_chapter_by_slug($post_id, $chapter_slug);
		}
		if (!$reading_chapter) {
			return;
		}
	}

	// Get chapter data
	$chapter = $wp_manga_functions->get_single_chapter($post_id, $reading_chapter['chapter_id']);
	if (!$chapter) {
		return;
	}

	// Get storage information
	$in_use = isset($_GET['host']) ? $_GET['host'] : $chapter['storage']['inUse'];
	$storage = $chapter['storage'];
	if (!isset($storage[$in_use]) || !is_array($storage[$in_use]['page'])) {
		return;
	}

	// Schema markup and content container
	?>
	<div class="pages flex-col-cen">
		<div id="ch-images">
		    <?php
		    $sponsored_options = get_option('sponsored_content_settings');
		    if (isset($sponsored_options['enable_content_reading_list_above']) && $sponsored_options['enable_content_reading_list_above'] === '1') { // Strict check for '1'
		        $link = !empty($sponsored_options['link_content_reading_list_above']) ? esc_url($sponsored_options['link_content_reading_list_above']) : '#';
		        $image = !empty($sponsored_options['image_content_reading_list_above']) ? esc_url($sponsored_options['image_content_reading_list_above']) : ''; // Default to empty
		        if (!empty($image)) { // Only display if image is set
		        ?>
		        <a href="<?php echo $link; ?>" target="_blank">
	                <img src="<?php echo $image; ?>" alt="Sponsored Content" style="max-width: 100% !important; width: 100%;" />
	            </a>
		    <?php }
		    } ?>
			<div itemscope itemtype="https://schema.org/ComicIssue">
				<meta itemprop="name" content="<?php echo esc_attr(get_the_title() . ' - ' . $reading_chapter['chapter_name']); ?>">
				<meta itemprop="datePublished" content="<?php echo get_the_date('c'); ?>">

				<div itemprop="hasPart" itemscope itemtype="https://schema.org/ComicStory">
					<meta itemprop="name" content="<?php echo esc_attr($reading_chapter['chapter_name']); ?>">

					<?php
					foreach ($chapter['storage'][$in_use]['page'] as $page => $link) {
						$host = $chapter['storage'][$in_use]['host'];
						$src = apply_filters('wp_manga_chapter_image_url', $host . $link['src'], $host, $link['src'], $post_id, $reading_chapter['chapter_slug']);
						
						if ($src) {
							$alt_text = esc_attr(get_the_title() . ' - ' . $reading_chapter['chapter_name'] . ' - Page ' . $page);
							// Simple encryption of the URL
							$encrypted_src = base64_encode($src);
							?>
							<figure itemscope itemtype="https://schema.org/ImageObject" class="page fit-w">
								<div class="image-skeleton" data-protected="true">
									<div class="skeleton-placeholder">
										<i class="fa-solid fa-image skeleton-icon"></i>
										<div class="skeleton-bg"></div>
									</div>
									<noscript>JavaScript is required to view images</noscript>
									<div class="protected-image-data" 
										style="display: none;"
										data-number="image-<?php echo esc_attr($page); ?>"
										data-src="<?php echo esc_attr($encrypted_src); ?>"
										data-alt="<?php echo $alt_text; ?>">
									</div>
								</div>
							</figure>
							<?php
							do_action('madara_after_chapter_image_wrapper', $page, count($chapter['storage'][$in_use]['page']));
						}
					}
					?>
				</div>
			</div>
		</div>
		<?php
	    if (isset($sponsored_options['enable_content_reading_list_below']) && $sponsored_options['enable_content_reading_list_below'] === '1') { // Strict check for '1'
	        $link = !empty($sponsored_options['link_content_reading_list_below']) ? esc_url($sponsored_options['link_content_reading_list_below']) : '#';
	        $image = !empty($sponsored_options['image_content_reading_list_below']) ? esc_url($sponsored_options['image_content_reading_list_below']) : ''; // Default to empty
	        if (!empty($image)) { // Only display if image is set
	        ?>
	        <a href="<?php echo $link; ?>" target="_blank">
	            <img src="<?php echo $image; ?>" alt="Sponsored Content" style="max-width: 100% !important; width: 100%;" />
	        </a>
	    <?php }
	    } ?>
	</div>

	<style>
	.image-skeleton {
		position: relative;
		width: 100%;
		height: 900px;
		background: hsl(224 7% 4%); /* Shadcn UI dark background */
		border-radius: 8px;
		overflow: hidden;
		margin-bottom: 15px;
	}

	.skeleton-placeholder {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: hsl(224 7% 6.5%); /* Shadcn UI darker shade */
		z-index: 1;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.skeleton-icon {
		font-size: 48px;
		color: hsl(224 7% 12%); /* Slightly lighter than background */
		z-index: 2;
		opacity: 0.5;
	}

	.skeleton-bg {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(
			90deg,
			transparent 0%,
			hsl(224 7% 10%) 20%, /* Shadcn UI highlight color */
			hsl(224 7% 8%) 40%,
			transparent 100%
		);
		background-size: 200% 100%;
		animation: shimmer 2s linear infinite;
	}

	@keyframes shimmer {
		0% {
			transform: translateX(-100%);
		}
		100% {
			transform: translateX(100%);
		}
	}

	.image-skeleton img.loaded {
		position: static !important;
		opacity: 1 !important;
		height: auto !important;
		width: auto !important;
		max-width: 100%;
	}

	.image-skeleton.loaded {
		height: auto !important;
		background: none !important;
		border-radius: 0 !important;
		overflow: visible !important;
		margin-bottom: 0 !important;
	}
	</style>

	<script>
	document.addEventListener('DOMContentLoaded', function() {
		// Add a flag to indicate JS is loaded
		document.body.classList.add('js-loaded');
		
		const initializeImages = () => {
			const protectedContainers = document.querySelectorAll('.image-skeleton[data-protected="true"]');
			
			protectedContainers.forEach(container => {
				const imageData = container.querySelector('.protected-image-data');
				if (!imageData) return;
				
				// Create the actual image element
				const img = document.createElement('img');
				img.className = 'preload-image fit-w';
				img.style.opacity = '0';
				img.style.transition = 'opacity 0.5s ease';
				img.style.position = 'absolute';
				img.dataset.number = imageData.dataset.number;
				// Decrypt the URL
				img.dataset.src = atob(imageData.dataset.src);
				img.alt = imageData.dataset.alt;
				
				// Add schema markup
				img.setAttribute('itemprop', 'image');
				
				// Insert the image
				container.appendChild(img);
			});
			
			// Now load images sequentially
			const loadImagesSequentially = (images, index = 0) => {
				if (index >= images.length) return;

				const image = images[index];
				if (!image.dataset.src) {
					loadImagesSequentially(images, index + 1);
					return;
				}

				const img = new Image();
				img.src = image.dataset.src;

				const loadNext = () => setTimeout(() => loadImagesSequentially(images, index + 1), 100);

				img.onload = () => {
					image.src = img.src;
					image.classList.add('loaded');
					const parent = image.closest('.image-skeleton');
					if (parent) {
						parent.classList.add('loaded');
						const placeholder = parent.querySelector('.skeleton-placeholder');
						if (placeholder) {
							placeholder.style.opacity = '0';
							setTimeout(() => placeholder.remove(), 500);
						}
					}
					loadNext();
				};

				img.onerror = () => {
					console.error('Failed to load image:', img.src);
					loadNext();
				};
			};

			const images = Array.from(document.querySelectorAll('.preload-image'));
			if (images.length) loadImagesSequentially(images);
		};
		
		// Initialize the protection system
		initializeImages();
	});
	</script>