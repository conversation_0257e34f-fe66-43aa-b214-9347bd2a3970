<?php

function add_user_id() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'manga_chapters';

    // Check if the column 'user_id' exists
    $column_exists = $wpdb->get_var("SHOW COLUMNS FROM `$table_name` LIKE 'user_id'");

    if (is_null($column_exists)) {
        $wpdb->query("ALTER TABLE `$table_name` ADD `user_id` BIGINT(20) UNSIGNED NOT NULL");
    }
}
add_action('init', 'add_user_id');