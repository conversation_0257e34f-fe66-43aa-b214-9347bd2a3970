<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="http://gmpg.org/xfn/11">
    <link rel="pingback" href="<?php bloginfo( 'pingback_url' ); ?>">
    <script type='text/javascript'>
        window.__sharethis__ = {
            property: '667f2dbd25b3fe00191df619',
            product: 'inline-share-buttons'
        };
    </script>
    <!-- <script type='text/javascript' src='https://platform-api.sharethis.com/js/sharethis.js' async='async'></script> -->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-R1QTQCMZ4M"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
    
      gtag('config', 'G-R1QTQCMZ4M');
    </script>
    <!-- Galaksion -->
    <script data-cfasync="false" async type="text/javascript" src="//dj.fattingnines.com/r3lPA405sq4CAm/rXral"></script>
    <!-- <script data-cfasync="false" async type="text/javascript" src="//outcrycaseate.com/r2baFLsuGYxU0ql/80953"></script> -->
    <!-- <script disable-devtool-auto src="https://cdn.jsdelivr.net/npm/disable-devtool@latest" disable-menu="true"></script> <script>DisableDevtool.isSuspend = false;</script> -->
    <?php 
    $is_chapter_reading_page = false;
    if(function_exists('madara_permalink_reading_chapter')) {
        $is_chapter_reading_page = madara_permalink_reading_chapter() ? true : false;
    } else {
        if($chapter_slug = get_query_var('chapter')) {
            global $wp_manga_functions;
            $is_chapter_reading_page = $wp_manga_functions->get_chapter_by_slug(get_the_ID(), $chapter_slug) ? true : false;
        }
    }

    $show_adult = isset($_COOKIE['show_adult']) && $_COOKIE['show_adult'] === '1'; 

    wp_head(); 
    ?>
</head>

<body>
<div class="wrapper">
    <header>
        <div class="container">
            <div class="component">
            <button id="nav-menu-btn" class="btn nav-btn">
                    <i class="fa-solid fa-bars fa-lg"></i>
                </button>
            <?php include get_stylesheet_directory() . '/template-parts/header/brand.php'; ?>
            <div id="nav-menu">
                    <ul>
                        <!-- <?php include get_stylesheet_directory() . '/template-parts/header/genres.php'; ?> -->
                        <li class="menu-item">
                            <a href="<?php echo home_url('/?post_type=wp-manga&s=&sort=most_viewed'); ?>">
                                <i class="fa-solid fa-fire"></i> Plus Populaire
                            </a>
                        </li>
                        <li class="menu-item">
                            <a href="<?php echo home_url('/?post_type=wp-manga&s=&sort=recently_added'); ?>">
                                <i class="fa-solid fa-bolt"></i> Récent
                            </a>
                        </li>
                        <?php
                        wp_nav_menu(array(
                            'theme_location' => 'primary_menu',
                            'menu' => 'primary_menu',
                            'container' => false,
                            'items_wrap' => '%3$s',
                        ));
                        ?>
                        <?php include get_stylesheet_directory() . '/template-parts/header/random.php'; ?>
                        <li class="menu-item">
                            <a href="http://pornhwa.io/" style="position: relative;">
                                <i class="fa-solid fa-heart"></i> Adulte
                                <span class="new-badge pulse-badge">+18</span>
                            </a>
                        </li>
                    </ul>
                </div>   
                

                <button id="nav-search-btn" class="btn nav-btn"><i class="fa-solid fa-magnifying-glass"></i></button>
                

                <?php include get_stylesheet_directory() . '/template-parts/header/search.php'; ?>
                <div class="nav-user" id="user">
                    <?php if ( is_user_logged_in() ) { ?>
                        <?php include get_stylesheet_directory() . '/template-parts/footer/user.php'; ?>
                    <?php } else { ?>
                        <?php include get_stylesheet_directory() . '/template-parts/footer/user-no-login.php'; ?>
                    <?php } ?>
                </div> 

                             
                
                

            </div>
        </div>
    </header>
<?php // endif; ?>
<!-- wrapper closed in footer.php -->

<?php
if ( is_user_logged_in() ) {
} else {
    $child_path = get_stylesheet_directory() . '/template-parts/footer/login-register.php';
    $parent_path = get_template_directory() . '/template-parts/footer/login-register.php';

    if (file_exists($child_path)) {
        include($child_path);
    } elseif (file_exists($parent_path)) {
        include($parent_path);
    }
}
?>
<script type="text/javascript">
    var ajaxurl = "<?php echo admin_url('admin-ajax.php'); ?>";
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchBtn = document.getElementById('nav-search-btn');
    const searchModal = document.getElementById('search-modal');
    const closeBtn = document.querySelector('.close-modal');
    const searchInput = searchModal.querySelector('input[type="text"]');

    searchBtn.addEventListener('click', function() {
        searchModal.style.display = 'block';
        // Trigger reflow
        searchModal.offsetHeight;
        searchModal.classList.add('show');
        // Focus on input after animation
        setTimeout(() => {
            searchInput.focus();
        }, 300);
    });

    function closeModal() {
        searchModal.classList.remove('show');
        setTimeout(() => {
            searchModal.style.display = 'none';
        }, 300); // Match the transition duration
    }

    closeBtn.addEventListener('click', closeModal);

    window.addEventListener('click', function(event) {
        if (event.target == searchModal) {
            closeModal();
        }
    });

    // Add escape key support
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && searchModal.classList.contains('show')) {
            closeModal();
        }
    });
});

// Add this new script for ShareThis
window.addEventListener('load', function() {
    if (typeof window.__sharethis__ === 'undefined') {
        console.warn('ShareThis not loaded properly');
        return;
    }
    
    // Force ShareThis to reinitialize if needed
    if (typeof window.__sharethis__.load === 'function') {
        window.__sharethis__.load('inline-share-buttons');
    }
});
</script>
</body>