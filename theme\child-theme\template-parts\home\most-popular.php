<?php
// Check if this template has already been loaded
if (!defined('MOST_POPULAR_TEMPLATE_LOADED')): 
    define('MOST_POPULAR_TEMPLATE_LOADED', true);
?>

<div class="popular-series-section">
    <div class="section-header">
        <h2>Top series</h2>
        
        <div class="time-filters">
            <button class="filter-btn active" data-period="weekly">Weekly</button>
            <button class="filter-btn" data-period="monthly">Monthly</button>
        </div>
    </div>

    <div class="popular-series-list" id="popular-manga-list">
        <div class="popular-series-wrapper">
            <!-- Removed skeleton loading HTML -->
        </div>
    </div>
    <div class="popular-series-list hidden" id="popular-manga-list-monthly"></div>
</div>

<style>
.popular-series-wrapper {
    opacity: 1;
    transition: opacity 0.3s ease;
}

.popular-series-wrapper.loading {
    opacity: 0.7;
}
</style>

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    const weeklyList = document.getElementById('popular-manga-list');
    const monthlyList = document.getElementById('popular-manga-list-monthly');
    const filterButtons = document.querySelectorAll('.filter-btn');
    let currentPeriod = 'weekly';
    
    const nonce = '<?php echo wp_create_nonce('filter_popular_manga_nonce'); ?>';
    
    // Function to load data with loading state
    const loadData = async (period) => {
        const targetList = period === 'weekly' ? weeklyList : monthlyList;
        
        console.log('Loading data for period:', period); // Debug log
        
        try {
            const response = await fetch(ajaxurl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: new URLSearchParams({
                    action: 'filter_popular_manga',
                    period: period,
                    nonce: nonce
                })
            });
            
            const data = await response.json();
            console.log('Response:', data); // Debug log
            
            if (data.success) {
                targetList.innerHTML = data.data;
                targetList.classList.remove('loading');
            } else {
                console.error('AJAX request failed:', data); // Debug log
            }
        } catch (error) {
            console.error('Error loading manga data:', error);
        }
    };

    // Load both weekly and monthly data immediately
    Promise.all([
        loadData('weekly'),
        loadData('monthly')
    ]);

    // Handle tab switching
    document.querySelector('.time-filters').addEventListener('click', (e) => {
        const btn = e.target.closest('.filter-btn');
        if (!btn || btn.classList.contains('active')) return;

        const period = btn.dataset.period;
        if (period === currentPeriod) return;

        filterButtons.forEach(button => button.classList.toggle('active'));
        weeklyList.classList.toggle('hidden');
        monthlyList.classList.toggle('hidden');
        
        currentPeriod = period;
    }, { passive: true });
});
</script>

<?php endif; ?>
