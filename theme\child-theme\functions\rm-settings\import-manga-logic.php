<?php

function import_manga_logic($use_fixed_content = true) {
    $file = get_stylesheet_directory() . '/functions/rm-settings/manga-info.txt';
    $fixed_content = 'هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة، لقد تم توليد هذا النص من مولد النص العربى، حيث يمكنك أن تولد مثل هذا النص أو العديد من النصوص الأخرى إضافة إلى زيادة عدد الحروف التى يولدها التطبيق. إذا كنت تحتاج إلى عدد أكبر من الفقرات يتيح لك مولد النص العربى زيادة عدد الفقرات كما تريد، النص لن يبدو مقسما ولا يحوي أخطاء لغوية، مولد النص العربى مفيد لمصممي المواقع على وجه الخصوص، حيث يحتاج العميل فى كثير من الأحيان أن يطلع على صورة حقيقية لتصميم الموقع.'; // Set your fixed content here

    if (!file_exists($file)) {
        error_log('Manga info file not found.');
        return 'File not found';
    }

    $content = file_get_contents($file);
    $mangas = preg_split("/\r?\n\r?\n/", trim($content)); // Split on double newlines to separate manga blocks
    $imported_titles = [];

    foreach ($mangas as $manga) {
        $manga_data = parse_manga_data($manga);
        
        // Skip if essential fields are missing or if the title has already been imported
        if (empty($manga_data['manga_name']) || empty($manga_data['story']) || in_array($manga_data['manga_name'], $imported_titles)) {
            continue;
        }

        // Use fixed content or story from the text file
        $post_content = $use_fixed_content ? $fixed_content : $manga_data['story'];

        $post_id = create_manga_post($manga_data, $post_content); // Pass the content here

        if ($post_id) {
            handle_manga_metadata($post_id, $manga_data);
            handle_manga_type($post_id, $manga_data['type']); // Handle the manga type like others
            handle_manga_terms($post_id, $manga_data);
            handle_manga_images($post_id, $manga_data);
        }

        // Track imported titles to prevent duplicates
        $imported_titles[] = $manga_data['manga_name'];
    }

    return 'Import completed';
}

function parse_manga_data($manga) {
    $manga_data = [];
    $lines = explode("\n", trim($manga)); // Split on single newlines to separate key-value pairs

    foreach ($lines as $line) {
        $pair = explode(': ', $line, 2);
        if (count($pair) === 2) {
            $key = strtolower(str_replace(' ', '_', $pair[0]));
            $manga_data[$key] = trim($pair[1]);
        }
    }

    return $manga_data;
}

function create_manga_post($manga_data, $post_content) {
    $post_data = [
        'post_title'   => $manga_data['manga_name'],
        'post_content' => $post_content, // Use the provided content
        'post_status'  => 'publish',
        'post_type'    => 'wp-manga'
    ];

    return wp_insert_post($post_data);
}

function handle_manga_metadata($post_id, $manga_data) {
    update_post_meta($post_id, '_wp_manga_status', $manga_data['status']);
    update_post_meta($post_id, '_wp_manga_chapter_type', $manga_data['chapter_type']);
    update_post_meta($post_id, '_wp_manga_alternative', $manga_data['alternative_name']);
}

function handle_manga_terms($post_id, $manga_data) {
    handle_genres($post_id, $manga_data['genres']);
    handle_artists($post_id, $manga_data['artist']);
    handle_authors($post_id, $manga_data['author']);
    handle_release_date($post_id, $manga_data['release-date']);
    handle_manga_type($post_id, $manga_data['type']); // Handle the manga type
}

function handle_manga_type($post_id, $manga_type_str) {
    if (!empty($manga_type_str)) {
        $types = array_map('trim', explode(',', $manga_type_str));
        error_log('Manga Types to be processed: ' . print_r($types, true));

        foreach ($types as $type) {
            $term = term_exists($type, 'wp-manga-type');
            if (!$term) {
                $term_result = wp_insert_term($type, 'wp-manga-type');
                error_log('Inserting type: ' . $type . ' - Result: ' . print_r($term_result, true));
            } else {
                error_log('Type already exists: ' . print_r($term, true));
            }
        }

        $type_ids = [];
        foreach ($types as $type) {
            $term = get_term_by('name', $type, 'wp-manga-type');
            if ($term) {
                $type_ids[] = $term->term_id;
            }
        }

        $result = wp_set_post_terms($post_id, $type_ids, 'wp-manga-type');
        error_log('wp_set_post_terms result: ' . print_r($result, true));
    }
}

function handle_genres($post_id, $genres_str) {
    if (!empty($genres_str)) {
        $genres = array_map('trim', explode(',', $genres_str));
        error_log('Genres to be processed: ' . print_r($genres, true));

        foreach ($genres as $genre) {
            $term = term_exists($genre, 'wp-manga-genre');
            if (!$term) {
                $term_result = wp_insert_term($genre, 'wp-manga-genre');
                error_log('Inserting genre: ' . $genre . ' - Result: ' . print_r($term_result, true));
            } else {
                error_log('Genre already exists: ' . print_r($term, true));
            }
        }

        $genre_ids = [];
        foreach ($genres as $genre) {
            $term = get_term_by('name', $genre, 'wp-manga-genre');
            if ($term) {
                $genre_ids[] = $term->term_id;
            }
        }

        $result = wp_set_post_terms($post_id, $genre_ids, 'wp-manga-genre');
        error_log('wp_set_post_terms result: ' . print_r($result, true));
    }
}

function handle_artists($post_id, $artists_str) {
    if (!empty($artists_str)) {
        $artists = array_map('trim', explode(',', $artists_str));
        wp_set_post_terms($post_id, $artists, 'wp-manga-artist');
    }
}

function handle_authors($post_id, $authors_str) {
    if (!empty($authors_str)) {
        $authors = array_map('trim', explode(',', $authors_str));
        wp_set_post_terms($post_id, $authors, 'wp-manga-author');
    }
}

function handle_release_date($post_id, $release_date) {
    if (!empty($release_date)) {
        wp_set_post_terms($post_id, [$release_date], 'wp-manga-release');
    }
}

function handle_manga_images($post_id, $manga_data) {
    set_thumbnail($post_id, $manga_data['thumbnail']);
    set_banner($post_id, $manga_data['banner']);
}

function set_thumbnail($post_id, $thumbnail_filename) {
    if (!empty($thumbnail_filename)) {
        $thumbnail_path = get_stylesheet_directory() . '/functions/rm-settings/covers/' . $thumbnail_filename;
        if (file_exists($thumbnail_path)) {
            $upload_dir = wp_upload_dir();
            $new_file_path = $upload_dir['path'] . '/' . $thumbnail_filename;
            if (copy($thumbnail_path, $new_file_path)) {
                $attach_id = insert_image_attachment($new_file_path, $post_id);
                set_post_thumbnail($post_id, $attach_id);
            } else {
                error_log('Failed to copy thumbnail file: ' . $thumbnail_path);
            }
        } else {
            error_log('Thumbnail file not found: ' . $thumbnail_path);
        }
    }
}

function set_banner($post_id, $banner_filename) {
    if (!empty($banner_filename)) {
        $banner_path = get_stylesheet_directory() . '/functions/rm-settings/banners/' . $banner_filename;
        if (file_exists($banner_path)) {
            $upload_dir = wp_upload_dir();
            $new_file_path = $upload_dir['path'] . '/' . $banner_filename;
            if (copy($banner_path, $new_file_path)) {
                $attach_id = insert_image_attachment($new_file_path, 0); // Attach to no specific post initially
                update_post_meta($post_id, 'manga_banner', $upload_dir['url'] . '/' . basename($new_file_path));
            } else {
                error_log('Failed to copy banner file: ' . $banner_path);
            }
        } else {
            error_log('Banner file not found: ' . $banner_path);
        }
    }
}

function insert_image_attachment($file_path, $post_id) {
    $wp_filetype = wp_check_filetype($file_path, null);
    $attachment = [
        'guid'           => wp_upload_dir()['url'] . '/' . basename($file_path),
        'post_mime_type' => $wp_filetype['type'],
        'post_title'     => preg_replace('/\.[^.]+$/', '', basename($file_path)),
        'post_content'   => '',
        'post_status'    => 'inherit'
    ];

    $attach_id = wp_insert_attachment($attachment, $file_path, $post_id);
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    $attach_data = wp_generate_attachment_metadata($attach_id, $file_path);
    wp_update_attachment_metadata($attach_id, $attach_data);
    
    return $attach_id;
}
