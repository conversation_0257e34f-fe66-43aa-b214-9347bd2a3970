<?php

function ajax_random_manga_url() {
    if (!defined('DOING_AJAX') || !DOING_AJAX) {
        wp_die('Unauthorized');
    }

    // Meta query for filtering adult manga
    $meta_query = array(
        array(
            'key'     => 'manga_adult_content',
            'value'   => 'yes',
            'compare' => 'NOT LIKE'
        )
    );

    $args = array(
        'post_type'      => 'wp-manga',
        'posts_per_page' => 1,
        'orderby'        => 'rand',
        'meta_query'     => $meta_query, // Add the meta query here
        'post_status'    => 'publish' // Only get published posts
    );

    $random_post = new WP_Query($args);

    if ($random_post->have_posts()) {
        $random_post->the_post();
        echo get_permalink();
    } else {
        echo '#'; // Fallback
    }

    wp_die();
}

add_action('wp_ajax_random_manga', 'ajax_random_manga_url');
add_action('wp_ajax_nopriv_random_manga', 'ajax_random_manga_url');