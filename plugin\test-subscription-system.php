<?php
/**
 * Test file for the subscription system
 * This file can be used to test the subscription functionality
 * 
 * Usage: Include this file in a WordPress environment where the plugin is active
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test subscription system functionality
 */
function test_wp_manga_subscription_system() {
    echo "<h2>WP Manga Subscription System Test</h2>";
    
    // Test 1: Check if subscription manager is available
    echo "<h3>Test 1: Subscription Manager Availability</h3>";
    if (class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
        echo "✅ WP_MANGA_SUBSCRIPTION_MANAGER class exists<br>";
        $subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
        if ($subscription_manager) {
            echo "✅ Subscription manager instance created successfully<br>";
        } else {
            echo "❌ Failed to create subscription manager instance<br>";
        }
    } else {
        echo "❌ WP_MANGA_SUBSCRIPTION_MANAGER class not found<br>";
        return;
    }
    
    // Test 2: Check database tables
    echo "<h3>Test 2: Database Tables</h3>";
    global $wpdb;
    
    $plans_table = $wpdb->prefix . 'manga_subscription_plans';
    $subscriptions_table = $wpdb->prefix . 'manga_user_subscriptions';
    
    $plans_exists = $wpdb->get_var("SHOW TABLES LIKE '$plans_table'") == $plans_table;
    $subscriptions_exists = $wpdb->get_var("SHOW TABLES LIKE '$subscriptions_table'") == $subscriptions_table;
    
    if ($plans_exists) {
        echo "✅ Subscription plans table exists<br>";
    } else {
        echo "❌ Subscription plans table missing<br>";
    }
    
    if ($subscriptions_exists) {
        echo "✅ User subscriptions table exists<br>";
    } else {
        echo "❌ User subscriptions table missing<br>";
    }
    
    // Test 3: Check default subscription plans
    echo "<h3>Test 3: Default Subscription Plans</h3>";
    $plans = $subscription_manager->get_subscription_plans();
    
    if (!empty($plans)) {
        echo "✅ Found " . count($plans) . " subscription plan(s):<br>";
        foreach ($plans as $plan) {
            echo "&nbsp;&nbsp;- {$plan->plan_name} ({$plan->duration_months} months) - $" . number_format($plan->price, 2) . "<br>";
        }
    } else {
        echo "❌ No subscription plans found<br>";
    }
    
    // Test 4: Test subscription functions
    echo "<h3>Test 4: Helper Functions</h3>";
    
    if (function_exists('wp_manga_chapter_coin_get_subscription_plans')) {
        echo "✅ wp_manga_chapter_coin_get_subscription_plans() function exists<br>";
    } else {
        echo "❌ wp_manga_chapter_coin_get_subscription_plans() function missing<br>";
    }
    
    if (function_exists('wp_manga_chapter_coin_user_has_subscription')) {
        echo "✅ wp_manga_chapter_coin_user_has_subscription() function exists<br>";
    } else {
        echo "❌ wp_manga_chapter_coin_user_has_subscription() function missing<br>";
    }
    
    // Test 5: Test main plugin class
    echo "<h3>Test 5: Main Plugin Class</h3>";
    
    if (class_exists('WP_MANGA_ADDON_CHAPTER_COIN')) {
        echo "✅ WP_MANGA_ADDON_CHAPTER_COIN class exists<br>";
        $plugin_instance = WP_MANGA_ADDON_CHAPTER_COIN::get_instance();
        if ($plugin_instance) {
            echo "✅ Plugin instance created successfully<br>";
            
            // Test subscription status method
            if (method_exists($plugin_instance, 'get_user_subscription_status')) {
                echo "✅ get_user_subscription_status() method exists<br>";
            } else {
                echo "❌ get_user_subscription_status() method missing<br>";
            }
        } else {
            echo "❌ Failed to create plugin instance<br>";
        }
    } else {
        echo "❌ WP_MANGA_ADDON_CHAPTER_COIN class not found<br>";
    }
    
    // Test 6: Test current user subscription status
    echo "<h3>Test 6: Current User Subscription Status</h3>";
    $current_user_id = get_current_user_id();
    
    if ($current_user_id) {
        echo "Current User ID: {$current_user_id}<br>";
        
        $has_subscription = $subscription_manager->has_active_subscription($current_user_id);
        if ($has_subscription) {
            echo "✅ User has active subscription<br>";
            $subscription = $subscription_manager->get_user_subscription($current_user_id);
            if ($subscription) {
                echo "&nbsp;&nbsp;Plan: {$subscription->plan_name}<br>";
                echo "&nbsp;&nbsp;End Date: {$subscription->end_date}<br>";
                echo "&nbsp;&nbsp;Status: {$subscription->status}<br>";
            }
        } else {
            echo "ℹ️ User does not have active subscription<br>";
        }
    } else {
        echo "ℹ️ No user logged in<br>";
    }
    
    // Test 7: Test subscription statistics
    echo "<h3>Test 7: Subscription Statistics</h3>";
    $stats = $subscription_manager->get_subscription_stats();
    
    echo "Active Subscriptions: {$stats['active']}<br>";
    echo "Expired Subscriptions: {$stats['expired']}<br>";
    echo "Cancelled Subscriptions: {$stats['cancelled']}<br>";
    
    echo "<h3>Test Complete</h3>";
    echo "<p>If all tests show ✅, the subscription system is properly configured.</p>";
}

// Only run test if user is admin and test parameter is present
add_action('init', function() {
    if (current_user_can('manage_options') && isset($_GET['test_subscription'])) {
        add_action('wp_footer', 'test_wp_manga_subscription_system');
    }
});

/**
 * Add test link to admin bar
 */
function add_subscription_test_link($wp_admin_bar) {
    if (current_user_can('manage_options')) {
        $wp_admin_bar->add_node(array(
            'id' => 'test-subscription',
            'title' => 'Test Subscription System',
            'href' => add_query_arg('test_subscription', '1', home_url()),
            'meta' => array('target' => '_blank')
        ));
    }
}
add_action('admin_bar_menu', 'add_subscription_test_link', 100);
