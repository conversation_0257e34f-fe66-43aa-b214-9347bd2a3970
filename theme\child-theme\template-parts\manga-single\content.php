<aside class="hcontent">

    <div class="hposter">
        <div class="covers">
            <div class="cover-background" style="background-image: url('<?php echo get_the_post_thumbnail_url( get_the_ID(), 'manga_cover' ); ?>')"></div>
            <div class="main-cover">
                <?php
                // Get flag image based on manga type
                $flag_path = get_stylesheet_directory_uri() . '/assets/images/flags/';
                $type_to_flag = [
                    'manga'  => 'jp.svg',
                    'manhwa' => 'kr.svg',
                    'manhua' => 'cn.svg'
                ];
                
                $comic_type = strtolower($manga_meta_type);
                $flag_image = isset($type_to_flag[$comic_type]) 
                    ? $flag_path . $type_to_flag[$comic_type]
                    : '';
                
                if($flag_image): ?>
                <div class="manga-flag" data-type="<?php echo esc_attr($comic_type); ?>">
                    <img src="<?php echo esc_url($flag_image); ?>" 
                         alt="<?php echo esc_attr($manga_meta_type); ?>" 
                         title="<?php echo esc_attr($manga_meta_type); ?>" 
                         class="flag-icon">
                </div>
                <?php endif; ?>
                <img class="cover" src="<?php echo get_the_post_thumbnail_url( get_the_ID(), 'manga_cover' ); ?>" alt="<?php echo get_the_title(); ?>" />
            </div>
        </div>
        <div class="actions">
            <?php if( $start_reading_ch ) : ?>
                <a class="btn btn-lg btn-primary readnow bhs" href="<?php echo esc_url( get_permalink() . $start_reading_ch ); ?>">
                    <i class="fa-solid fa-play"></i>
                    <p>Lire le Ch. 1</p>
                </a>
            <?php endif; ?>

            <button class="bookmark-button btn btn-lg btn-secondary1 h-100 bhs-2" type="button" data-post-id="<?php echo get_the_ID(); ?>">
                <i class="fa-solid fa-bookmark fa-xs"></i>
                <span>Favoris</span>
            </button> 

            <?php 
            // Only show upload button to admin, editor, or post author
            $post_id = get_the_ID();
            $author_id = get_post_field('post_author', $post_id);
            if ( get_option('allow_post', 1) 
                && ( current_user_can('administrator') 
                    || current_user_can('editor') 
                    || get_current_user_id() === (int) $author_id )
            ) : ?>
            <button class="up-chapter-btn btn btn-lg btn-secondary1 h-100 bhs-2" type="button">
                <span>Ajouter</span>
                <i class="fa-solid fa-arrow-up-from-bracket"></i>
            </button>
            <?php endif; ?>

        </div>
    </div>
    <div class="serie-info">
        <h1 class="serie-title"><?php echo get_the_title(); ?></h1>
        <?php if ( !empty( $alternative_title ) ) : ?>
            <h6 class="alternative-title"><?php echo esc_html( $alternative_title ); ?></h6>
        <?php endif; ?>
        <?php if (!empty($genres)) : ?>
            <div class="genre-list">
                <span class="grx">
                    <?php foreach ($genres as $index => $genre) : 
                        $genre_link = get_term_link($genre);
                        if (!is_wp_error($genre_link)) : ?>
                        <div class="genre-link" href="<?php echo $genre_link; ?>"><?php echo $genre->name; ?></div>
                    <?php endif; endforeach; ?>
                </span>
            </div><br>
        <?php endif; ?>

        <?php if (current_user_can('administrator')) { ?>
        <?php } ?>
        
        <div class="description">
            <?php
            $content = has_excerpt() ? get_the_excerpt() : get_the_content();
            $words = explode(' ', strip_tags($content));
            $truncated = implode(' ', array_slice($words, 0, 90));
            ?>
            <div class="description-content collapsed">
                <?php echo $truncated; ?>
            </div>
            <?php if (count($words) > 90) : ?>
                <button class="view-all-btn">
                    <i class="fa fa-chevron-down"></i>
                </button>
            <?php endif; ?>
            <?php
            $sponsored_options = get_option('sponsored_content_settings');
            if (isset($sponsored_options['enable_manga_content']) && $sponsored_options['enable_manga_content']) {
                $link = !empty($sponsored_options['link_manga_content']) ? esc_url($sponsored_options['link_manga_content']) : '#';
                $image = !empty($sponsored_options['image_manga_content']) ? esc_url($sponsored_options['image_manga_content']) : ''; // Default to empty
                if (!empty($image)) { // Only display if image is set
                ?>
                <a href="<?php echo $link; ?>" target="_blank">
                    <img src="<?php echo $image; ?>" alt="Sponsored Content" style="max-width: 100% !important; width: 100%;" />
                </a>
            <?php }
            } ?>
        </div>
        <div class="manga-stats">
            <div class="stat-item">
                <div class="stat-details">
                    <span class="stat-label">Type</span>
                    <span class="manga">
                        <?php echo $manga_meta_type ?: 'Unknown'; ?>
                    </span>
                </div>
                <img class="m-icons" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/m-icons/type.svg" alt="Type">
            </div>
            
            <div class="stat-item">
                <div class="stat-details">
                    <span class="stat-label">État du titre</span>
                    <span class="manga">
                        <?php 
                        $status = get_post_meta(get_the_ID(), '_wp_manga_status', true) ?: 'Ongoing';
                        // Normalize status text
                        $status = str_replace(
                            ['on-going', 'On-going', 'end'], 
                            ['En cours', 'En cours', 'Terminé'], 
                            $status
                        );
                        echo $status;
                        ?>
                    </span>
                </div>
                <img class="m-icons" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/m-icons/title-status.svg" alt="État du titre">
            </div>

            <div class="stat-item">
                <div class="stat-details">
                    <span class="stat-label">Édition</span>
                    <span class="stat-value">Scantrad</span>
                </div>
                <img class="m-icons" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/m-icons/transfer-stats.svg" alt="Éditeur">
            </div>

            <?php
            // Determine the format based on the manga type
            $format = 'Unknown';
            if (in_array($manga_meta_type, ['Manhwa', 'Manhua', 'Comic'])) {
                $format = 'Coloré';
            } elseif ($manga_meta_type === 'Manga') {
                $format = 'N/B';
            }
            ?>

            <div class="stat-item">
                <div class="stat-details">
                    <span class="stat-label">Format</span>
                    <span class="stat-value"><?php echo $format; ?></span>
                </div>
                <img class="m-icons" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/m-icons/format.svg" alt="Format">
            </div>

            <?php 
            // Ensure these variables are defined as in sidebar.php
            $total_votes = get_post_meta(get_the_ID(), '_manga_total_votes', true);
            $total_votes = !empty($total_votes) ? floatval($total_votes) : 0;

            $user_votes = get_post_meta(get_the_ID(), '_manga_user_votes', true);
            $user_votes = is_array($user_votes) ? $user_votes : array();
            $vote_count = count($user_votes);
            ?>
<div class="stat-item">
    <div class="stat-details">
        <span class="stat-label"><?php echo esc_html($vote_count); ?> Votes</span>
        <div class="rating-section">
            <div class="star-rating" data-post-id="<?php echo esc_attr($post_id); ?>">
                <div class="stars-display">
                    <?php
                    // Each star now represents 2 points (2,4,6,8,10)
                    for ($i = 2; $i <= 10; $i += 2) {
                        $star_index = $i / 2;
                        if ($total_votes >= $i) {
                            echo '<i class="fas fa-star" data-rating="' . esc_attr($i) . '"></i>';
                        } else if ($total_votes >= ($i - 1)) {
                            echo '<i class="fas fa-star-half-alt" data-rating="' . esc_attr($i) . '"></i>';
                        } else {
                            echo '<i class="far fa-star" data-rating="' . esc_attr($i) . '"></i>';
                        }
                    }
                    ?>
                </div>
                <span class="vote-count"><?php echo esc_html(number_format($total_votes, 1)); ?></span>
            </div>
        </div>
    </div>
    <img class="m-icons" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/m-icons/reviews.svg" alt="Reviews">
</div>

            <div class="stat-item">
                <div class="stat-details">
                    <span class="stat-label">Auteur</span>
                    <span class="stat-value">
                        <?php 
                        if (!empty($authors)) {
                            foreach ($authors as $index => $author) {
                                echo esc_html($author->name);
                                if ($index < count($authors) - 1) echo ', ';
                            }
                        } else {
                            echo 'Unknown';
                        }
                        ?>
                    </span>
                </div>
                <img class="m-icons" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/m-icons/author.svg" alt="Auteur">
            </div>

            <div class="stat-item">
                <div class="stat-details">
                    <span class="stat-label">Artiste</span>
                    <span class="stat-value">
                        <?php 
                        if (!empty($artists)) {
                            foreach ($artists as $index => $artist) {
                                echo esc_html($artist->name);
                                if ($index < count($artists) - 1) echo ', ';
                            }
                        } else {
                            echo 'Unknown';
                        }
                        ?>
                    </span>
                </div>
                <img class="m-icons" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/m-icons/artist.svg" alt="Artiste">
            </div>

            <div class="stat-item">
                <div class="stat-details">
                    <span class="stat-label">Sortie</span>
                    <span class="stat-value">
                        <?php 
                        if (!empty($releases)) {
                            foreach ($releases as $index => $release) {
                                echo esc_html($release->name);
                                if ($index < count($releases) - 1) echo ', ';
                            }
                        } else {
                            echo 'Unknown';
                        }
                        ?>
                    </span>
                </div>
                <img class="m-icons" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/m-icons/release.svg" alt="Sortie">
            </div>
        </div>
        
        <div class="main-inner manga-bottom">
                <aside class="content">
                    <section class="m-list">
                        <div class="tab-content" >
                            <?php include get_stylesheet_directory() . '/template-parts/manga-single/chapters.php'; ?>
                        </div>
                        <div class="tab-content" style="display: none;">
                            <?php include get_stylesheet_directory() . '/template-parts/manga-single/volumes.php'; ?>
                        </div>
                    </section>
                    <section class="default-style">
                        <div class="body p-4">
                            <?php do_action( 'wp_manga_discussion' ); ?>
                        </div>
                    </section>
                </aside>
            </div>
    </div>
</aside>
<script type="text/javascript">
    var ajax_manga_params = {
        ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
        nonce: '<?php echo wp_create_nonce('manga_nonce'); ?>',
        is_logged_in: <?php echo is_user_logged_in() ? 'true' : 'false'; ?>
    };
    document.addEventListener('DOMContentLoaded', function() {
        const viewAllBtn = document.querySelector('.view-all-btn');
        if (viewAllBtn) {
            viewAllBtn.addEventListener('click', function() {
                const content = document.querySelector('.description-content');
                content.classList.toggle('expanded');
                const icon = viewAllBtn.querySelector('i');
                icon.classList.toggle('fa-chevron-down');
                icon.classList.toggle('fa-chevron-up');
                if (content.classList.contains('expanded')) {
                    content.innerHTML = `<?php echo $content; ?>`;
                } else {
                    content.innerHTML = `<?php echo $truncated; ?>`;
                }
            });
        }
    });
</script>