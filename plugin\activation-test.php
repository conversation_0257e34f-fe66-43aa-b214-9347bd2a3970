<?php
/**
 * Simple activation test for WP Manga Subscription
 * This file helps verify that the plugin can be activated safely
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test basic plugin requirements
 */
function wp_manga_subscription_activation_test() {
    $results = array();
    
    // Test 1: Check PHP version
    $results['php_version'] = array(
        'test' => 'PHP Version >= 7.4',
        'result' => version_compare(PHP_VERSION, '7.4', '>='),
        'value' => PHP_VERSION
    );
    
    // Test 2: Check WordPress version
    global $wp_version;
    $results['wp_version'] = array(
        'test' => 'WordPress Version >= 5.0',
        'result' => version_compare($wp_version, '5.0', '>='),
        'value' => $wp_version
    );
    
    // Test 3: Check WP Manga Core
    $results['wp_manga_core'] = array(
        'test' => 'WP Manga Core Available',
        'result' => class_exists('WP_MANGA'),
        'value' => class_exists('WP_MANGA') ? 'Available' : 'Missing'
    );
    
    // Test 4: Check required files
    $required_files = array(
        'admin/dbsetup.php',
        'admin/subscription-manager.php',
        'admin/backend.php',
        'admin/reporter.php',
        'inc/shortcodes.php',
        'inc/helper.php',
        'inc/subscription-helper.php',
        'templates/modal-subscribe.php',
        'templates/user-settings-subscription.php'
    );
    
    $missing_files = array();
    foreach ($required_files as $file) {
        if (!file_exists(WP_MANGA_CHAPTER_COIN_DIR . $file)) {
            $missing_files[] = $file;
        }
    }
    
    $results['required_files'] = array(
        'test' => 'Required Files Present',
        'result' => empty($missing_files),
        'value' => empty($missing_files) ? 'All files present' : 'Missing: ' . implode(', ', $missing_files)
    );
    
    // Test 5: Check database connection
    global $wpdb;
    $results['database'] = array(
        'test' => 'Database Connection',
        'result' => $wpdb->check_connection(),
        'value' => $wpdb->check_connection() ? 'Connected' : 'Failed'
    );
    
    // Test 6: Check memory limit
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    $results['memory'] = array(
        'test' => 'Memory Limit >= 128MB',
        'result' => $memory_limit >= (128 * 1024 * 1024),
        'value' => ini_get('memory_limit')
    );
    
    // Test 7: Check if classes can be instantiated
    try {
        if (class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
            $manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
            $class_test = true;
        } else {
            $class_test = false;
        }
    } catch (Exception $e) {
        $class_test = false;
    }
    
    $results['class_instantiation'] = array(
        'test' => 'Classes Can Be Instantiated',
        'result' => $class_test,
        'value' => $class_test ? 'Success' : 'Failed'
    );
    
    return $results;
}

/**
 * Display activation test results
 */
function wp_manga_subscription_show_activation_test() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $results = wp_manga_subscription_activation_test();
    $all_passed = true;
    
    echo '<div style="background: #fff; padding: 20px; margin: 20px; border: 1px solid #ccc; border-radius: 5px;">';
    echo '<h2>🧪 WP Manga Subscription - Activation Test</h2>';
    
    echo '<table style="width: 100%; border-collapse: collapse;">';
    echo '<thead>';
    echo '<tr style="background: #f0f0f0;">';
    echo '<th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Test</th>';
    echo '<th style="padding: 10px; text-align: center; border: 1px solid #ddd;">Result</th>';
    echo '<th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Value</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($results as $key => $test) {
        $status = $test['result'] ? '✅ Pass' : '❌ Fail';
        $row_color = $test['result'] ? '#f0fff0' : '#fff0f0';
        
        if (!$test['result']) {
            $all_passed = false;
        }
        
        echo '<tr style="background: ' . $row_color . ';">';
        echo '<td style="padding: 10px; border: 1px solid #ddd;">' . esc_html($test['test']) . '</td>';
        echo '<td style="padding: 10px; text-align: center; border: 1px solid #ddd;">' . $status . '</td>';
        echo '<td style="padding: 10px; border: 1px solid #ddd;">' . esc_html($test['value']) . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
    
    echo '<div style="margin-top: 20px; padding: 15px; border-radius: 5px; ' . 
         ($all_passed ? 'background: #d4edda; color: #155724; border: 1px solid #c3e6cb;' : 'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;') . '">';
    
    if ($all_passed) {
        echo '<h3>🎉 All Tests Passed!</h3>';
        echo '<p>Your system meets all requirements for WP Manga Subscription. The plugin should activate without issues.</p>';
    } else {
        echo '<h3>⚠️ Some Tests Failed</h3>';
        echo '<p>Please resolve the failed tests before activating the plugin to avoid errors.</p>';
    }
    
    echo '</div>';
    
    // Show current plugin status
    echo '<div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 5px;">';
    echo '<h3>📊 Current Plugin Status</h3>';

    // Check development mode
    $dev_mode = defined('WP_MANGA_SUBSCRIPTION_DEV_MODE') && WP_MANGA_SUBSCRIPTION_DEV_MODE;
    echo '<p><strong>Development Mode:</strong> ' . ($dev_mode ? '✅ Enabled (License check bypassed)' : '❌ Disabled') . '</p>';

    if (!$dev_mode) {
        $license_key = get_option(WP_MANGA_CHAPTER_COIN_LICENSE_KEY);
        echo '<p><strong>License Key:</strong> ' . ($license_key ? '✅ Set' : '❌ Not Set') . '</p>';
    }

    $db_version = get_option('wp_manga_chapter_coin_db_ver', '');
    echo '<p><strong>Database Setup:</strong> ' . ($db_version ? '✅ Version ' . $db_version : '❌ Not Setup') . '</p>';

    $plugin_active = class_exists('WP_MANGA_ADDON_CHAPTER_COIN');
    echo '<p><strong>Plugin Class:</strong> ' . ($plugin_active ? '✅ Loaded' : '❌ Not Loaded') . '</p>';

    // Check subscription manager
    $subscription_manager_active = class_exists('WP_MANGA_SUBSCRIPTION_MANAGER');
    echo '<p><strong>Subscription Manager:</strong> ' . ($subscription_manager_active ? '✅ Available' : '❌ Not Available') . '</p>';

    // Check database tables
    global $wpdb;
    $plans_table = $wpdb->prefix . 'manga_subscription_plans';
    $subscriptions_table = $wpdb->prefix . 'manga_user_subscriptions';

    $plans_exists = $wpdb->get_var("SHOW TABLES LIKE '$plans_table'") == $plans_table;
    $subscriptions_exists = $wpdb->get_var("SHOW TABLES LIKE '$subscriptions_table'") == $subscriptions_table;

    echo '<p><strong>Subscription Tables:</strong> ' .
         ($plans_exists && $subscriptions_exists ? '✅ Created' : '❌ Missing') . '</p>';

    if ($plans_exists && $subscription_manager_active) {
        try {
            $manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
            $plans = $manager->get_subscription_plans();
            echo '<p><strong>Available Plans:</strong> ' . count($plans) . ' plan(s)</p>';
        } catch (Exception $e) {
            echo '<p><strong>Available Plans:</strong> ❌ Error loading plans</p>';
        }
    }

    echo '</div>';
    
    echo '</div>';
}

// Add test page to admin menu
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            'tools.php',
            'Manga Subscription Activation Test',
            'Manga Activation Test',
            'manage_options',
            'manga-subscription-activation-test',
            'wp_manga_subscription_show_activation_test'
        );
    }
});

// Show quick status in admin notices
add_action('admin_notices', function() {
    // Check if user functions are available
    if (!function_exists('current_user_can')) {
        return;
    }

    if (current_user_can('manage_options') && isset($_GET['page']) && $_GET['page'] === 'manga-subscription-activation-test') {
        return; // Don't show notice on the test page itself
    }

    if (current_user_can('manage_options')) {
        $results = wp_manga_subscription_activation_test();
        $failed_tests = array_filter($results, function($test) {
            return !$test['result'];
        });
        
        if (!empty($failed_tests)) {
            echo '<div class="notice notice-warning">';
            echo '<p><strong>WP Manga Subscription:</strong> Some system requirements are not met. ';
            echo '<a href="' . admin_url('tools.php?page=manga-subscription-activation-test') . '">Run Activation Test</a>';
            echo '</p></div>';
        }
    }
});
