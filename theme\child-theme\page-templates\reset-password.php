<?php
/*
Template Name: Reset Password
*/

if (is_user_logged_in()) {
    wp_redirect(home_url());
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['reset_password'])) {
    $user = get_user_by('email', $_POST['email']);
    if ($user) {
        $reset_key = get_password_reset_key($user);
        $reset_link = network_site_url("wp-login.php?action=rp&key=$reset_key&login=" . rawurlencode($user->user_login));
        wp_mail($_POST['email'], 'Réinitialiser le mot de passe', 'Cliquez ici pour réinitialiser votre mot de passe : ' . $reset_link);
        $message = 'Vérifiez votre e-mail pour le lien de réinitialisation du mot de passe.';
    } else {
        $error_message = 'Aucun utilisateur n\'a été trouvé avec cette adresse e-mail.';
    }
}

get_header(); ?>

<main class="">
    <div class="container mt-5 d-flex flex-column align-items-center">
        <h1 class="text-white">Réinitialiser le mot de passe</h1>
        <div class="default-style p-4 mt-5 max-sm w-100">
            <form method="post" class="ajax" action="">
                <?php if (!empty($message)) { echo '<p class="success">' . $message . '</p>'; } ?>
                <?php if (!empty($error_message)) { echo '<p class="error">' . $error_message . '</p>'; } ?>
                <div class="form-group"><input type="email" class="form-control" name="email" placeholder="Votre email" required /></div>
                <button class="btn w-100 btn-lg btn-primary" name="reset_password">Réinitialiser</button>
            </form>
        </div>
    </div>
</main>

<?php get_footer(); ?>