/*
Theme Name: Raijin <PERSON> theme
Description:  A child theme for Madara - WordPress Theme for Manga, Novel sites
Author:       7thCloud
Author URI:   https://raijinscan.fr/
Template: madara
Tags: one-column, two-columns, right-sidebar, custom-header, custom-menu, editor-style, featured-images, microformats, post-formats, rtl-language-support, sticky-post, translation-ready
Version: 1.5.2
*/

/*--------------------------------------------------------------
>>> LTR CSS
----------------------------------------------------------------*/
@charset "UTF-8";
:root {
  /* Background Colors */
  --color-1: #000000 !important; /* background (darkest) */
  --bgc: #0c0c0c !important; /* background color */
  --bgcards: #111111 !important; /* sections background colors */
  --color-3: #151515 !important; /* popover */

  /* Border & Button Colors */
  --secb: #171717 !important; /* borders and secondary buttons */
  --color-7: #63aff5 !important; /* Main Color Buttons*/

  /* Text Colors */
  --pt: #646464 !important; /* paragraph text */
  --icol: #606060 !important; /* icons color */
  --color-6: #475569 !important; /* accent */
  --color-10: #717171 !important; /* accent foreground */
  --color-11: #e2e8f0 !important; /* foreground text */
}
body,
html {
  scrollbar-color: #222222 #ffffff00;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
}
#manga-discussion > h4,
.logo:after {
  display: none;
}
h2 {
  font-size: 1.65rem;
  display: flex;
  align-items: center;
  margin: 0;
  color: rgb(255 255 255 / 0.8);
}
p {
  margin: 0 !important;
}
.hidden {
  display: none !important;
}
.overflow-hidden {
  overflow: hidden;
}
#recently-up-ajax .unit .poster > div,
.h-full {
  height: 100%;
}
#continue-reading .unit {
  width: 14.36%;
}
#ch-images {
  width: 697px;
  border-radius: 16px;
}
#ch-images img {
  width: 100%;
}
#change-width span,
#nav-menu,
#nav-menu > ul > li > ul,
#nav-search,
#nav-search .search-inner form input,
.dir-ltr,
.nav-user .u-menu .user-ul,
.sub-panel .head form .form-group input,
footer .abs-footer .wrapper span:first-of-type {
  direction: ltr;
}
.tab-list {
  display: flex;
  gap: 4px;
}
.tab-list .tab {
  margin: 0 !important;
  background: #111111;
  padding: 4px 8px;
  border-radius: 8px;
}
.tab-list .tab.active {
  background: #2b2b2b;
  color: #afafaf;
}
.justify-center {
  display: flex;
  justify-content: center !important;
}
#ctrl-menu {
  background: #131313;
}
#ctrl-menu .ts-name div p,
form {
  margin-bottom: 0;
}
.pb-7rm {
  padding-bottom: 7rem;
}
p.text-muted {
  margin-bottom: 10px !important;
}
.max-md {
  max-width: 700px;
}
.text-sm {
  font-size: 0.875rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.pb-5 {
  padding-bottom: 1.25rem;
}
.first\:pt-4:first-child {
  padding-top: 1rem;
}
.text-danger {
  color: #dc3545;
}
.font-bold {
  font-weight: 700;
}
.list-disc {
  list-style-type: disc;
}
.ml-6 {
  margin-left: 1.5rem;
}
body > span.bg {
  background: none !important;
}
#nav-search .search-inner .x-suggestion {
  display: none;
  top: 100%;
  width: 100%;
  position: absolute;
  background: #131313;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  overflow: hidden;
  padding: 1rem 0 0;
  margin-top: -1rem;
  z-index: 1;
  border-radius: 0 0 0.5rem 0.5rem;
  border: 1px solid #1e2c43;
}
#nav-search .search-inner .x-suggestion > div:last-child {
  padding: 1rem;
}
#nav-search .search-inner form > button > span {
  margin-left: 0.2rem;
}
button.btn-primary2 {
  flex-shrink: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  height: 1.85rem !important;
  border-radius: 50rem !important;
  margin-left: 0.3rem !important;
  background-color: #f15062 !important;
  border-color: #f15062 !important;
}
button.btn-primary2:hover {
  background-color: #f4c60f !important;
  border-color: #f4c60f !important;
}
main {
  background: none;
}
.manga-detail .main-inner .sidebar .rating-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
}
.manga-detail .main-inner .sidebar .rating-box .author-info {
  max-width: calc(100% - 5rem);
  overflow: hidden;
}
.manga-detail .main-inner .sidebar .rating-box .author-name {
  white-space: nowrap;
}
.manga-detail .main-inner .sidebar .rating-box .author-avatar img {
  border-radius: 10%;
}
.manga-detail .hposter {
  border-radius: 16px 16px 0 0;
}
#ctrl-menu .ts-name {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}
#ctrl-menu .ts-name div b {
  font-size: 1.2rem;
  color: #8bbadd;
}
#ctrl-menu .ts-name i {
  font-size: 1.4rem;
  transition: transform 0.7s;
}
#ctrl-menu .ts-name:hover i {
  transform: rotate(360deg) scale(1.3);
}
#page-wrapper-x > div {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.nav-user .u-notify .dropdown-menu {
  width: 300px;
}
.dropdown-menu {
  --menu-bg: #111;
  --item-hover-bg: rgba(255, 255, 255, 0.05);
  --transition-timing: cubic-bezier(0.34, 1.56, 0.64, 1);

  transform-origin: top center;
  position: absolute !important;
  top: 100% !important;
  right: 0 !important;
  left: auto !important;
  margin-top: 10px;

  opacity: 0;
  visibility: hidden;
  transform: translateY(-20px) scale(0.95) !important;
  background: var(--menu-bg);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 8px;
  z-index: 1000;
  transition: all 0.3s var(--transition-timing);
}

.dropdown-menu::before {
  content: "";
  position: absolute;
  top: -4px;
  right: 20px;
  width: 8px;
  height: 8px;
  background: var(--menu-bg);
  transform: rotate(45deg);
  border-radius: 2px;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1) !important;
}

.dropdown-menu .dropdown-item {
  padding: 10px 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dropdown-menu .dropdown-item:hover {
  background: var(--item-hover-bg);
  transform: translateX(4px);
}

.dropdown-menu .dropdown-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.03),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.dropdown-menu .dropdown-item:hover::before {
  transform: translateX(100%);
}

/* Optional: Add hover states for menu items */
.dropdown-menu .dropdown-item:active {
  transform: scale(0.98);
}

/* Optional: Add focus styles */
.dropdown-menu .dropdown-item:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}
.inner {
  transition: transform 0.3s;
}
.inner:hover {
  scale: 1.05;
  transition: scale 0.3s;
}
.original.card-lg .unit {
  width: 14.25%;
}
.original.card-lg .unit .inner {
  transition: scale 0.3s;
}
.original.card-lg .unit .inner:hover {
  scale: 1.05;
  transition: scale 0.3s;
}
.original.card-lg .unit .inner .info > div {
  color: #d1d1d1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  font-size: 1rem;
  line-height: 1.3;
}
.user-panel .manga-bottom {
  margin-top: 0;
}
.user-panel .manga-bottom .content .m-list .vol-list,
.user-panel .manga-bottom .content .m-list ul {
  max-height: none;
}
.user-panel .manga-bottom .content .m-list,
.user-panel .manga-bottom .content .m-list .vol-list {
  background: 0 0;
}
.user-panel .manga-bottom .content .m-list .vol-list {
  padding: 0;
}
.user-panel .main-inner {
  gap: 24px;
}
.user-panel .main-inner .manga-bottom .content {
  margin-left: 0;
}
.thumb-prev {
  display: flex;
  justify-content: center;
}
.thumb-prev img {
  width: 280px;
  height: 400px;
  object-fit: cover;
}
@media (max-width: 1199.98px) {
  #filters > div:last-child > div:last-child {
    width: 50%;
  }
  #filters > div:last-child > div {
    width: 25%;
  }
}
@media only screen and (min-width: 800px) {
  .bhs-2 span {
    display: inline !important;
  }
}
@media only screen and (max-width: 800px) {
  .bhs,
  .bhs-2 {
    height: 40px !important;
  }
  .bhs-2 {
    font-size: 1.3rem !important;
  }
  .manga-detail .main-inner .content .actions .btn.bhs-2 i {
    margin-left: 0 !important;
  }
}
.form-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}
a.dropdown-item.disabled {
  opacity: 0.3;
  cursor: not-allowed !important;
}
.up-chts .unit .inner .info > p {
  color: #c6cacf;
}
.up-chts .unit .inner .info > .type {
  color: #3c8bc6;
}
.original.card-lg .unit.private .inner {
  background: #9f28284d;
  border: 1px solid #b62c2c45;
}
.swiper-inner {
  position: relative;
  width: 100%;
  height: 464px; /* Full viewport height */
  overflow: hidden;
}
/*Rating*/
.star-rating {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.stars-display {
  display: inline-flex;
  gap: 2px;
  font-size: 1.7rem;
}

.stars-display i {
  color: #ffd700 !important;
  cursor: pointer;
  transition: transform 0.2s;
}

.stars-display i:hover {
  transform: scale(1.1);
}

.vote-count {
  color: #666;
  font-size: 0.9em;
  margin-left: 5px;
}
/*Banner*/
.banner-wrapper {
  position: relative;
  width: 100%;
  height: 100%; /* Full height of parent */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Rest of the CSS remains the same */
.banner-wrapper::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70%;
  background: linear-gradient(
    to top,
    rgb(12 12 12 / 82%) 25%,
    rgba(0, 0, 0, 0) 100%
  );
}
.status-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  background-color: #4caf50; /* Green color */
  border-radius: 50%;
  display: inline-block;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.flag-icon {
  width: 33px;
  height: 25px;
  display: block;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .above
  > a {
  margin-top: 4px;
  font-size: 1.3rem;
  z-index: 5;
  font-weight: 600;
  position: relative;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  margin-bottom: 4px;
}
#top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner .info {
  position: absolute;
  bottom: 0;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .below
  > p {
  margin: 0.8rem 0 !important;
}
.dropdown-menu.dropdown-menu-right.user-ul.show {
  will-change: transform;
}
.manga-detail .main-inner .content .info > h6 {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.manga-detail .main-inner .content .info > h6::-webkit-scrollbar {
  display: none;
}
.container .alert {
  margin-bottom: 2rem;
}
.card-md .unit a > span,
.container.mt-5.d-flex.flex-column.align-items-center.max-md h1,
.home-swiper .swiper .swiper-wrapper .swiper-slide > a > span,
.manga-detail .main-inner .content .info > h1,
.original.card-lg .unit .inner .info > a,
.original.card-lg .unit .inner .info > div,
footer,
header {
  direction: ltr;
}
.wrapper header {
  width: 100%;
}
#addMangaForm,
#ctrl-menu .ts-name div,
#nav-menu > ul > li > ul,
#nav-search .search-inner form input,
#profile-form,
#responseMessage,
#synopsis.modal,
#top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner .info,
.container.mt-5.d-flex.flex-column.align-items-center.max-md,
.manga-detail .main-inner .content .info,
.manga-detail .main-inner .sidebar,
.modal-content,
.nav-user .u-menu .user-ul,
.original.card-lg .unit .inner .info > a,
.original.card-lg .unit .inner .info > div,
.original.card-lg.reading,
.original.card-lg.reading .unit .inner .info,
.original.card-sm .unit .info,
.sub-panel .head form .form-group input,
.up-chts .original.card-lg,
.user-nav li a i,
textarea.form-control {
  text-align: left;
}
.grx div {
  background: #171717;
  padding: 6px 8px;
  color: #898989;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}
.grx div:hover {
  background: #2c2c2c;
  color: #afafaf;
}
span.grx {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  background: #e4e4e7;
  border-radius: 8px;
  outline: none;
  padding: 0;
  margin: 8px 0;
  cursor: pointer;
  position: relative;
  background-image: linear-gradient(#f4c60f, #f4c60f);
  background-size: var(--range-progress, 50%) 100%;
  background-repeat: no-repeat;
}

/* Webkit (Chrome, Safari, Edge) */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #f4c60f;
  border-radius: 50%;
  border: 2px solid #ffffff;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 8px rgba(0, 111, 238, 0.2);
  margin-top: -7px; /* Centers the thumb on the track */
}

input[type="range"]::-webkit-slider-thumb:hover {
  background: #cba50a;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 111, 238, 0.3);
}

input[type="range"]::-webkit-slider-thumb:active {
  transform: scale(0.95);
  background: #af8e06;
}

/* Firefox */
input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #f4c60f;
  border-radius: 50%;
  border: 2px solid #ffffff;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 8px rgba(0, 111, 238, 0.2);
}

input[type="range"]::-moz-range-thumb:hover {
  background: #cba50a;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 111, 238, 0.3);
}

input[type="range"]::-moz-range-thumb:active {
  transform: scale(0.95);
  background: #cba50a;
}

/* Track styling */
input[type="range"]::-webkit-slider-runnable-track {
  width: 100%;
  height: 6px;
  cursor: pointer;
  background: transparent;
  border-radius: 8px;
}

input[type="range"]::-moz-range-track {
  width: 100%;
  height: 6px;
  cursor: pointer;
  background: transparent;
  border-radius: 8px;
}

/* Focus state */
input[type="range"]:focus {
  outline: none;
}

input[type="range"]:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px rgba(0, 111, 238, 0.2);
}

input[type="range"]:focus::-moz-range-thumb {
  box-shadow: 0 0 0 3px rgba(0, 111, 238, 0.2);
}
.head a,
.cancel-button {
  background: transparent;
  padding: 6px 10px;
  border-radius: 8px;
  color: #5f5f5f;
  gap: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid #1d1d1d;
}
.head a:hover,
.cancel-button:hover {
  background: #242424;
  color: #fff;
}
.manga-detail .detail-bg {
  display: none;
}
.sub-panel ul li a {
  justify-content: flex-end;
}
#filters > div:last-child > div.search:after,
.modal-dialog .modal-content .modal-close {
  right: 1rem;
}

#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="checkbox"]
  + label:before,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="radio"]
  + label:before {
  margin-right: 0.5rem;
  margin-left: 0.2rem;
}
#filters > div:last-child > div {
  width: 14.25%;
}

.user-panel .main-inner > .content {
  margin: 0 1.5rem 0 0;
}
.user-nav li a {
  padding: 0.7rem 1.4rem;
}
#nav-search .search-inner form {
  padding-left: 6px;
  padding-right: 0.4rem;
}
#nav-search .search-inner form i {
  margin-left: 0.25rem;
}

#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner:hover
  .poster {
  transform: rotate(0) scale(1) translate(0, 0);
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .below
  > div
  a
  + a,
.manga-detail .main-inner .content .actions .up-chapter-btn {
  margin-left: 0;
  margin-right: 0.6rem;
}
#top-trending .trending-button-prev {
  left: auto;
  right: 0;
  border-radius: 0.5rem 0 0 0.5rem;
}
#top-trending .trending-button-next {
  right: auto;
  left: 0;
  border-radius: 0 0.5rem 0.5rem 0;
}
#top-trending .trending-button-prev:after,
.fa-angle-left:before {
  content: "\f105";
}
#top-trending .trending-button-next:after,
.fa-angle-right:before {
  content: "\f104";
}

.fa-chevron-square-right:before,
.fa-square-chevron-right:before {
  content: "\f32a";
}
.fa-chevron-square-left:before,
.fa-square-chevron-left:before {
  content: "\f32b";
}
.tabs .s-pagi > div + div {
  margin-right: 0.5rem;
}

@media (max-width: 991.98px) {
  footer .abs-footer .wrapper {
    flex-direction: column;
  }
}
.hposter {
  width: 20%;
  height: 100%;
}
.manga-detail .main-inner .content .info {
  width: 80%;
}
@media (max-width: 767.98px) {
  .manga-detail .main-inner .content .info {
    padding-right: 0;
    text-align: center;
  }
  .manga-detail .main-inner .content .info > h1 {
    margin: 0.6rem 0;
  }
}
.manga-detail .main-inner .content .poster > div {
  overflow: hidden !important;
  border-radius: 0.3rem;
  display: flex;
  gap: 8px;
}
.manga-bottom .content .m-list .chapvol-tab > a,
.manga-detail .main-inner .content .info > p {
  letter-spacing: 1px;
}

.manga-detail .main-inner .content .actions .btn > i {
  margin-left: 0;
  margin-right: 0.35rem;
}
.manga-detail .main-inner .sidebar .meta > div {
  display: inline;
}
.manga-bottom .content .m-list ul li a span:first-child:before,
.sub-panel ul li a.active:before,
.sub-panel ul li a:hover:before {
  display: none;
}
.manga-bottom .content .m-list ul li a span i {
  margin-right: 0;
  font-weight: 900;
  transition: margin 0.3s, opacity 0.3s, font-size 0.3s;
  opacity: 0;
  font-size: 0;
}
.manga-bottom .content .m-list ul li a:hover span i {
  opacity: 1;
  margin-right: 0.5rem;
  font-size: 0.8rem;
}
.manga-bottom .content .m-list ul li a:hover {
  background: #191919;
}
.manga-bottom .content .m-list ul li a:visited {
  background: #0b0b0b;
}
.manga-bottom .content .m-list ul li a span {
  display: flex;
  align-items: center;
  gap: 5px;
}
.manga-bottom .content .m-list .list-menu form {
  height: 100%;
}
.manga-bottom .content .m-list .list-menu form input {
  padding: 0.4rem 0 0.4rem 0.8rem !important;
}
.manga-bottom .content .m-list .list-menu form input::placeholder {
  color: #474747; /* Replace with your desired color */
  opacity: 1; /* Ensures full opacity for the placeholder */
}
.form-control::placeholder {
  color: #5a5a5a !important;
}
.original.card-sm .unit .poster {
  margin-right: 0.8rem;
}
/*.custom-file-label::after {
  right: auto;
  left: 0;
  content: "\0625\062e\062a\0631\0020\0645\0644\0641";
  border-radius: 0.5rem 0 0 0.5rem;
}*/
.form-control::placeholder {
  color: #5b6775;
}
.custom-control-label::after,
.custom-control-label::before {
  left: auto;
  right: -1.5rem;
}
.control-name {
  margin-bottom: 5px;
}
i.rtl-icon.fa-light.fa-arrow-right.ml-1 {
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
}
@media (max-width: 1199.98px) {
  #filters > div:last-child > div {
    width: 25%;
  }
  #filters > div:last-child > div:last-child {
    width: 100%;
  }
}
.sub-panel .head {
  padding: 0.5rem 1.5rem 0.5rem 1rem;
}

.user-panel .card-md.vol-list.scroll-sm p {
  line-height: 1.8rem;
  text-align: center;
}
p.s-p {
  text-align: center;
  margin: 40px 0 15px !important;
  font-size: 15px;
}
#top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner .bookmark {
  left: 0;
}

footer .container {
  direction: rtl;
}
footer .inner nav {
  text-align: left;
}
footer nav ul {
  direction: ltr;
}
footer .wrap .inner div {
  text-align: left;
}
footer .wrap .inner div p {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
}

.home-swiper {
  max-width: 1444px;
  margin: 20px auto;
  padding: 0 15px;
}

#modal-close {
  text-align: left;
}
section#most-viewed {
  padding: 0;
}
/* Modal responsive fixes */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 0.5rem;
    width: 95%;
    max-width: none;
  }

  .modal-content {
    width: 100%;
  }

  .modal-body {
    padding: 1rem;
  }

  /* Make range input more touch-friendly */
  input[type="range"] {
    height: 8px; /* Slightly thicker for mobile */
  }

  input[type="range"]::-webkit-slider-thumb {
    width: 24px;
    height: 24px;
  }

  input[type="range"]::-moz-range-thumb {
    width: 24px;
    height: 24px;
  }
}

/* Control menu mobile fixes */
@media (max-width: 768px) {
  #ctrl-menu {
    width: 90%;
    right: -90%;
    font-size: 16px;
  }

  #ctrl-menu.active {
    right: 0;
  }

  #ctrl-menu .jb-btn {
    padding: 12px;
    font-size: 14px;
  }

  #ctrl-menu .jb-btn i {
    font-size: 16px;
  }

  #ctrl-menu nav button {
    padding: 10px;
    font-size: 14px;
  }

  #ctrl-menu .head {
    font-size: 16px;
    padding: 12px;
  }

  #ctrl-menu .ts-name {
    padding: 12px;
    font-size: 14px;
  }
}

/* General mobile improvements */
@media (max-width: 768px) {
  .btn {
    padding: 10px 15px;
  }

  .modal-title {
    font-size: 18px;
  }

  #currentWidth,
  #width-value {
    font-size: 14px;
  }

  .close {
    font-size: 24px;
    padding: 8px;
  }

  /* Ensure images don't overflow */
  #ch-images {
    max-width: 100% !important;
    width: 100% !important;
  }

  #ch-images img {
    max-width: 100%;
    height: auto;
  }
}

/* Additional touch-friendly improvements */
@media (max-width: 768px) {
  .modal-header {
    padding: 15px;
  }

  .gap-2 {
    gap: 1rem !important;
  }

  .mt-3 {
    margin-top: 1rem !important;
  }
}

/* Ensure images don't overflow on mobile */
@media (max-width: 768px) {
  #ch-images {
    max-width: 100% !important;
    width: 100% !important;
    min-width: 0 !important; /* Prevent min-width issues */
  }

  #ch-images img {
    max-width: 100% !important;
    height: auto !important;
    width: 100% !important;
  }

  /* Override any inline styles */
  #page-wrapper-x .pages #ch-images[style] {
    width: 100% !important;
    max-width: 100% !important;
  }
}

@media (max-width: 768px) {
  [data-target="#widthModal"],
  #widthModal {
    display: none !important;
  }

  #ch-images {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* Control menu positioning and mobile styles */
@media (max-width: 1199.98px) {
  body.read #ctrl-menu {
    position: fixed;
    top: 0;
    bottom: 0;
    right: -85%; /* Start off-screen */
    width: 85%;
    max-width: 400px;
    transition: right 0.3s ease;
    z-index: 1000;
    background: #131313;
    padding: 1rem;
    overflow-y: auto;
  }

  body.read #ctrl-menu.active {
    right: 0;
  }

  /* Bigger elements for mobile */
  #ctrl-menu .head {
    font-size: 1.2rem;
    padding: 1rem;
  }

  #ctrl-menu .ts-name {
    padding: 1rem;
    font-size: 1.1rem;
  }

  #ctrl-menu nav button {
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
  }

  #ctrl-menu .jb-btn {
    padding: 1rem;
    font-size: 1.1rem;
  }

  #ctrl-menu .jb-btn i {
    font-size: 1.2rem;
    margin-right: 1rem;
  }

  /* Bigger navigation buttons */
  .number-nav {
    padding: 1rem 0;
  }

  .number-nav a {
    font-size: 1.1rem;
    padding: 1rem 1.5rem;
  }

  .number-nav a i {
    font-size: 1.2rem;
  }
}

/* Additional mobile improvements */
@media (max-width: 768px) {
  /* Chapter content spacing */
  body.read #ctrl-menu {
    right: -85%; /* Start off-screen */
    width: 85%;
    max-width: 85%;
  }
  body.read #ctrl-menu.active {
    right: 0;
    max-width: 85%;
  }

  .number-nav a {
    padding: 1rem 2rem;
    font-size: 1.2rem;
    min-height: 50px;
  }

  .number-nav a i {
    font-size: 1.3rem;
  }

  /* Bigger text elements */
  .chapter-content {
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

/* Login and Register Forms */
.login-form-container,
.register-form-container {
  max-width: 400px;
  margin: 40px auto;
  padding: 20px;
  background: var(--color-3);
  border-radius: 8px;
}

.login-form-container h1,
.register-form-container h1 {
  text-align: center;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
}

.form-group input {
  width: 100%;
  padding: 20px;
  border: 1px solid var(--color-8);
  border-radius: 4px;
  color: var(--color-11);
}
.form-group input::placeholder {
  color: #3e3e3e;
}

.form-links {
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
}

.alert {
  padding: 10px;
  margin-top: 15px;
  border-radius: 4px;
}

.alert-error {
  background: #dc3545;
  color: white;
}

.slider-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #ffffffbd;
  font-size: 12px;
}

.slider-rating i {
  color: #f2ae40;
}

.slider-rating .vote-count {
  font-size: 0.9em;
  opacity: 0.8;
}

.poster-image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.poster-image-wrapper .manga-flag {
  position: absolute;
  top: 4px;
  right: 32px;
  z-index: 2;
}

.poster-image-wrapper .flag-icon {
  width: 28px;
  height: auto;
  border-radius: 3px;
}
/* Shine Effect for Manga Covers */
.poster-image-wrapper.shine-effect {
  position: relative;
  overflow: hidden;
}

.poster-image-wrapper.shine-effect::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  display: block;
  width: 0;
  height: 100%;
  filter: blur(10px);
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 30%,
    rgba(255, 255, 255, 0.3) 100%
  );
  transform: skewX(-25deg);
  transition: width 0.8s;
}

.poster-image-wrapper.shine-effect:hover::before {
  width: 200%;
}
.rating-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 5px;
}

.rating-wrapper i {
  color: #ffd700; /* Gold color for the star */
}

.rating-wrapper span {
  font-size: 14px;
  color: #666;
}
span.ch-num {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
  color: #8e8e8e;
}
span.ch-date {
  font-size: 12px;
  text-align: center;
  color: #606060;
  font-weight: 400;
}
.original.card-lg .unit .inner .info > ul > li {
  width: 50%;
}
.reading .manga-flag {
  right: 4px;
}

.manga-progress {
  margin-top: 8px;
  width: 100%;
}

.progress-text {
  font-size: 12px;
  color: #ccc;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
}

.progress-text .current {
  color: #4caf50; /* Highlight the current chapter */
}
.progress-bar {
  background: rgba(255, 255, 255, 0.1);
  height: 4px;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.progress-fill {
  background: #01a051;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 2px;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.unit {
  position: relative;
}

.inner {
  position: relative;
  cursor: pointer;
}
.poster-image-wrapper img {
  height: 100%;
  width: 100%;
}
.manga-flag.\.most {
  right: 32px !important;
}
.head i.fa-solid {
  font-size: 26px;
}
i.fa-solid.fa-chevron-right {
  font-size: 14px;
}
i.fa-solid.fa-trash {
  font-size: 12px;
}
.fa-solid.fa-xs.fa-arrow-right {
  font-size: 14px;
}
.head-t {
  display: flex;
  gap: 8px;
}

.content li a {
  display: flex;
  flex-direction: column;
}
.info {
  padding: 10px;
}
.serie-info {
  width: 80%;
}
ul.content {
  list-style: none;
  display: flex;
  gap: 6px;
  margin: 4px 0 0 0;
  padding: 0;
}
a.c-title {
  color: #d1d1d1;
  background-color: transparent !important;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-size: 1.05rem;
  line-height: 1.4;
  height: 3rem;
}
.swiper-container .inner .info {
  padding: 10px;
}
.horizontal-scroll {
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  padding-bottom: 10px; /* For scroll bar space */
}
.scroll-content {
  width: 100%;
}
.horizontal-scroll .scroll-content {
  display: flex;
  padding: 0.5rem;
}

.horizontal-scroll::-webkit-scrollbar {
  height: 8px;
}

.horizontal-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.horizontal-scroll::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.horizontal-scroll::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Ensure manga units don't shrink */
.horizontal-scroll .unit {
  flex: 0 0 auto;
  width: 200px; /* Adjust this value based on your design */
}

.last-read {
  font-size: 0.85em;
  color: #666 !important;
  margin: 4px 0;
  display: flex;
  align-items: center;
  gap: 5px;
}
.last-read i {
  font-size: 0.9em;
}
.title {
  color: #d1d1d1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-size: 1.05rem;
  line-height: 1.4;
  height: 3rem;
  margin-bottom: 4px;
}
.genres {
  display: flex;
  justify-content: center;
  gap: 6px;
}
span.genre {
  padding: 4px 6px;
  background: #212121;
  color: #878787;
  border-radius: 4px;
  width: 50%;
  text-align: center;
  font-size: 13px;
}
.slider-flag {
  position: absolute;
  top: 6px;
  right: 6px;
  z-index: 2;
}
.manga-flag {
  position: absolute;
  width: 28px;
  top: 6px;
  left: 6px;
  z-index: 2;
}
.manga-flag img {
  width: 25px;
  height: auto;
}

.poster {
  position: relative;
}
.description-content {
  max-height: 150px;
  overflow: hidden;
  transition: max-height 0.5s ease-in-out, padding 0.5s ease-in-out;
}

.description-content.expanded {
  max-height: 500px;
  padding-bottom: 10px;
}

.view-all-btn {
  display: block;
  margin-top: 14px;
  padding-top: 6px;
  background: transparent;
  border-radius: 8px;
  border: none;
  color: #6b6b6b;
  font-size: 16px;
  width: 100%;
  border-top: 1px solid #282828;
}
.view-all-btn:hover {
  background: #151515;
  color: #979797;
  border-top: 1px solid #1f1f1f;
}
.manga-stats {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 15px 0;
  flex-wrap: wrap;
  gap: 14px;
  margin-top: 20px;
}
.m-icons {
  width: 63px;
  position: absolute;
  right: -15px;
  opacity: 0.1;
  bottom: -8px;
}
.stat-item {
  display: flex;
  width: 24%;
  background: #151515;
  padding: 12px;
  border-radius: 8px;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.stat-item .m-icons {
  width: 63px;
  opacity: 0.1;
  transform: rotate(0deg);
  transition: all 0.3s ease; /* Add transition for non-hover state */
}

.stat-item:hover {
  background: #1f1f1f;
  transition: background 0.3s ease; /* Smooth background transition */
}

.stat-item:hover .m-icons {
  opacity: 0.5;
  width: 77px;
  transform: rotate(-30deg);
  transition: all 0.3s ease;
}
.stat-item i {
  font-size: 1.35rem;
  color: #666;
}

.stat-details {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 14px;
  color: #737373;
}

.stat-value {
  font-weight: 500;
  font-size: 16px;
  color: #d1d1d1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  line-height: 1.4;
}

.hposter {
  position: relative;
  overflow: hidden;
}
.hcontent {
  display: flex;
  flex-direction: row;
  gap: 34px;
}
.cover-background {
  filter: blur(15px);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  height: 70%;
  background-repeat: no-repeat;
  background-size: cover;
}

.main-cover {
  display: block;
  margin: 30px;
  position: relative;
  z-index: 2;
  height: 100%;
  width: 100%;
}
.covers {
  display: flex;
  justify-content: center;
}
.cover {
  border-radius: 12px;
  height: 100%;
  width: 100%;
}
.main-cover .manga-flag img {
  width: 33px;
}
.hposter .actions {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.actions p {
  font-weight: 600;
}
.alternative-title {
  color: #94969c;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  font-size: 1rem;
  line-height: 1.3;
}
.description {
  color: #94969c;
}
.bookmark-button {
  background: #151515 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
.inner .hposter {
  width: 100%;
  height: auto;
}
#top-trending .btn.bookmark-button {
  background: transparent !important;
  border: none;
  transition: font-size 0.3s;
  color: #fff;
  text-shadow: 0 0 0.5rem rgb(134 134 134 / 90%);
  opacity: 0;
  transition: 0.33s ease;
}

#continue-reading {
  max-width: 1444px;
  margin: 0 auto;
  padding: 0 15px;
}
.recently-updated {
  max-width: 1444px;
  margin: 0 auto;
  padding: 0 15px;
}
.serie-title {
  font-size: 28px;
}
footer {
  padding: 60px 0 30px;
  color: #fff;
}
.footer-top {
  width: 1444px;
  margin: 0 auto;
  padding: 48px;
  border-radius: 24px 24px 0 0;
  background: #111;
}
.footer-brand {
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.footer-brand img {
  width: 180px;
}
.footer-brand .tagline {
  color: #575757;
  font-size: 15px;
}

.footer-links {
  display: flex;
  justify-content: left;
  gap: 12px;
  margin-top: 16px;
}

.footer-links a {
  color: #808080;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #fff;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  width: 1444px;
  margin: 0 auto;
  padding: 48px;
}

.copyright {
  color: #808080;
}

.copyright .subtitle {
  font-size: 0.9em;
  margin-top: 10px;
}

.social-links {
  display: flex;
  gap: 20px;
}

/* Social Media Icon Colors - Always Visible */
.social-links a {
  font-size: 1.5em;
  transition: color 0.3s;
}
.social-links a.twitter {
  color: #1da1f2; /* Twitter blue */
}

.social-links a.reddit {
  color: #ff4500; /* Reddit orange */
}

.social-links a.discord {
  color: #5865f2; /* Discord blue */
}

/* Optional hover effect to make icons slightly darker */
.social-links a:hover {
  opacity: 0.8;
}
/* Modal Clear History */
.modal-confirm {
  color: #636363;
}
.modal-confirm .modal-content {
  padding: 20px;
  border-radius: 5px;
  border: none;
}
.modal-confirm .modal-header {
  position: relative;
  text-align: center;
}
.modal-confirm .modal-title {
  text-align: center;
  font-size: 26px;
}
.modal-confirm .modal-footer {
  border: none;
  text-align: center;
  border-radius: 5px;
  font-size: 13px;
}
.modal-confirm .icon-box {
  color: #fff;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #ef5350;
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-confirm .icon-box i {
  font-size: 16px;
}
/* Modal Clear History */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(5px);
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.modal.show {
  opacity: 1;
  display: block;
  z-index: 9999;
}

.modal-content {
  position: relative;
  background-color: #151515;
  margin: 15vh auto; /* Changed from 10% to 15vh for better positioning */
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  transform: translateY(-20px);
  opacity: 0;
  transition: all 0.3s ease-in-out;
  z-index: 1001; /* Added z-index to ensure it's above the backdrop */
}

.modal.show .modal-content {
  transform: translateY(0);
  opacity: 1;
}

.close-modal {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #666;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.2s ease;
}
.search-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
}
.search-results {
  flex: 1;
  transition: height 0.3s ease-in-out;
  overflow: hidden;
}
.results-section {
  transition: height 0.3s ease-in-out;
  min-height: 0;
}
.results-section::-webkit-scrollbar {
  width: 8px;
}

.results-section::-webkit-scrollbar-track {
  background: transparent;
}

.results-section::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.results-section::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
}
.manga-grid {
  transition: opacity 0.2s ease-in-out;
}

.close-modal:hover {
  color: #333;
}

.search-inner {
  position: relative;
}

.search-inner form {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #1a1a1a;
  padding: 15px;
  border-radius: 8px 8px 0 0;
  border: 1px solid #2a2a2a;
}

.search-inner input[type="text"] {
  flex: 1;
  background: transparent;
  border: none;
  color: #fff;
  font-size: 16px;
  padding: 8px;
  outline: none;
}

.search-inner input[type="text"]::placeholder {
  color: #666;
}
#search-results {
  display: flex;
  flex-direction: column;
  padding: 2px;
  gap: 8px;
}
.search-result-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  border-radius: 12px;
  gap: 12px;
  background: #1a1a1a;
}
.search-result-card:hover {
  color: #fff;
  background: #222222;
}
.search-result-card:focus {
  background: #2b2b2b;
}
.thumbnail-wrapper img {
  width: 66px;
  height: auto;
  border-radius: 8px;
}
.view-all-results {
  background: transparent;
  padding: 6px 10px;
  border-radius: 8px;
  color: #5f5f5f;
  gap: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid #1d1d1d;
}
.view-all-results:hover {
  background: #242424;
  color: #fff;
}
.command-bar {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}
.filter-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  color: #666;
  transition: color 0.2s ease;
  margin-left: 8px;
}

.filter-button i {
  font-size: 1.2em;
  margin-right: 4px;
}

.filter-button span {
  font-size: 0.9em;
}
.command-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
}
.search-inner .fa-magnifying-glass {
  color: #666;
  font-size: 16px;
}

.x-suggestion {
  margin-top: 20px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #2a2a2a;
}
li.page-item.active {
  cursor: pointer;
}
.search-result-details {
  color: #9d9d9d;
}
i.fas.fa-star {
  color: #f2ae40;
}
.head h2 {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-result-title {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin: 0;
}
.search-result-card.selected {
  background-color: rgba(0, 0, 0, 0.05);
  outline: 2px solid #007bff;
  animation: pulse 2s infinite;
}
.menu-item a > i {
  font-size: 18px;
}
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(14, 212, 64, 0.85);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
  }
}
.original.card-lg.reading {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
/* Access Page */
.access-page {
  background: #111111;
  border-radius: 24px;
  margin-top: 100px;
  display: flex;
  align-items: center;
  padding: 0;
  position: relative;
  overflow: hidden;
  min-height: 500px;
}
.access-page-content {
  display: flex;
  width: 50%;
  padding: 24px;
  flex-direction: column;
}
.access-page-content-header {
  margin-bottom: 26px;
}
.sliding {
  position: absolute;
  overflow: hidden;
  width: 50%;
  min-height: 733px;
  right: 0;
  top: 0;
}
.infinite-scroll-component {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: hidden;
  display: flex;
  gap: 8px;
}
.infinite-scroll-content,
.infinite-scroll-content-rev {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.infinite-scroll-content {
  animation: scrollDown 20s linear infinite;
  transform: translateY(0);
}
.infinite-scroll-content-rev {
  animation: scrollUp 20s linear infinite;
  transform: translateY(-100%);
}
.serie-card {
  display: block;
  width: 160px;
  margin: 4px 0;
  border-radius: 8px;
}

@keyframes scrollDown {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-100%);
  }
}

@keyframes scrollUp {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0);
  }
}
.sub-white {
  padding-bottom: 12px;
  border-bottom: 1px solid #2b2b2b;
}
.login-text {
  margin-top: 16px;
  text-align: center;
}
.rotated {
  transform: rotate(12deg);
}
/* Access Page */
.new-chapter {
  color: #63aff5 !important;
  background: #63aff51a !important;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  animation: chapter-pulse 2s infinite;
  gap: 3px;
}

.premium-ch {
  color: #ffc71e !important;
  background: #f5da631a !important;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  animation: premium-pulse 2s infinite;
  gap: 3px;
}

@keyframes chapter-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(7, 135, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}

@keyframes premium-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 210, 7, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}

.u-menu .nav-btn {
  border-radius: 30px;
}

.u-menu .nav-btn img {
  border-radius: 50%;
  width: 28px;
  height: 28px;
  object-fit: cover;
}
.head.mb-3 a {
  border: none;
}

.progress-wrap {
  position: fixed;
  right: 50px;
  bottom: 50px;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px rgba(252, 191, 58, 0.2);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}

.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.progress-wrap::after {
  position: absolute;
  content: "↑";
  text-align: center;
  line-height: 46px;
  font-size: 24px;
  color: #63aff5;
  left: 0;
  top: 0;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  z-index: 1;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}

.progress-wrap svg path {
  fill: none;
}

.progress-wrap svg.progress-circle path {
  stroke: #63aff5;
  stroke-width: 4;
  box-sizing: border-box;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}
.original.card-lg .unit .inner .info > ul li a:visited {
  background: #111111;
}

.original.card-lg .unit .inner .info > ul li a:visited span {
  color: #5d5d5d;
}

.ad-container {
  background: #0e0e0e;
  text-align: center;
  border-radius: 8px;
  border: 1px solid #1a1a1a;
  color: #5f5f5f;
  width: 755px;
  margin: 1rem auto;
  overflow-x: hidden;
}
.ad-container-home {
  background: #0e0e0e;
  text-align: center;
  border-radius: 8px;
  border: 1px solid #1a1a1a;
  color: #5f5f5f;
  width: 1410px;
  margin: 1rem auto;
  overflow-x: hidden;
}
.ad-header {
  border-bottom: 1px solid #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 12px 0;
}
.ad-footer {
  border-top: 1px solid #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 0;
}
.ad-content {
  padding: 12px;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}
.top-ad {
  display: flex;
  gap: 8px;
}
body.read main .m-content .number-nav a,
.chapter-control .jb-btn {
  width: 33.33%;
  justify-content: center;
}
.chapter-control {
  margin: 1rem 0;
  max-width: 720px;
  width: 100%;
}
/*Socials*/
.flex-container {
  display: flex;
  gap: 1rem;
}
.container-socials {
  max-width: 1444px;
  width: 100%;
}
.comments-container {
  max-width: 1444px;
  width: 100%;
  background: #161616;
  margin-top: 1rem;
  border-radius: 12px;
  padding: 32px;
}
.section-container {
  display: flex;
  justify-content: space-between;
  gap: 1.5rem;
  background: rgba(255, 255, 255, 0.04);
  padding: 1rem;
  border-radius: 1rem;
  position: relative;
  width: 50%;
  align-items: center;
}

.section-content {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.indicator-container {
  width: fit-content;
  height: 100%;
}

.indicator {
  height: 46px;
  width: 0.5rem;
  border-radius: 0.5rem;
  background: rgb(66 61 61 / 20%);
}

.section-text .sec-title {
  color: #fff;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 1.3rem;
}

.section-text .subtitle {
  color: #808080;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  height: 2.5rem;
  padding: 0 1.5rem;
  border-radius: 999px;
  color: white;
  font-weight: 500;
}

.discord-button {
  background: #5865f2;
}

.discord-button:hover {
  background: rgba(88, 101, 242, 0.8);
  color: #fff;
}
.sponsored-button {
  background: linear-gradient(45deg, #5865f2, #5a47d7, #5865f2);
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

.sponsored-button:hover {
  background: linear-gradient(45deg, #5865f2, #5a47d7, #5865f2);
  background-size: 200% 200%;
  animation: gradient 2s ease infinite;
  color: #fff;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
.sponsored-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 2;
  background-color: #ffd700;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: #000;
}
.title-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
}
.ad-badge {
  background-color: #ffffff1a;
  backdrop-filter: blur(4px);
  padding: 0 4px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  color: #ffffffa1;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border: 1px solid #ffffff24;
}
.status-wrapper .ad-badge {
  margin-left: -4px;
}
.donation-section .background-image {
  height: 100%;
  width: auto;
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleX(-1);
}

.kofi-button {
  background: #4299e1;
}

.kofi-button:hover {
  background: rgba(66, 153, 225, 0.8);
  color: #fff;
}

.icon-container .icon {
  width: 1.5rem;
}
/* Chapter List */
.nav-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1rem 0;
}
.nav-header {
  font-size: 1.2rem;
  text-align: center;
  margin: 1rem;
}
.chapter-navigation-bar {
  max-width: 720px;
  width: 100%;
}
.ch-settings {
  display: flex;
  gap: 1rem;
  max-width: 720px;
}
.chapter-list {
  border-radius: 8px;
  overflow-y: auto;
  background: #0c0c0c;
  border: 1px solid #191919;
  height: 500px;
  width: 100%;
}

.chapter-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}
.chapter-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  padding: 8px;
}

.chapter-list li {
  padding: 0;
  margin: 0;
  width: 49%;
  text-align: center;
}

.chapter-list a {
  display: block;
  padding: 8px 16px;
  text-decoration: none;
  color: inherit;
  background: #131313;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}
.chapter-list a:hover {
  background: #242424;
  color: #fff;
}

/* Move visited state before active to allow active to override */

.chapter-list a:visited {
  background: #0b0b0b;
  color: #5d5d5d;
}
.chapter-list a:visited:hover {
  background: #242424;
  color: #fff;
}
/* Active state will now take precedence */
.chapter-list a[class="active"] {
  background: #1a1a1a;
  color: white !important;
}
.jb-btn {
  width: 100%;
}
.chapter-buttons > button {
  width: 33.33%;
}
#number-go-left:disabled {
  background: transparent;
  color: #383838;
  pointer-events: none;
}
button.number-toggler,
button#number-go-right,
button#number-go-left,
.jb-btn {
  background: transparent;
  padding: 10px 0px;
  border-radius: 8px;
  font-size: 16px;
  color: #cacaca;
  gap: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid #1d1d1d;
  justify-content: center;
}
button.number-toggler:hover,
button#number-go-right:hover,
button#number-go-left:hover,
.jb-btn:hover {
  background: #242424;
  color: #fff;
}
.chapter-search {
  padding: 10px;
}

.search-wrapper {
  position: relative;
}

i.fa-light.fa-magnifying-glass {
  position: absolute;
  left: 12px;
  color: #4c4c4c;
  top: 14px;
}

input#chapterSearch {
  width: 100%;
  padding: 8px 8px 8px 35px;
  border: 1px solid #212121;
  border-radius: 8px;
  margin-bottom: 10px;
  background: #131313;
  color: #fff;
}
.site-header {
  position: static !important;
}
/*Loading Spinner*/
@keyframes ldld-default {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.reading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

#view-all {
  position: relative;
  width: 100%;
  background: transparent;
  padding: 10px 0px;
  border-radius: 8px;
  font-size: 16px;
  color: #5f5f5f;
  gap: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid #1d1d1d;
  justify-content: center;
}
#view-all:hover {
  background: #242424;
  color: #fff;
}
.content-main {
  display: flex;
  width: 100%;
  margin: auto;
  max-width: 1440px;
}
/*Most Popular*/
.popular-series-section {
  border-radius: 8px;
  width: 24%;
  height: 100%;
}
.popular-series-wrapper {
  display: flex;
  gap: 12px;
  flex-direction: column;
}
button.filter-btn {
  color: #696969;
  font-size: 12px;
}
.time-filters {
  border: 1px solid #2d2d2d;
  padding: 4px;
  border-radius: 8px;
  display: flex;
}

.filter-btn {
  background: transparent;
  border: none;
  color: #fff;
  padding: 8px 12px;
  cursor: pointer;
}

.filter-btn.active {
  background: #ffffff;
  border-radius: 6px;
  color: #000;
}

.popular-series-item {
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  opacity: 1;
  background: #121212;
  border-radius: 12px;
}
.popular-series-item:hover {
  background: #171717;
}
.series-title {
  color: #d1d1d1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-size: 1rem;
}
.genre-tag {
  background: #171717;
  padding: 3px 5px;
  color: #898989;
  border-radius: 4px;
  font-size: 12px;
}
.series-genres {
  display: flex;
  gap: 4px;
}
.rank-number {
  font-size: 12px;
  color: #fff;
  background: #191919;
  min-width: 20px;
  min-height: 20px;
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 4px 0 3px;
}
.section-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.6rem;
}
/* New combined styles */
.series-thumb-rank {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}
/* Loading state animations */
.popular-series-list.loading {
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.popular-series-list.loading .popular-series-item {
  opacity: 0.5;
}

/* Prevent height collapse during loading */
.popular-series-list:empty {
  min-height: 200px; /* Same as .popular-series-list */
}

/* Smooth entrance for new items */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.popular-series-item {
  animation: fadeIn 0.3s ease forwards;
}

.series-thumbnail {
  width: 66px;
  height: 96px;
  overflow: hidden;
  border-radius: 4px;
  flex-shrink: 0;
}

.series-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.top-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.top-rating i {
  color: #f2ae40;
}

.top-rating span {
  margin-left: 4px;
  color: #666;
}

.far.fa-star {
  color: #f2ae40;
}
/*Scroll Dowwn Animation*/

.scroll-indicator {
  width: 150px;
  height: 150px;
  background: rgb(0 0 0 / 19%);
  border: 1px solid #1b1b1b;
  border-radius: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.arrows {
  display: flex;
  flex-direction: column;
}

.arrow {
  width: 24px;
  height: 18px;
  color: white;
  animation: bounce 1s infinite;
}

.arrow:nth-child(1) {
  opacity: 0.3;
}

.arrow:nth-child(2) {
  opacity: 0.6;
  animation-delay: 150ms;
}

.arrow:nth-child(3) {
  opacity: 1;
  animation-delay: 300ms;
}

.scroll-text {
  color: white;
  font-family: system-ui, -apple-system, sans-serif;
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px !important;
}
.scroll-indicator-container {
  width: 720px;
  height: 720px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #111111;
  opacity: 1;
  border-radius: 12px 12px 0 0;
  transition: opacity 0.3s ease;
  position: relative;
  z-index: 2;
}

.scroll-indicator-container.fade-out {
  opacity: 0;
  pointer-events: none;
}
@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(6px);
  }
}
/*toast*/
.toast-container {
  position: fixed;
  bottom: 1rem; /* Changed from top to bottom */
  right: 1rem;
  z-index: 99999;
  pointer-events: none;
}

.toast {
  pointer-events: auto;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  max-width: 400px;
  margin-bottom: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  background: #18181b; /* Dark background */
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: all 0.2s ease-in-out;
  will-change: transform, opacity;
}

/* Success toast */
.toast-success {
  color: #4ade80; /* Light green text for success */
}

/* Error toast */
.toast-error {
  color: #f87171; /* Light red text for error */
}

/* Removed/Neutral toast */
.toast-removed {
  color: #a1a1aa; /* Light grey text for removed/neutral state */
}

.toast-icon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.toast-message {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.4;
  color: inherit;
}

.toast-close {
  margin-left: auto;
  padding: 0;
  width: 16px;
  height: 16px;
  border: none;
  background: none;
  color: #71717a;
  opacity: 0.5;
  cursor: pointer;
  transition: opacity 0.2s;
}

.toast-close:hover {
  opacity: 1;
}

.toast-close svg {
  width: 100%;
  height: 100%;
}

@keyframes toast-slide-in {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes toast-slide-out {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}
.below > span {
  color: #ffffff6b;
  font-weight: 300;
}
#top-trending .swiper-slide a,
#top-trending .swiper-slide a:hover {
  color: #fff;
  text-decoration: none;
}
.new-badge {
  background-color: #63aff5;
  color: #212121;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.6em;
  font-weight: bold;
  text-transform: uppercase;
  position: absolute;
  top: -2px;
  right: -14px;
  animation: pulse-badge 2s infinite;
}

.premium-badge {
  background-color: #ffb935;
  color: #212121;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.6em;
  font-weight: bold;
  text-transform: uppercase;
  position: absolute;
  top: -2px;
  right: -14px;
  animation: pulse-premium 2s infinite;
}

@keyframes pulse-badge {
  0% {
    box-shadow: 0 0 0 0 rgba(7, 218, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}

@keyframes pulse-premium {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 197, 7, 0.87);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}
.adb {
  width: 100%;
  max-width: 728px;
  margin: 20px auto;
  text-align: center;
}

.ad-notice {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}

.adb {
  border-radius: 4px;
}

.adb img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.continue-reading,
.sponsored-notice {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
}
.maintenance-notice {
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  color: #856404;
  padding: 12px 0;
  text-align: center;
}

.maintenance-notice .container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.maintenance-notice i {
  font-size: 1.2em;
}

.maintenance-notice p {
  margin: 0;
}
/*Responsiveness*/
@media (max-width: 1444px) {
  .footer-top {
    width: 100%;
    padding: 24px;
  }
  .footer-bottom {
    width: 100%;
    padding: 24px;
    gap: 12px;
  }
}

@media (max-width: 1280px) {
  .original.card-lg .unit {
    width: 20%;
  }
  .stat-item {
    width: 23%;
  }
}
@media (max-width: 990px) {
  .original.card-lg .unit {
    width: 20%;
  }
  .hposter {
    width: 30%;
  }
  .stat-item {
    width: 30%;
  }
  #filters > div:last-child > div .dropdown .dropdown-menu.c1 {
    min-width: 100%;
    width: 100%;
  }
  .ad-container {
    width: 100%;
  }
  .ad-container-home {
    width: 100%;
  }
  .flex-container {
    flex-direction: column;
  }
  .section-container {
    width: 100%;
  }
  section.recently-updated {
    width: 100%;
  }
  .popular-series-section {
    width: 100%;
    padding: 12px;
  }
  .content-main {
    flex-direction: column;
  }
  #continue-reading .unit {
    width: 20%;
  }
}
@media (max-width: 780px) {
  .nav-controls {
    margin: 1rem;
  }
  .chapter-list li {
    width: 48%;
  }
  #ch-images {
    width: 100%;
  }
  .sliding {
    display: none;
  }
  .access-page-content {
    width: 100%;
  }
  #filters > div:last-child > div {
    width: 50%;
  }
  .original.card-lg .unit {
    width: 20%;
  }
  .hposter {
    width: 100%;
  }
  .hcontent {
    flex-direction: column;
  }
  .serie-info {
    width: 100%;
  }
  .stat-item {
    width: 48%;
  }
  .modal-content {
    margin: 20px auto;
    border-radius: 12px;
    width: 90%;
  }
  .modal-content {
    width: 95%;
    max-width: 100%;
    border-radius: 0;
    overflow-x: hidden;
    margin-top: 20%;
  }

  .modal {
    padding: 0 !important;
  }

  .search-content {
    padding: 15px;
    width: 100%;
    box-sizing: border-box;
  }

  .search-result-card {
    width: 100%;
    box-sizing: border-box;
    margin: 0;
  }

  .search-inner form {
    width: 100%;
    box-sizing: border-box;
  }
  .shortcuts {
    display: none;
  }
  .view-all-results {
    width: 100%;
    justify-content: center;
  }
  .ad-content {
    flex-direction: column;
  }
  .top-ad {
    flex-direction: column;
  }
  #continue-reading .unit {
    width: 25%;
  }
  .scroll-indicator-container {
    width: 100%;
    height: 520px;
  }
  #top-trending {
    margin-bottom: 1rem;
  }
  .container-socials {
    padding: 12px;
  }
  .comments-container {
    padding: 32px 12px;
    margin-top: 0;
  }
}
@media (max-width: 630px) {
  .original.card-lg .unit {
    width: 33.333%;
  }
  .main-cover {
    margin: 70px;
  }
  .footer-links {
    flex-direction: column;
  }
  .footer-bottom {
    flex-direction: column;
  }
  .sub-panel.active {
    width: 85%;
  }
  .progress-wrap {
    right: 20px;
    bottom: 20px;
  }
  .container {
    padding-right: 8px;
    padding-left: 8px;
  }
  #nav-search-btn {
    display: flex;
  }
  .manga-bottom .content .m-list ul li a span:last-child {
    width: fit-content;
  }
  #continue-reading .unit {
    width: 33%;
  }
  .reading-content {
    width: 100%;
  }
}
@media (max-width: 474px) {
  #continue-reading .unit {
    width: 50%;
  }
  .original.card-lg .unit {
    width: 50%;
  }
  .original.card-lg.reading .unit,
  .user-panel .original.card-lg.reading .unit {
    width: 50%;
  }
  .home-swiper .swiper .swiper-wrapper .swiper-slide {
    width: 50% !important;
  }
}


