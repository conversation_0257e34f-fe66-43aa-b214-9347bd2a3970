<?php

function register_manga_type_taxonomy() {
    $labels = array(
        'name'              => _x('Manga Types', 'taxonomy general name', 'child-mfire'),
        'singular_name'     => _x('Manga Type', 'taxonomy singular name', 'child-mfire'),
        'search_items'      => __('Search Manga Types', 'child-mfire'),
        'all_items'         => __('All Manga Types', 'child-mfire'),
        'parent_item'       => __('Parent Manga Type', 'child-mfire'),
        'parent_item_colon' => __('Parent Manga Type:', 'child-mfire'),
        'edit_item'         => __('Edit Manga Type', 'child-mfire'),
        'update_item'       => __('Update Manga Type', 'child-mfire'),
        'add_new_item'      => __('Add New Manga Type', 'child-mfire'),
        'new_item_name'     => __('New Manga Type Name', 'child-mfire'),
        'menu_name'         => __('Manga Type', 'child-mfire'),
    );

    $args = array(
        'labels'            => $labels,
        'hierarchical'      => false,  // true if you want parent/child relationships
        'public'            => true,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'manga-type'),
    );

    register_taxonomy('wp-manga-type', array('wp-manga'), $args);
}
add_action('init', 'register_manga_type_taxonomy');