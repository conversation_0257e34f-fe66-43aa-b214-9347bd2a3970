<?php
/**
 * Subscription Modal Template
 */

// Get subscription plans
global $wpdb;
$plans_table = $wpdb->prefix . 'manga_subscription_plans';
$plans = $wpdb->get_results("SELECT * FROM $plans_table WHERE is_active = 1 ORDER BY duration_months ASC");

// Get current user subscription status
$user_id = get_current_user_id();
$current_subscription = null;
if ($user_id && class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
    $subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
    $current_subscription = $subscription_manager->get_user_subscription($user_id);
}
?>

<!-- Subscription Modal -->
<div id="subscription-modal" class="subscription-modal" style="display: none;">
    <div class="subscription-modal-overlay" onclick="closeSubscriptionModal()"></div>
    <div class="subscription-modal-content">
        <!-- Close Button -->
        <button class="subscription-modal-close" onclick="closeSubscriptionModal()">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                <path d="M13 1L1 13M1 1L13 13" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
        </button>

        <!-- Header -->
        <div class="subscription-modal-header">
            <h2>Get Premium Access!</h2>
            <p>Unlock all premium manga chapters with your subscription</p>
        </div>

        <!-- Subscription Plans -->
        <div class="subscription-modal-body">
            <div class="subscription-plans-grid">
                <?php if (!empty($plans)): ?>
                    <?php foreach ($plans as $index => $plan): ?>
                        <?php
                        $is_popular = $plan->duration_months == 6;
                        $is_best_deal = $plan->duration_months == 12;
                        $discount = $plan->duration_months == 6 ? '10% OFF' : ($plan->duration_months == 12 ? '20% OFF' : '');
                        $savings = $plan->duration_months == 6 ? 'Save $6' : ($plan->duration_months == 12 ? 'Save $15' : '');
                        ?>
                        <button class="subscription-plan-card <?php echo $index === 0 ? 'selected' : ''; ?>" 
                                data-plan-id="<?php echo $plan->plan_id; ?>"
                                data-plan-name="<?php echo esc_attr($plan->plan_name); ?>"
                                data-plan-price="<?php echo $plan->price; ?>"
                                data-plan-duration="<?php echo $plan->duration_months; ?>"
                                onclick="selectPlan(this)">
                            
                            <?php if ($is_popular): ?>
                                <div class="plan-badge popular">Most Popular</div>
                            <?php elseif ($is_best_deal): ?>
                                <div class="plan-badge best-deal">Best Deal</div>
                            <?php endif; ?>

                            <div class="plan-content">
                                <div class="plan-main">
                                    <div class="plan-duration">
                                        <span class="duration-number"><?php echo $plan->duration_months; ?></span>
                                        <span class="duration-text">Month<?php echo $plan->duration_months > 1 ? 's' : ''; ?></span>
                                    </div>
                                    <div class="plan-price">
                                        <span class="price-currency">$</span>
                                        <span class="price-amount"><?php echo number_format($plan->price, 2); ?></span>
                                    </div>
                                    <?php if ($discount): ?>
                                        <div class="plan-discount"><?php echo $discount; ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ($savings): ?>
                                    <div class="plan-savings">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                                            <path d="M7 1L8.5 5.5L13 7L8.5 8.5L7 13L5.5 8.5L1 7L5.5 5.5L7 1Z" fill="currentColor"/>
                                        </svg>
                                        <span><?php echo $savings; ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </button>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="no-plans-message">
                        <p>No subscription plans available at the moment.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Selected Plan Summary -->
            <div class="subscription-summary">
                <div class="summary-row">
                    <span class="summary-label">Selected Plan</span>
                    <span class="summary-value" id="selected-plan-name">
                        <?php echo !empty($plans) ? $plans[0]->plan_name : 'No plan selected'; ?>
                    </span>
                </div>
                <div class="summary-row total">
                    <span class="summary-label">Total</span>
                    <span class="summary-price" id="selected-plan-price">
                        $<?php echo !empty($plans) ? number_format($plans[0]->price, 2) : '0.00'; ?>
                    </span>
                </div>
            </div>

            <!-- Benefits List -->
            <div class="subscription-benefits">
                <h4>What you get:</h4>
                <ul>
                    <li>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M13.5 4.5L6 12L2.5 8.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Unlimited access to all premium chapters
                    </li>
                    <li>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M13.5 4.5L6 12L2.5 8.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Early access to new releases
                    </li>
                    <li>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M13.5 4.5L6 12L2.5 8.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Ad-free reading experience
                    </li>
                    <li>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M13.5 4.5L6 12L2.5 8.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Download chapters for offline reading
                    </li>
                </ul>
            </div>

            <!-- PayPal Button Section -->
            <div class="subscription-payment">
                <button class="paypal-button" onclick="processSubscription()" id="subscribe-button">
                    <div class="paypal-button-content">
                        <svg width="24" height="24" viewBox="0 0 384 512" fill="currentColor">
                            <path d="M111.4 295.9c-3.5 19.2-17.4 108.7-21.5 134-.3 1.8-1 2.5-3 2.5H12.3c-7.6 0-13.1-6.6-12.1-13.9L58.8 46.6c1.5-9.6 10.1-16.9 20-16.9 152.3 0 165.1-3.7 204 11.4 60.1 23.3 65.6 79.5 44 140.3-21.5 62.6-72.5 89.5-140.1 90.3-43.4.7-69.5-7-75.3 24.2zM357.1 152c-1.8-1.3-2.5-1.8-3 1.3-2 11.4-5.1 22.5-8.8 33.6-39.9 113.8-150.5 103.9-204.5 103.9-6.1 0-10.1 3.3-10.9 9.4-22.6 140.4-27.1 169.7-27.1 169.7-1 7.1 3.5 12.9 10.6 12.9h63.5c8.6 0 15.7-6.3 17.4-14.9.7-5.4-1.1 6.1 14.4-91.3 4.6-22 14.3-19.7 29.3-19.7 71 0 126.4-28.8 142.9-112.3 6.5-34.8 4.6-71.4-23.8-92.6z"/>
                        </svg>
                        <span>Subscribe with PayPal</span>
                    </div>
                </button>
                
                <!-- PayPal embedded checkout (hidden initially) -->
                <div id="paypal-button-container" style="display: none;"></div>
            </div>

            <!-- Security Notice -->
            <div class="subscription-security">
                <p>🔒 Secure payment • Cancel anytime • Instant activation</p>
            </div>
        </div>
    </div>
</div>

<style>
.subscription-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.2s ease-out;
}

.subscription-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
}

.subscription-modal-content {
    position: relative;
    background: #0B0B0B;
    border: 6px solid #1D1D1D;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 95vh;
    overflow-y: auto;
    box-shadow: 0px 25px 63px 0px rgba(0,0,0,0.46);
    animation: slideIn 0.3s ease-out;
}

.subscription-modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.05);
    border: none;
    border-radius: 8px;
    color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
    z-index: 10;
}

.subscription-modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.subscription-modal-header {
    text-align: center;
    padding: 60px 40px 30px;
}

.subscription-modal-header h2 {
    color: #fff;
    font-size: 2.5rem;
    font-weight: 900;
    margin: 0 0 12px 0;
    line-height: 1.2;
}

.subscription-modal-header p {
    color: #8A8A8A;
    font-size: 1rem;
    margin: 0;
    line-height: 1.5;
}

.subscription-modal-body {
    padding: 0 40px 40px;
}

.subscription-plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.subscription-plan-card {
    background: #1A1A1A;
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.subscription-plan-card:hover {
    background: rgba(26, 26, 26, 0.7);
}

.subscription-plan-card.selected {
    border-color: #FF6B35;
    background: rgba(255, 107, 53, 0.1);
}

.plan-badge {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    color: white;
    white-space: nowrap;
}

.plan-badge.popular {
    background: #10B981;
}

.plan-badge.best-deal {
    background: #4F46E5;
}

.plan-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.plan-main {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.plan-duration {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin-bottom: 8px;
}

.duration-number {
    color: #fff;
    font-size: 1.5rem;
    font-weight: bold;
}

.duration-text {
    color: #fff;
    font-size: 1rem;
}

.plan-price {
    display: flex;
    align-items: baseline;
    gap: 2px;
    margin-bottom: 4px;
}

.price-currency {
    color: #8A8A8A;
    font-size: 0.9rem;
}

.price-amount {
    color: #8A8A8A;
    font-size: 1rem;
}

.plan-discount {
    background: #FF4E4E;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
}

.plan-savings {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #10B981;
    font-size: 12px;
    font-weight: 600;
}

.subscription-summary {
    background: rgba(42, 42, 42, 0.3);
    border: 3px solid #1A1A1A;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.summary-row.total {
    margin-bottom: 0;
    padding-top: 16px;
    border-top: 1px solid #333;
}

.summary-label {
    color: #8A8A8A;
    font-size: 1rem;
}

.summary-value {
    color: #FF6B35;
    font-weight: 600;
}

.summary-price {
    color: #8A8A8A;
    font-size: 1.5rem;
    font-weight: bold;
}

.subscription-benefits {
    margin-bottom: 24px;
}

.subscription-benefits h4 {
    color: #fff;
    margin: 0 0 16px 0;
    font-size: 1.1rem;
}

.subscription-benefits ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.subscription-benefits li {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #8A8A8A;
    margin-bottom: 12px;
    font-size: 0.95rem;
}

.subscription-benefits svg {
    color: #10B981;
    flex-shrink: 0;
}

.paypal-button {
    width: 100%;
    height: 60px;
    background: linear-gradient(135deg, #FF780C 0%, #FF6B35 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 
        0 4px 20px rgba(255, 103, 10, 0.25),
        0 1.5px 0 rgb(230, 169, 64) inset,
        0 -8px 44px rgba(255, 167, 17, 0.5) inset,
        0 0 12px rgba(255, 255, 255, 0.25) inset,
        0 7px 14px rgb(254, 97, 8) inset;
    margin-bottom: 16px;
}

.paypal-button:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 6px 25px rgba(255, 103, 10, 0.35),
        0 1.5px 0 rgb(230, 169, 64) inset,
        0 -8px 44px rgba(255, 167, 17, 0.5) inset,
        0 0 12px rgba(255, 255, 255, 0.25) inset,
        0 7px 14px rgb(254, 97, 8) inset;
}

.paypal-button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.subscription-security {
    text-align: center;
}

.subscription-security p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: scale(0.95) translateY(-20px);
    }
    to { 
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .subscription-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .subscription-modal-header {
        padding: 40px 24px 20px;
    }
    
    .subscription-modal-header h2 {
        font-size: 1.8rem;
    }
    
    .subscription-modal-body {
        padding: 0 24px 24px;
    }
    
    .subscription-plans-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .subscription-plan-card {
        padding: 16px;
        min-height: 100px;
    }
    
    .duration-number {
        font-size: 1.2rem;
    }
    
    .paypal-button {
        height: 50px;
        font-size: 1rem;
    }
}
</style>

<script>
let selectedPlanData = null;

function selectPlan(element) {
    // Remove selected class from all plans
    document.querySelectorAll('.subscription-plan-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Add selected class to clicked plan
    element.classList.add('selected');
    
    // Store selected plan data
    selectedPlanData = {
        id: element.dataset.planId,
        name: element.dataset.planName,
        price: element.dataset.planPrice,
        duration: element.dataset.planDuration
    };
    
    // Update summary
    document.getElementById('selected-plan-name').textContent = selectedPlanData.name;
    document.getElementById('selected-plan-price').textContent = '$' + parseFloat(selectedPlanData.price).toFixed(2);
}

function openSubscriptionModal() {
    document.getElementById('subscription-modal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeSubscriptionModal() {
    document.getElementById('subscription-modal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

function processSubscription() {
    if (!selectedPlanData) {
        alert('Please select a subscription plan');
        return;
    }
    
    // Check if user is logged in
    <?php if (!is_user_logged_in()): ?>
        window.location.href = '<?php echo site_url('/connexion'); ?>';
        return;
    <?php endif; ?>
    
    // Hide the main button and show PayPal embedded checkout
    document.getElementById('subscribe-button').style.display = 'none';
    document.getElementById('paypal-button-container').style.display = 'block';
    
    // Initialize PayPal buttons if not already done
    if (!window.paypalButtonsRendered) {
        initializePayPalButtons();
        window.paypalButtonsRendered = true;
    }
}

function initializePayPalButtons() {
    // Clear any existing PayPal buttons first
    document.getElementById('paypal-button-container').innerHTML = '';
    
    paypal.Buttons({
        style: {
            layout: 'vertical',
            color: 'gold',
            shape: 'rect',
            label: 'subscribe'
        },
        createSubscription: function(data, actions) {
            return actions.subscription.create({
                'plan_id': selectedPlanData.paypal_plan_id,
                'subscriber': {
                    'name': {
                        'given_name': '<?php echo wp_get_current_user()->first_name; ?>',
                        'surname': '<?php echo wp_get_current_user()->last_name; ?>'
                    },
                    'email_address': '<?php echo wp_get_current_user()->user_email; ?>'
                }
            });
        },
        onApprove: function(data, actions) {
            // Handle successful subscription
            const formData = new FormData();
            formData.append('action', 'process_paypal_subscription');
            formData.append('subscription_id', data.subscriptionID);
            formData.append('plan_id', selectedPlanData.id);
            formData.append('nonce', '<?php echo wp_create_nonce('subscription_payment'); ?>');
            
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Subscription successful!');
                    location.reload();
                } else {
                    alert('Error processing subscription: ' + data.data.message);
                }
            });
        },
        onCancel: function(data) {
            document.getElementById('subscribe-button').style.display = 'block';
            document.getElementById('paypal-button-container').style.display = 'none';
        },
        onError: function(err) {
            console.error('PayPal error:', err);
            alert('Payment error occurred. Please try again.');
            document.getElementById('subscribe-button').style.display = 'block';
            document.getElementById('paypal-button-container').style.display = 'none';
        }
    }).render('#paypal-button-container');
}

// Initialize first plan as selected
document.addEventListener('DOMContentLoaded', function() {
    const firstPlan = document.querySelector('.subscription-plan-card');
    if (firstPlan) {
        selectPlan(firstPlan);
    }
});
</script>

<!-- Add PayPal SDK script -->
<script src="https://www.paypal.com/sdk/js?client-id=<?php echo get_option('manga_subscription_paypal_client_id', ''); ?>&vault=true&intent=subscription"></script>

<script>
// PayPal Buttons configuration
paypal.Buttons({
    style: {
        layout: 'vertical',
        color: 'gold',
        shape: 'rect',
        label: 'subscribe'
    },
    createSubscription: function(data, actions) {
        if (!selectedPlanData) {
            alert('Please select a subscription plan');
            return;
        }
        
        return actions.subscription.create({
            'plan_id': selectedPlanData.paypal_plan_id, // You'll need to create PayPal plans
            'subscriber': {
                'name': {
                    'given_name': '<?php echo wp_get_current_user()->first_name; ?>',
                    'surname': '<?php echo wp_get_current_user()->last_name; ?>'
                },
                'email_address': '<?php echo wp_get_current_user()->user_email; ?>'
            }
        });
    },
    onApprove: function(data, actions) {
        // Handle successful subscription
        const formData = new FormData();
        formData.append('action', 'process_paypal_subscription');
        formData.append('subscription_id', data.subscriptionID);
        formData.append('plan_id', selectedPlanData.id);
        formData.append('nonce', '<?php echo wp_create_nonce('subscription_payment'); ?>');
        
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Subscription successful!');
                location.reload();
            } else {
                alert('Error processing subscription: ' + data.data.message);
            }
        });
    },
    onError: function(err) {
        console.error('PayPal error:', err);
        alert('Payment error occurred. Please try again.');
    }
}).render('#paypal-button-container');
</script>

