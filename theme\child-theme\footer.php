<footer>
    <!-- Footer Top Section -->
    <div class="footer-top">
        <!-- Logo and Tagline -->
        <div class="footer-brand">
            <a class="logo" href="<?php echo home_url(); ?>">
                <img src="<?php echo esc_url(get_stylesheet_directory_uri()); ?>/assets/images/logo.png" alt="<?php echo esc_attr(get_bloginfo('name')); ?>" />
            </a>
            <p class="tagline">Plongez dans le monde ultime des Webtoon,<br>Action, Drame, Aventure&Fantaisie !</p>
        </div>

        <!-- Footer Links -->
        <div class="footer-links">
            <a href="/tos">Conditions d'utilisation</a>
            <a href="/politique-general">Politique générale</a>
            <a href="/advertising">Advertising</a>
            <a href="/contactez-nous">Contact</a>
        </div>
    </div>

    <!-- Footer Bottom Section -->
    <div class="footer-bottom">
        <div class="copyright">
            <p>© 2025 Raijin Scans. Tous droits réservés.</p>
            <p class="subtitle">Profitez d'un accès gratuit aux derniers chapitres et aux classiques du genre, adaptés à tous les fans des Webtoons. Bonne lecture !</p>
        </div>
        <div class="social-links">
            <a href="#" target="_blank" class="twitter"><i class="fa-brands fa-twitter"></i></a>
            <a href="#" target="_blank" class="reddit"><i class="fa-brands fa-reddit"></i></a>
            <a href="https://discord.gg/DkFSSEncp7" target="_blank" class="discord"><i class="fa-brands fa-discord"></i></a>
        </div>
    </div>
</footer>

<?php wp_footer(); ?>

<div id="toast"></div>

<!-- Add scroll to top button -->
<div class="progress-group">
    <div class="progress-wrap">
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
        </svg>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const progressWrap = document.querySelector('.progress-wrap');
    const progressPath = document.querySelector('.progress-wrap path');
    const pathLength = progressPath.getTotalLength();
    
    progressPath.style.transition = 'none';
    progressPath.style.strokeDasharray = pathLength + ' ' + pathLength;
    progressPath.style.strokeDashoffset = pathLength;
    progressPath.getBoundingClientRect();
    progressPath.style.transition = 'stroke-dashoffset 10ms linear';

    const updateProgress = () => {
        const scroll = window.pageYOffset;
        const height = document.documentElement.scrollHeight - window.innerHeight;
        const progress = pathLength - (scroll * pathLength / height);
        progressPath.style.strokeDashoffset = progress;
        
        if(scroll > 50) {
            progressWrap.classList.add('active-progress');
        } else {
            progressWrap.classList.remove('active-progress');
        }
    }

    window.addEventListener('scroll', updateProgress);

    progressWrap.addEventListener('click', (e) => {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
});
</script>
</body></html>
