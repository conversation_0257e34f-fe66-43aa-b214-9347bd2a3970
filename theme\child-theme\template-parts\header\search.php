<div id="search-modal" class="modal">
    <div class="modal-content">
        <button class="close-modal">
            <i class="fa-solid fa-xmark"></i>
        </button>
        <div class="search-inner">
            <form role="search" method="get" id="searchform" action="<?php echo esc_url(home_url('/')); ?>">
                <div class="command-bar">
                    <i class="fa-solid fa-magnifying-glass"></i>
                    <input type="hidden" name="post_type" value="wp-manga" />
                    <input type="text" name="s" id="live-search-input" placeholder="Rechercher" autocomplete="off" />
                    <button type="button" class="cancel-button">
                        <i class="fa-solid fa-xmark"></i>
                        <span>Annuler</span>
                    </button>
                </div>
            </form>
            <div class="search-content">
                <div class="search-results">
                    <div class="results-section">
                        <div class="manga-grid" id="search-results">
                            <!-- Results will be populated here -->
                        </div>
                    </div>
                </div>
                <div class="command-footer">
                    <div class="shortcuts">
                        <span class="shortcut">
                            <kbd>↑</kbd>
                            <kbd>↓</kbd>
                            <span>pour naviguer</span>
                        </span>
                        <span class="shortcut">
                            <kbd>↵</kbd>
                            <span>sélectionner</span>
                        </span>
                        <span class="shortcut">
                            <kbd>esc</kbd>
                            <span>fermer</span>
                        </span>
                    </div>
                    <button class="view-all-results" onclick="document.getElementById('searchform').submit();">
                        Voir tout
                        <i class="fa-solid fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Cache DOM elements and frequently used values
    const elements = {
        searchInput: document.getElementById('live-search-input'),
        searchResults: document.getElementById('search-results'),
        modal: document.getElementById('search-modal'),
        closeButton: document.querySelector('.close-modal'),
        viewAllButton: document.querySelector('.view-all-results'),
        cancelButton: document.querySelector('.cancel-button')
    };
    const DEBOUNCE_DELAY = 300;
    const MIN_SEARCH_LENGTH = 2;
    
    // Use a single bound event handler for better memory management
    const boundCloseModal = closeModal.bind(null);

    // Optimized modal functions using cached elements
    function openModal() {
        elements.modal.classList.add('show');
        document.body.classList.add('modal-open');
    }

    function closeModal() {
        elements.modal.classList.remove('show');
        document.body.classList.remove('modal-open');
    }

    // Debounce function to limit API calls
    const debounce = (fn, delay) => {
        let timer;
        return (...args) => {
            clearTimeout(timer);
            timer = setTimeout(() => fn.apply(this, args), delay);
        };
    };

    // Optimized search function with template literal stored outside the loop
    const resultTemplate = manga => `
        <a class="search-result-card" href="${manga.url}">
            <div class="search-result-thumbnail">
                <div class="thumbnail-wrapper">
                    <img src="${manga.thumbnail}" alt="${manga.title}" loading="lazy">
                </div>
            </div>
            <div class="search-result-details">
                <h6 class="search-result-title">${manga.title}</h6>
                <div class="search-result-genres">
                    ${manga.type ? `<span class="search-result-type-badge">${manga.type}</span>` : ''}
                </div>
                <div class="search-result-rating">
                    <i class="fas fa-star"></i>
                    <span>${manga.rating || '0.0'}</span>
                </div>
            </div>
        </a>
    `;

    // Modify the displayResults function to handle animations and improve image loading
    function displayResults(results) {
        const resultsSection = elements.searchResults.closest('.results-section');
        elements.searchResults.style.opacity = '0'; // Start fade-out animation

        setTimeout(() => {
            if (!results || results.length === 0) {
                elements.searchResults.innerHTML = '<div class="no-results">Aucun résultat trouvé.</div>'; // Display no results message
                // Let CSS handle height, or set to auto if needed, remove explicit pixel height setting
                resultsSection.style.height = 'auto'; 
                elements.searchResults.style.opacity = '1'; // Fade-in (no results message)
                currentIndex = -1; // Reset navigation index
                return;
            }

            elements.searchResults.innerHTML = results.map(resultTemplate).join('');
            // Cache result cards once to avoid querying on every navigation
            searchResultCards = Array.from(elements.searchResults.querySelectorAll('.search-result-card'));
            const images = Array.from(elements.searchResults.querySelectorAll('img'));

            if (images.length === 0) {
                // No images, calculate height immediately
                // Let CSS handle height, or set to auto
                resultsSection.style.height = 'auto';
                elements.searchResults.style.opacity = '1'; // Fade-in content
                currentIndex = -1; // Reset navigation index
            } else {
                // Images exist, wait for them to load or error
                const imageLoadPromises = images.map(img => {
                    // Resolve if image is already complete and has dimensions, or create a promise
                    if (img.complete && img.naturalHeight !== 0) return Promise.resolve();
                    return new Promise(resolve => {
                        img.onload = () => resolve();
                        img.onerror = () => resolve(); // Resolve on error too, to not block indefinitely
                    });
                });

                Promise.all(imageLoadPromises).then(() => {
                    // All images have attempted to load
                    // Let CSS handle height, or set to auto
                    resultsSection.style.height = 'auto';
                    elements.searchResults.style.opacity = '1'; // Fade-in content
                    currentIndex = -1; // Reset navigation index
                });
            }
        }, 200); // Delay for fade-out effect before DOM update
    }

    // Optimized search function with error handling
    const performSearch = debounce(async (query) => {
        const data = new FormData();
        data.append('action', 'live_search');
        data.append('search', query);
        data.append('nonce', '<?php echo wp_create_nonce("search_nonce"); ?>');

        try {
            const response = await fetch('<?php echo admin_url("admin-ajax.php"); ?>', {
                method: 'POST',
                body: data
            });
            const result = await response.json();
            
            displayResults(result.success ? result.data : null);
        } catch (error) {
            elements.searchResults.innerHTML = '<div class="no-results">An error occurred while searching</div>';
        }
    }, DEBOUNCE_DELAY);

    // Event Listeners using event delegation where possible
    elements.searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        if (query.length < MIN_SEARCH_LENGTH) {
            // Clear results and collapse section without showing "No results found" yet
            elements.searchResults.innerHTML = '';
            const resultsSection = elements.searchResults.closest('.results-section');
            // resultsSection.style.height = '0px'; // Let CSS handle this or set to auto if it was causing issues
            resultsSection.style.height = 'auto'; // Or '0px' if you want it to collapse fully
            elements.searchResults.style.opacity = '0'; // Hide it smoothly
            currentIndex = -1; // Reset navigation index
        } else {
            performSearch(query);
        }
    });

    elements.closeButton.addEventListener('click', boundCloseModal);
    document.addEventListener('keydown', e => e.key === 'Escape' && boundCloseModal());
    elements.modal.addEventListener('click', e => e.target === elements.modal && boundCloseModal);
    elements.viewAllButton.addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('searchform').submit();
    });

    // Add these variables at the top with other cached elements
    let currentIndex = -1;
    let searchResultCards = [];

    // Add this navigation function before the keyboard event listener
    function navigateResults(direction) {
         if (!searchResultCards.length) return;

         // Remove previous selection
         searchResultCards.forEach(card => card.classList.remove('selected'));

         // Update index based on direction
         if (direction === 'up') {
             currentIndex = currentIndex <= 0 ? searchResultCards.length - 1 : currentIndex - 1;
         } else {
             currentIndex = currentIndex >= searchResultCards.length - 1 ? 0 : currentIndex + 1;
         }

         // Add selection to new item and scroll into view
         const selectedCard = searchResultCards[currentIndex];
         selectedCard.classList.add('selected');
         selectedCard.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
     }

    // Add keyboard navigation event listener
    document.addEventListener('keydown', function(e) {
        if (!elements.modal.classList.contains('show')) return;

        switch(e.key) {
            case 'ArrowUp':
                e.preventDefault();
                navigateResults('up');
                break;
            case 'ArrowDown':
                e.preventDefault();
                navigateResults('down');
                break;
            case 'Enter':
                if (currentIndex >= 0) {
                    e.preventDefault();
                    const selectedCard = searchResultCards[currentIndex];
                    if (selectedCard) {
                        window.location.href = selectedCard.href;
                    }
                }
                break;
        }
    });

    elements.cancelButton.addEventListener('click', boundCloseModal);
});
</script>