jQuery(document).ready(function ($) {
  /*--------------------------------------------------------------
    >>> Request
    ----------------------------------------------------------------*/
  $("#submit-request").on("click", function (e) {
    e.preventDefault(); // Prevent default form submission behavior

    var title = $("#manga-title").val().trim(); // Trim whitespace
    var message = $("#manga-message").val().trim(); // Trim whitespace

    // Frontend validation for the 'title' field
    if (title === "" || title.length < 3) {
      $("#request-response").html(
        '<div class="alert alert-danger">Manga name is required</div>'
      );
      return; // Stop further execution if validation fails
    }

    if (title.length > 255) {
      $("#request-response").html(
        '<div class="alert alert-danger">Manga title is too long.</div>'
      );
      return;
    }

    if (message.length > 1000) {
      $("#request-response").html(
        '<div class="alert alert-danger">Message content is too long.</div>'
      );
      return;
    }

    // Show loading animation
    $("#request .loading").show();

    // Proceed with AJAX request
    $.ajax({
      type: "POST",
      url: ajax_manga_params.ajax_url,
      data: {
        action: "submit_request", // Ensure this matches the action defined in functions.php
        title: title,
        message: message,
        nonce: ajax_manga_params.nonce, // Include nonce for security
      },
      success: function (response) {
        // Hide loading animation
        $("#request .loading").hide();

        if (response.success) {
          $("#request-response").html(
            '<div class="alert alert-success">' + response.data + "</div>"
          );
          $("#request-form")[0].reset(); // Reset form fields after successful submission
        } else {
          $("#request-response").html(
            '<div class="alert alert-danger">' + response.data + "</div>"
          );
        }
      },
      error: function (xhr, status, error) {
        // Hide loading animation
        $("#request .loading").hide();

        $("#request-response").html(
          '<div class="alert alert-danger">Error: ' +
            xhr.responseText +
            "</div>"
        ); // Display error message
        console.error("AJAX Error:", xhr.status, error);
      },
    });
  });

  /*--------------------------------------------------------------
    >>> REPORTING
    ----------------------------------------------------------------*/

  $("#report-submit").on("click", function (e) {
    e.preventDefault();

    // Check if user is logged in
    if (ajax_manga_params.is_logged_in !== "true") {
      $("#report-message").html(
        '<div class="alert alert-danger">Please login first</div>'
      );
      return; // Stop the form from being submitted
    }

    // Show loading indicator
    $("#report .loading").show();

    var message = $('textarea[name="message"]').val();
    var mangaName = $('input[name="manga_name"]').val();
    var chapterNumber = $('input[name="chapter_number"]').val();
    var reportType = $('input[name="report_type"]').val();

    $.ajax({
      type: "POST",
      url: ajax_manga_params.ajax_url,
      data: {
        action: "submit_report",
        message: message,
        manga_name: mangaName,
        chapter_number: chapterNumber,
        report_type: reportType,
        nonce: ajax_manga_params.nonce, // Include nonce for security
      },
      success: function (response) {
        // Hide loading indicator
        $("#report .loading").hide();

        if (response.success) {
          $("#report-message").html(
            '<div class="alert alert-success">' + response.data + "</div>"
          );
          $("#report-form")[0].reset(); // Reset form fields after successful submission
        } else {
          $("#report-message").html(
            '<div class="alert alert-danger">' + response.data + "</div>"
          );
        }
      },
      error: function (xhr, status, error) {
        // Hide loading indicator
        $("#report .loading").hide();

        // Display generic error message to user
        $("#report-message").html(
          '<div class="alert alert-danger">An error occurred while processing your request. Please try again later</div>'
        );
        console.error("AJAX Error:", xhr.status, error); // Log the error in the browser console for debugging
      },
    });
  });

  /*--------------------------------------------------------------
    >>> QUICK
    ----------------------------------------------------------------*/

  $("#nav-search-btn").on("click", function (e) {
    e.stopPropagation(); // Prevent the click from bubbling up to the document
    $("#nav-search").addClass("active");
  });

  $(document).on("click", function (e) {
    if (
      !$(e.target).closest("#nav-search .search-inner").length &&
      !$(e.target).is("#nav-search-btn")
    ) {
      $("#nav-search").removeClass("active");
    }
  });

  /*--------------------------------------------------------------
    >>> RECENTLY UPDATED PAGINATION
    ----------------------------------------------------------------*/

  let currentPage = 1;
  let totalPages = 1; // Initially set to 1; will be updated dynamically

  // Function to load manga posts via Ajax
  function loadManga(page) {
    $.ajax({
      url: ajax_manga_params.ajax_url,
      type: "POST",
      data: {
        action: "load_manga",
        nonce: ajax_manga_params.nonce,
        page: page,
      },
      beforeSend: function () {
        $("#recently-up-ajax").html('<div class="loading"></div>'); // Show loading message
      },
      success: function (response) {
        $("#recently-up-ajax").html(response.data.manga_html);

        // Update total pages based on the response
        totalPages = response.data.total_pages;

        // Check if the response contains a message indicating no more posts
        if (totalPages <= currentPage) {
          $(".nav.next").addClass("disabled"); // Disable the next button if no more posts are available
        } else {
          $(".nav.next").removeClass("disabled"); // Enable the next button if there are more posts
        }

        if (currentPage === 1) {
          $(".nav.prev").addClass("disabled"); // Disable the previous button on the first page
        } else {
          $(".nav.prev").removeClass("disabled"); // Enable the previous button if not on the first page
        }

        // Re-bind bookmark click handler to newly loaded content
        bindBookmarkEvents();
      },
    });
  }

  // Initial load of manga posts
  loadManga(currentPage);

  // Next/previous pagination
  $(".nav.next").click(function () {
    if (currentPage < totalPages) {
      currentPage++;
      loadManga(currentPage);
    }
  });

  $(".nav.prev").click(function () {
    if (currentPage > 1) {
      currentPage--;
      loadManga(currentPage);
    }
  });

  /*--------------------------------------------------------------
    >>> BOOKMARK
    ----------------------------------------------------------------*/

  // Counter to create unique IDs for each toast message
  let toastCounter = 0;

  // Bind bookmark event handlers
  bindBookmarkEvents();

  // Function to handle bookmarking
  function bindBookmarkEvents() {
    $(".bookmark-button")
      .off("click")
      .on("click", function (e) {
        e.preventDefault();

        var post_id = $(this).data("post-id"); // Get the post ID from data attribute

        $.ajax({
          url: ajax_manga_params.ajax_url,
          type: "POST",
          dataType: "json",
          data: {
            action: "toggle_bookmark",
            post_id: post_id,
            nonce: ajax_manga_params.nonce,
          },
          success: function (response) {
            if (response.success) {
              // Display success message
              showToast("success", response.data.message);
            } else {
              // Display error message
              showToast("danger", response.data.message);
            }
          },
          error: function () {
            // Display error message on failed request
            showToast(
              "danger",
              "An error occurred while processing your request"
            );
          },
        });
      });
  }

  // Function to display the toast message with a unique ID
  function showToast(type, message) {
    // Increment the toast counter to create a unique ID
    toastCounter++;

    // Define the icon based on the type of message
    var icon =
      type === "success"
        ? '<i class="fa-solid fa-check-circle"></i>'
        : '<i class="fa-solid fa-exclamation-circle"></i>';

    // Create a unique ID for the toast
    var toastId = `toast-${toastCounter}`;

    // Append the message to the #toast div with a unique ID
    $("#toast").append(`
            <div class="alert alert-${type}" id="${toastId}" style="display: block;">
                ${icon}<span class="mx-2">${message}</span>
                <button type="button" class="close" data-dismiss="alert"><span>×</span></button>
            </div>
        `);

    // Fade out the toast after 3 seconds and remove it from the DOM
    setTimeout(function () {
      $(`#${toastId}`).fadeOut("slow", function () {
        $(this).remove(); // Remove the alert after fade out
      });
    }, 3000);
  }

  // Optional: Handle the manual close of the alerts
  $("#toast").on("click", ".close", function () {
    $(this)
      .closest(".alert")
      .fadeOut("slow", function () {
        $(this).remove();
      });
  });

  /*--------------------------------------------------------------
    >>> PROFILE SETTINGS
    ----------------------------------------------------------------*/

  // Profile update AJAX logic
  $("#profile-form-submit").on("click", function (e) {
    e.preventDefault(); // Prevent default form submission

    var form = $("#profile-form")[0]; // Get the profile form element
    var formData = new FormData(form); // Create FormData object from form

    // Show the loading spinner
    $(".loading").show();

    $.ajax({
      url: ajax_manga_params.ajax_url,
      type: "POST",
      data: formData,
      processData: false, // Prevent jQuery from automatically processing the data
      contentType: false, // Set content type to false to allow FormData
      headers: {
        "X-Requested-With": "XMLHttpRequest", // Set header to indicate AJAX request
      },
      success: function (data) {
        var messageContainer = $("#response-message");
        messageContainer.show(); // Show message container

        if (data.success) {
          messageContainer
            .removeClass("alert-danger")
            .addClass("alert-success"); // Add success class
          messageContainer.html(data.data.message); // Display success message

          // Redirect to home page if the response indicates a redirect
          if (data.data.redirect) {
            setTimeout(() => {
              window.location.href = "<?php echo home_url(); ?>"; // Redirect to home page after 3 seconds
            }, 1000);
          }
        } else {
          messageContainer
            .removeClass("alert-success")
            .addClass("alert-danger"); // Add error class
          messageContainer.html(data.data.message); // Display error message
        }
      },
      error: function (error) {
        console.error("Error:", error); // Handle any errors
        var messageContainer = $("#response-message");
        messageContainer.show(); // Show message container
        messageContainer.removeClass("alert-success").addClass("alert-danger"); // Add error class
        messageContainer.html("An error occurred. Please try again."); // Display error message
      },
      complete: function () {
        // Hide the loading spinner after the AJAX call is complete
        $(".loading").hide();
      },
    });
  });

  /*--------------------------------------------------------------
    >>> LOGIN
    ----------------------------------------------------------------*/

  $("#login-form").on("submit", function (e) {
    e.preventDefault();
    var $form = $(this);
    var login = $form.find('input[name="login"]').val();
    var password = $form.find('input[name="password"]').val();
    var nonce = ajax_manga_params.nonce;

    $("#responseMessage")
      .hide()
      .removeClass("alert-success alert-danger")
      .text("");
    $(".loading").show();

    $.ajax({
      url: ajax_manga_params.ajax_url,
      type: "POST",
      data: {
        action: "process_login",
        login: login,
        password: password,
        nonce: nonce,
      },
      success: function (response) {
        $(".loading").hide();

        if (response.success) {
          $("#responseMessage")
            .show()
            .removeClass("alert-danger")
            .addClass("alert-success")
            .text("Login successful! Redirecting...");
          setTimeout(function () {
            window.location.href = ajax_manga_params.site_url; // Dynamic redirect
          }, 1000);
        } else {
          $("#responseMessage")
            .show()
            .removeClass("alert-success")
            .addClass("alert-danger")
            .text(response.message);
        }
      },
      error: function () {
        $(".loading").hide();
        $("#responseMessage")
          .show()
          .removeClass("alert-success")
          .addClass("alert-danger")
          .text("An error occurred while processing your request");
      },
    });
  });

  /*--------------------------------------------------------------
    >>> REGISTER
    ----------------------------------------------------------------*/

  // Handle registration logic (unchanged)
  $("#register-form").on("submit", function (e) {
    e.preventDefault();
    var $form = $(this);
    var username = $form.find('input[name="username"]').val();
    var email = $form.find('input[name="email"]').val();
    var password = $form.find('input[name="password"]').val();
    var nonce = ajax_manga_params.nonce;

    $("#responseMessage")
      .hide()
      .removeClass("alert-success alert-danger")
      .text("");
    $(".loading").show();

    $.ajax({
      url: ajax_manga_params.ajax_url,
      type: "POST",
      data: {
        action: "process_register",
        username: username,
        email: email,
        password: password,
        nonce: nonce,
      },
      success: function (response) {
        $(".loading").hide();

        if (response.success) {
          $("#responseMessage")
            .show()
            .addClass("alert-success")
            .text("Registration successful! Redirecting...");
          setTimeout(function () {
            window.location.href = ajax_manga_params.site_url;
          }, 1000);
        } else {
          $("#responseMessage")
            .show()
            .addClass("alert-danger")
            .text(response.message);
        }
      },
      error: function () {
        $(".loading").hide();
        $("#responseMessage")
          .show()
          .addClass("alert-danger")
          .text("An error occurred while processing your request");
      },
    });
  });

  /*--------------------------------------------------------------
    >>> UPLOAD CHAPTER BUTTON
    ----------------------------------------------------------------*/

  // Bind upload chapter button click event
  $(".up-chapter-btn")
    .off("click")
    .on("click", function (e) {
      e.preventDefault(); // Prevent default button behavior

      // Check if user is logged in
      if (ajax_manga_params.is_logged_in === "true") {
        // Redirect to the upload chapter page
        var postUrl = window.location.href; // Get current manga post URL
        window.location.href = postUrl + "?upload-chapter";
      } else {
        // Alert message when not logged in
        showToast("danger", "Please login to upload a chapter");
      }
    });

  /*--------------------------------------------------------------
    >>> UPLOAD CHAPTER ZIP
    ----------------------------------------------------------------*/

  $("#uploadChapterForm").on("submit", function (e) {
    e.preventDefault(); // Prevent the default form submission

    // Clear previous messages
    $("#responseMessage")
      .hide()
      .removeClass("alert-danger alert-success")
      .text("");
    $(".progress").hide(); // Hide progress bar initially
    $(".loading").show(); // Show loading while validation happens

    // Validation check
    var chapterNumber = $('input[name="chapter-number"]').val();
    var chapterFile = $("#chapter-file").prop("files")[0];

    if (!chapterNumber || !chapterFile) {
      // If validation fails, show error message and stop further execution
      $("#responseMessage")
        .removeClass("alert-success")
        .addClass("alert-danger")
        .html("Chapter number and chapter file are required.")
        .show();
      $(".loading").hide(); // Hide loading on validation failure
      return;
    }

    var formData = new FormData(this);
    formData.append("action", "upload_chapter"); // Set the action for the AJAX request
    formData.append("nonce", ajax_manga_params.nonce); // Add nonce for security

    // Create an XMLHttpRequest
    var xhr = new XMLHttpRequest();
    xhr.open("POST", ajax_manga_params.ajax_url, true);

    // Progress callback
    xhr.upload.onprogress = function (event) {
      if (event.lengthComputable) {
        var percentComplete = (event.loaded / event.total) * 100;
        $("#uploadProgress")
          .css("width", percentComplete + "%")
          .attr("aria-valuenow", percentComplete)
          .text(Math.round(percentComplete) + "%");
        $(".progress").show(); // Show progress bar when upload starts
      }
    };

    // Success callback
    xhr.onload = function () {
      $(".loading").hide(); // Hide loading element after response

      if (xhr.status === 200) {
        var response = JSON.parse(xhr.responseText);
        if (response.success) {
          $("#responseMessage")
            .removeClass("alert-danger")
            .addClass("alert-success")
            .html(response.data)
            .show(); // Use .html() instead of .text()
          $("#uploadChapterForm")[0].reset(); // Clear the form on success
        } else {
          $("#responseMessage")
            .removeClass("alert-success")
            .addClass("alert-danger")
            .html(response.data)
            .show(); // Use .html() instead of .text()
        }
      } else {
        $("#responseMessage")
          .removeClass("alert-success")
          .addClass("alert-danger")
          .text("An error occurred. Please try again")
          .show();
      }
      $(".progress").hide(); // Hide progress bar after completion
    };

    // Error callback
    xhr.onerror = function () {
      $("#responseMessage")
        .removeClass("alert-success")
        .addClass("alert-danger")
        .text("An error occurred. Please try again")
        .show();
      $(".loading").hide(); // Hide loading element on error
      $(".progress").hide(); // Hide progress bar on error
    };

    // Start upload after validation, hide loading, show progress
    $(".loading").hide(); // Hide loading before starting the upload
    $(".progress").show(); // Show the progress bar

    // Send the request
    xhr.send(formData);
  });
});

$(document).ready(function () {
  const widths = [100, 90, 70, 50, 40]; // Define widths as percentages
  let currentIndex = 0;

  // Set/get cookie
  const setCookie = (name, value, days) => {
    let expires = "";
    if (days) {
      const date = new Date();
      date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
      expires = "; expires=" + date.toUTCString();
    }
    document.cookie = `${name}=${value || ""}${expires}; path=/`;
  };

  const getCookie = (name) => {
    const nameEQ = `${name}=`;
    return (
      document.cookie
        .split(";")
        .map((c) => c.trim())
        .find((c) => c.startsWith(nameEQ))
        ?.substring(nameEQ.length) || null
    );
  };

  // Image width change functionality
  const $chImages = $("#ch-images");
  const $button = $("#change-width");

  if ($chImages.length && $button.length) {
    const savedWidth = getCookie("chImageWidth"); // Get saved width from cookie

    // Determine initial width based on saved cookie or screen size
    if (savedWidth) {
      currentIndex = widths.indexOf(parseInt(savedWidth)); // Use saved width if available
    } else if (window.innerWidth <= 767) {
      // Mobile size
      currentIndex = widths.indexOf(100); // Default to 100% for mobile
    } else {
      // Larger screens
      currentIndex = widths.indexOf(50); // Default to 50% for larger screens
    }

    const applyWidth = (width) => {
      $chImages.css("width", width + "%");
      $button.find("span span").text(width + "%");
      setCookie("chImageWidth", width, 7);
    };

    applyWidth(widths[currentIndex]); // Apply the initial width

    // Button click handler
    $button.on("click", () => {
      currentIndex = (currentIndex + 1) % widths.length; // Increment index for the next width
      applyWidth(widths[currentIndex]); // Apply the next width
    });
  }

  // Prevent dropdown closing on click inside
  $(".dropdown-menu.noclose").on("click", (e) => e.stopPropagation());

  // Toggle content with jQuery's slideToggle using specific classes
  $(".ani-toggle").on("click", function (e) {
    e.preventDefault(); // Prevent default anchor behavior
    const targetClass = $(this).data("target"); // Get the target class from data attribute
    const targetElement = $("." + targetClass); // Find the element with that target class
    if (targetElement.length) targetElement.slideToggle(150); // Toggle the target element
  });

  $("#number-close").on("click", () =>
    $("#number-panel").removeClass("active")
  );
  $("#nav-menu-btn").on("click", () => $("#nav-menu > ul").toggle());

  // Remove Disqus ads
  setInterval(() => $("iframe[sandbox]").remove(), 500);

  // Initialize Swipers
  const sliders = [
    {
      selector: ".trending",
      options: {
        slidesPerView: 1,
        loop: true, // Enable infinite loop
        // autoplay: { delay: 8000 },  // Enable autoplay with a 3-second delay
        navigation: {
          nextEl: ".trending-button-next",
          prevEl: ".trending-button-prev",
        },
        breakpoints: {
          700: { slidesPerView: 1 },
          992: { slidesPerView: 2 },
          1400: { slidesPerView: 3 },
        },
      },
    },
    {
      selector: ".swiper-msv",
      options: {
        slidesPerView: 2,
        breakpoints: {
          400: { slidesPerView: 3 },
          640: { slidesPerView: 4 },
          768: { slidesPerView: 5 },
          992: { slidesPerView: 6 },
          1200: { slidesPerView: 7 },
        },
      },
    },
    {
      selector: ".swiper-nrs",
      options: {
        slidesPerView: 2,
        navigation: {
          nextEl: ".complete-button-next",
          prevEl: ".complete-button-prev",
        },
        breakpoints: {
          400: { slidesPerView: 3 },
          640: { slidesPerView: 4 },
          768: { slidesPerView: 5 },
          992: { slidesPerView: 6 },
          1200: { slidesPerView: 7 },
        },
      },
    },
  ];
  sliders.forEach((slider) => new Swiper(slider.selector, slider.options));

  // Go to top
  $("#go-top").on("click", () =>
    $("html, body").animate({ scrollTop: 0 }, "smooth")
  );

  // Chapter reading menu
  const $ctrlMenu = $("#ctrl-menu");
  if ($ctrlMenu.length && getCookie("ctrlMenuActive") === "true")
    $ctrlMenu.addClass("active");

  const toggleCtrlMenu = () => {
    $ctrlMenu.toggleClass("active");
    setCookie("ctrlMenuActive", $ctrlMenu.hasClass("active"), 7);
  };

  $("#show-ctrl-menu, .page.fit-w").on("click", toggleCtrlMenu);
  $("#ctrl-menu-close").on("click", () => {
    $ctrlMenu.removeClass("active");
    setCookie("ctrlMenuActive", "false", 7);
  });

  $(".number-toggler").on("click", () =>
    $("#number-panel").toggleClass("active")
  );

  // Toggle adult content
  const toggleAdultContent = () => {
    const newSetting = getCookie("show_adult") === "1" ? "0" : "1";
    setCookie("show_adult", newSetting, 7);
    location.reload();
  };

  $(".show-adult").on("click", toggleAdultContent);

  // Show/Hide change width button based on screen size
  const toggleChangeWidthButton = () => {
    if (window.innerWidth <= 767) {
      // If the screen width is 767px or less (mobile)
      $button.addClass("hidden"); // Hide the button
    } else {
      $button.removeClass("hidden"); // Show the button
    }
  };

  // Initial check
  toggleChangeWidthButton();

  // Re-check on resize
  $(window).on("resize", toggleChangeWidthButton);

  $(".tab").on("click", function () {
    const index = $(this).index();

    $(".tab").removeClass("active").eq(index).addClass("active");
    $(".tab-content").hide().eq(index).show();
  });
});
