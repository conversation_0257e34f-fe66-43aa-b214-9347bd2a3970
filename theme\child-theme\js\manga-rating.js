document.addEventListener("DOMContentLoaded", function () {
  console.log("Rating script loaded");

  const ratingSection = document.querySelector(".star-rating");
  if (!ratingSection) {
    console.log("Rating section not found");
    return;
  }

  const stars = ratingSection.querySelectorAll(".stars-display i");
  const postId = ratingSection.dataset.postId;
  const voteCountElement = ratingSection.querySelector(".vote-count");

  console.log("PostID:", postId);

  // Hover effect
  stars.forEach((star) => {
    star.addEventListener("mouseenter", function () {
      const rating = parseInt(this.dataset.rating);
      stars.forEach((s) => {
        const starValue = parseInt(s.dataset.rating);
        if (starValue <= rating) {
          s.classList.remove("far", "fa-star-half-alt");
          s.classList.add("fas", "fa-star");
        } else {
          s.classList.remove("fas", "fa-star-half-alt");
          s.classList.add("far", "fa-star");
        }
      });
    });
  });

  ratingSection.addEventListener("mouseleave", function () {
    updateStarsDisplay(
      parseFloat(voteCountElement.textContent.replace(/[()]/g, ""))
    );
  });

  // Click to vote
  stars.forEach((star) => {
    star.addEventListener("click", function () {
      const rating = parseInt(this.dataset.rating);
      console.log("Star clicked:", rating);
      submitVote(postId, rating);
    });
  });

  function submitVote(postId, rating) {
    console.log("Envoi du vote:", { postId, rating });

    const originalContent = voteCountElement.textContent;
    voteCountElement.textContent = "Soumission...";

    fetch(mangaRating.ajaxurl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        action: "manga_submit_rating",
        post_id: postId,
        rating: rating,
        nonce: mangaRating.nonce,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        console.log("Response data:", data);

        if (data.success) {
          const newRating = data.new_rating || "0.0";
          voteCountElement.textContent = newRating;
          updateStarsDisplay(parseFloat(newRating));
        } else {
          voteCountElement.textContent = originalContent;
          alert(data.message || "Échec de soumission du vote");
        }
      })
      .catch((error) => {
        console.error("Error:", error);
        voteCountElement.textContent = originalContent;
        alert("Échec de soumission du vote");
      });
  }

  function updateStarsDisplay(rating) {
    // Convert 10-point rating to 5-star display
    const starRating = rating / 2;
    stars.forEach((star) => {
      const value = parseFloat(star.dataset.rating) / 2;
      if (value <= starRating) {
        star.classList.remove("far", "fa-star-half-alt");
        star.classList.add("fas", "fa-star");
      } else if (value - 0.5 <= starRating) {
        star.classList.remove("fas", "far");
        star.classList.add("fas", "fa-star-half-alt");
      } else {
        star.classList.remove("fas", "fa-star-half-alt");
        star.classList.add("far", "fa-star");
      }
    });
  }
});
