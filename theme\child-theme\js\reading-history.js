jQuery(document).ready(function ($) {
  console.log("Reading history script loaded");

  // Use event delegation for dynamic content
  $(document).on("click", ".reading-remove", function (e) {
    e.preventDefault();
    e.stopPropagation();

    const button = $(this);
    const mangaId = button.data("manga-id");
    const chapterId = button.data("chapter-id");
    const unitElement = button.closest(".unit");

    console.log("Clicked remove:", { mangaId, chapterId });

    // Use separate AJAX parameters for reading history
    $.ajax({
      url: readingHistoryVars.ajax_url,
      type: "POST",
      data: {
        action: "remove_from_reading_history",
        manga_id: mangaId,
        chapter_id: chapterId,
        nonce: readingHistoryVars.nonce,
      },
      success: function (response) {
        console.log("Response:", response);
        if (response.success) {
          unitElement.fadeOut(300, function () {
            $(this).remove();
          });
        } else {
          alert("Failed to remove from reading history");
        }
      },
      error: function (xhr, status, error) {
        console.error("AJAX Error:", error);
      },
    });
  });

  // Clear history with modal confirmation
  $("#clear-reading-history").on("click", function (e) {
    e.preventDefault();
    $("#clearHistoryModal").modal("show");
  });

  $("#confirmClear").on("click", function () {
    console.log("Sending AJAX request to clear history...");
    $.ajax({
      url: readingHistoryVars.ajax_url,
      type: "POST",
      data: {
        action: "clear_reading_history",
        nonce: readingHistoryVars.nonce,
      },
      success: function (response) {
        console.log("Response:", response);
        if (response.success) {
          window.location.reload();
        } else {
          alert("Failed to clear reading history. Please try again.");
        }
      },
      error: function (xhr, status, error) {
        console.error("AJAX Error:", error);
        alert("An error occurred. Please try again.");
      },
    });
    $("#clearHistoryModal").modal("hide");
  });
});
