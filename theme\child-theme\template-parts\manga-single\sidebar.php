<aside class="sidebar">
    <div>
        <div class="meta">
            <?php 
            // Get and validate the initial values
            $total_votes = get_post_meta(get_the_ID(), '_manga_total_votes', true);
            $total_votes = !empty($total_votes) ? floatval($total_votes) : 0;

            $user_votes = get_post_meta(get_the_ID(), '_manga_user_votes', true);
            $user_votes = is_array($user_votes) ? $user_votes : array();
            $vote_count = count($user_votes);

            $post_id = get_the_ID();
            ?>
            <div class="rating-section">
                <span>Rating:</span>
                <div class="star-rating" data-post-id="<?php echo esc_attr($post_id); ?>">
                    <div class="stars-display">
                        <?php
                        for ($i = 1; $i <= 5; $i++) {
                            if ($i <= $total_votes) {
                                echo '<i class="fas fa-star" data-rating="' . esc_attr($i) . '"></i>';
                            } else if ($i - 0.5 <= $total_votes) {
                                echo '<i class="fas fa-star-half-alt" data-rating="' . esc_attr($i) . '"></i>';
                            } else {
                                 echo '<i class="far fa-star" data-rating="' . esc_attr($i) . '"></i>';
                            }
                        }
                        ?>
                    </div>
                    <span class="vote-count">(<?php echo esc_html(number_format($total_votes, 1)); ?> - <?php echo esc_html($vote_count); ?> votes)</span>
                </div>
            </div><br>
            <?php if (!empty($authors)) : ?>
            <div>
                <span>Author:</span>
                <span>
                <?php foreach ($authors as $index => $author) : 
                    $author_link = get_term_link($author);
                    if (!is_wp_error($author_link)) : ?>
                    <a href="<?php echo $author_link; ?>"><?php echo $author->name; ?></a><?php echo ($index < count($authors) - 1) ? ', ' : ''; ?>
                    <?php endif; endforeach; ?>
                </span>
            </div><br>
            <?php endif; ?>

            <?php if (!empty($artists)) : ?>
            <div>
                <span>Artist:</span>
                <span>
                <?php foreach ($artists as $index => $artist) : 
                    $artist_link = get_term_link($artist);
                    if (!is_wp_error($artist_link)) : ?>
                    <a href="<?php echo $artist_link; ?>"><?php echo $artist->name; ?></a><?php echo ($index < count($artists) - 1) ? ', ' : ''; ?>
                    <?php endif; endforeach; ?>
                </span>
            </div><br>
            <?php endif; ?>

            <?php if (!empty($releases)) : ?>
            <div>
                <span>Release :</span>
                <span>
                    <?php foreach ($releases as $index => $release) : 
                        $release_link = get_term_link($release);
                        if (!is_wp_error($release_link)) : ?>
                        <a href="<?php echo $release_link; ?>"><?php echo $release->name; ?></a><?php echo ($index < count($releases) - 1) ? ', ' : ''; ?>
                    <?php endif; endforeach; ?>
                </span>
            </div><br>
            <?php endif; ?>

            <?php if (!empty($types) || !empty($manga_meta_type)) : ?>
            <div>
                <span>Type:</span>
                <span>
                    <?php if (!empty($types)) : ?>
                        <?php foreach ($types as $index => $type) : 
                            $type_link = get_term_link($type);
                            if (!is_wp_error($type_link)) : ?>
                            <a href="<?php echo $type_link; ?>"><?php echo $type->name; ?></a><?php echo ($index < count($types) - 1) ? ', ' : ''; ?>
                        <?php endif; endforeach; ?>
                    <?php else : ?>
                        <?php echo $manga_meta_type; ?>
                    <?php endif; ?>
                </span>
            </div><br>
            <?php endif; ?>
        </div>
    </div>
</aside>