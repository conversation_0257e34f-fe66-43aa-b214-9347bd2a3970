jQuery(document).ready(function ($) {
  // Handle individual bookmark removal
  $(".bookmark-remove").on("click", function (e) {
    e.preventDefault();
    const mangaId = $(this).data("manga-id");
    const $bookmarkItem = $(this).closest(".unit");

    $.ajax({
      url: bookmarkAjax.ajaxurl,
      type: "POST",
      data: {
        action: "remove_bookmark",
        manga_id: mangaId,
        nonce: bookmarkAjax.nonce,
      },
      success: function (response) {
        if (response.success) {
          $bookmarkItem.fadeOut(300, function () {
            $(this).remove();
            // Reload page if no items left
            if ($(".unit").length === 0) {
              location.reload();
            }
          });
        }
      },
    });
  });

  // Handle clear all bookmarks
  $("#clear-bookmarks").on("click", function () {
    var myModal = new bootstrap.Modal(
      document.getElementById("clearBookmarksModal")
    );
    myModal.show();
  });

  $("#confirmClearBookmarks").on("click", function () {
    $.ajax({
      url: bookmarkAjax.ajaxurl,
      type: "POST",
      data: {
        action: "clear_all_bookmarks",
        nonce: bookmarkAjax.nonce,
      },
      success: function (response) {
        if (response.success) {
          location.reload();
        }
      },
    });
  });
});
