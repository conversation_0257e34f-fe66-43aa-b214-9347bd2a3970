<?php

function wp_manga_enqueue_scripts() {
    wp_enqueue_media();
    wp_enqueue_script('manga-volumes-js', get_stylesheet_directory_uri() . '/functions/manga-volume/manga-volumes.js', array('jquery'), null, true);
    wp_enqueue_style('manga-volumes-css', get_stylesheet_directory_uri() . '/functions/manga-volume/manga-volumes.css');
}
add_action('admin_enqueue_scripts', 'wp_manga_enqueue_scripts');

// Register meta box
function wp_manga_add_volume_metabox() {
    add_meta_box(
        'wp-manga-volumes',         // Meta box ID
        'Manga Volumes',            // Title
        'wp_manga_volumes_meta_box', // Callback to render
        'wp-manga',                 // Custom post type ('wp-manga')
        'normal',                   // Context
        'high'                      // Priority
    );
}
add_action('add_meta_boxes', 'wp_manga_add_volume_metabox');

function wp_manga_volumes_meta_box($post) {
    $volumes = get_post_meta($post->ID, 'wp_manga_volumes', true);
    ?>
    <div id="wp-manga-volumes-container">
        <h4>Manga Volumes</h4>
        <button type="button" class="button" id="wp_manga_add_volume_button">Add Manga Volume</button>
        <button type="button" class="button" id="wp_manga_delete_all_volumes_button">Delete All Volumes</button>
        <button type="button" class="button" id="wp_manga_select_multiple_images_button">Add Multiple Volumes</button>
        <div id="wp-manga-volumes-grid">
            <?php if (!empty($volumes)) : ?>
                <?php foreach ($volumes as $index => $volume) : ?>
                    <div class="wp-manga-volume-card">
                        <img src="<?php echo esc_url($volume['image']); ?>" alt="" class="wp-manga-volume-image" />
                        <input type="hidden" name="wp_manga_volumes[<?php echo $index; ?>][image]" value="<?php echo esc_url($volume['image']); ?>" />
                        <input type="text" name="wp_manga_volumes[<?php echo $index; ?>][number]" placeholder="Volume Number" value="<?php echo esc_attr($volume['number']); ?>" />
                        <input type="text" name="wp_manga_volumes[<?php echo $index; ?>][chapter]" placeholder="Chapter Number" value="<?php echo esc_attr($volume['chapter']); ?>" />
                        <button type="button" class="button wp_manga_select_image_button">Select/Upload Image</button>
                        <button type="button" class="button wp-manga-remove-volume-button">Delete Volume</button>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
    <?php
}

function wp_manga_save_volumes($post_id) {
    // Check for autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Check user permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Update or delete meta field
    if (isset($_POST['wp_manga_volumes'])) {
        $volumes = $_POST['wp_manga_volumes'];
        $cleaned_volumes = array_filter($volumes, function($volume) {
            return !empty($volume['image']);
        });
        update_post_meta($post_id, 'wp_manga_volumes', $cleaned_volumes);
    } else {
        delete_post_meta($post_id, 'wp_manga_volumes');
    }
}
add_action('save_post', 'wp_manga_save_volumes');