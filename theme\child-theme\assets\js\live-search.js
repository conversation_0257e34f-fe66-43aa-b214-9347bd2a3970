document.addEventListener("DOMContentLoaded", function () {
  const searchInput = document.querySelector('#searchform input[name="s"]');
  const suggestionBox = document.querySelector("#search-suggestions");

  if (!searchInput || !suggestionBox) return; // Check if elements exist

  let debounceTimeout;
  let currentQuery = "";

  function debounce(func, delay) {
    return function (...args) {
      clearTimeout(debounceTimeout);
      debounceTimeout = setTimeout(() => func.apply(this, args), delay);
    };
  }

  function fetchSearchResults(query) {
    if (query.length === 0) {
      suggestionBox.innerHTML = "";
      suggestionBox.style.display = "none";
      return;
    }

    suggestionBox.innerHTML = "<p>Loading...</p>";
    suggestionBox.style.display = "block";

    fetch(`/wp-json/myplugin/v1/search?query=${encodeURIComponent(query)}`)
      .then((response) => response.json())
      .then((data) => {
        if (query !== currentQuery) {
          return;
        }

        if (data.length === 0) {
          suggestionBox.innerHTML = "<p>No results found</p>";
          return;
        }

        const resultsHtml = data
          .map(
            (item) => `
                    <div class="original card-sm body">
                        <a class="unit" href="${item.url}">
                            <div class="poster">
                                <div><img src="${item.thumbnail}" /></div>
                            </div>
                            <div class="info">
                                <h6>${item.title}</h6>
                                <div><span>read</span></div>
                            </div>
                        </a>
                    </div>
                `
          )
          .join("");

        const viewAllUrl = `${
          window.location.origin
        }/?post_type=wp-manga&s=${encodeURIComponent(query)}`;

        suggestionBox.innerHTML = `
                    ${resultsHtml}
                    <div>
                        <a class="btn btn-primary w-100" href="${viewAllUrl}">
                            <span>View all Results</span> <i class="fa-solid fa-chevron-right fa-xs"></i>
                        </a>
                    </div>
                `;
      })
      .catch((error) => {
        console.error("Error fetching search results:", error);
        suggestionBox.innerHTML =
          "<p>Something went wrong. Please try again later.</p>";
      });
  }

  const debouncedFetchSearchResults = debounce((query) => {
    currentQuery = query;
    fetchSearchResults(query);
  }, 300);

  searchInput.addEventListener("input", function () {
    const query = searchInput.value.trim();
    debouncedFetchSearchResults(query);
  });

  document.addEventListener("click", function (event) {
    if (
      !searchInput.contains(event.target) &&
      !suggestionBox.contains(event.target)
    ) {
      suggestionBox.innerHTML = "";
      suggestionBox.style.display = "none";
    }
  });
});
