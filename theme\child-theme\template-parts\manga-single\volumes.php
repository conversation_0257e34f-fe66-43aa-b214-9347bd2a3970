<div class="list-menu">
    <button class="btn btn-secondary1 vol-order">
        <i class="fa-solid fa-arrow-down-arrow-up"></i>
        <span class="sort-text">الأحدث</span>
    </button>
    <form class="form-inline">
        <input class="form-control vol-filter" type="text" placeholder="رقم المجلد..." />
        <button class="btn">
            <i class="fa-regular fa-magnifying-glass"></i>
        </button>
    </form>
</div>

<?php if ($volumes && is_array($volumes)) { 
    $volumes = array_reverse($volumes); // Reverse the order of the volumes
?>
    <div class="list-body-hh">
        <div class="card-md vol-list scroll-sm">
            <?php foreach ($volumes as $index => $volume) { 
                $volume_number = !empty($volume['number']) ? esc_html($volume['number']) : '';
                $volume_chapter = !empty($volume['chapter']) ? esc_html($volume['chapter']) : '';
                $volume_image = !empty($volume['image']) ? esc_url($volume['image']) : '';
                $volume_url = $volume_image;
                ?>
                <div class="unit item" data-number="<?php echo $volume_number; ?>">
                    <a href="<?php echo esc_url($volume_url); ?>">
                        <div class="poster">
                            <div><img src="<?php echo $volume_image; ?>" alt="Volume Image" /></div>
                        </div>
                        <span><zebi>المجلد</zebi> <?php echo $volume_number; ?></span>
                    </a>
                </div>
            <?php } ?>
        </div>
    </div>
<?php } ?>

<script>
document.addEventListener('DOMContentLoaded', function () {
    let ascending = true;

    // Sorting volumes by number
    document.querySelector('.vol-order').addEventListener('click', function () {
        const container = document.querySelector('.card-md.vol-list');
        const items = Array.from(container.querySelectorAll('.unit.item'));

        items.sort((a, b) => {
            const numA = parseInt(a.getAttribute('data-number'));
            const numB = parseInt(b.getAttribute('data-number'));
            return ascending ? numA - numB : numB - numA; // Toggle between ascending and descending
        });

        items.forEach(item => container.appendChild(item));

        // Toggle sort order for next click
        ascending = !ascending;
        document.querySelector('.sort-text').textContent = ascending ? 'الأحدث' : 'الأقدم';
    });

    // Filtering volumes by number
    document.querySelector('.vol-filter').addEventListener('input', function () {
        const query = this.value.toLowerCase();
        const items = document.querySelectorAll('.card-md.vol-list .unit.item');

        items.forEach(item => {
            const number = item.getAttribute('data-number').toString();
            item.style.display = number.includes(query) ? '' : 'none';
        });
    });
});
</script>
