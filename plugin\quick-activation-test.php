<?php
/**
 * Quick activation test to verify all conflicts are resolved
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test if all functions can be loaded without conflicts
 */
function wp_manga_subscription_quick_test() {
    $results = array();
    
    // Test 1: Check if constants are properly defined
    $results['constants'] = array(
        'WP_MANGA_CHAPTER_COIN_LICENSE_KEY' => defined('WP_MANGA_CHAPTER_COIN_LICENSE_KEY'),
        'MANGA_CHAPTER_COIN_TEXT_DOMAIN' => defined('MANGA_CHAPTER_COIN_TEXT_DOMAIN'),
        'WP_MANGA_SUBSCRIPTION_DEV_MODE' => defined('WP_MANGA_SUBSCRIPTION_DEV_MODE')
    );
    
    // Test 2: Check if main classes exist
    $results['classes'] = array(
        'WP_MANGA_ADDON_CHAPTER_COIN' => class_exists('WP_MANGA_ADDON_CHAPTER_COIN'),
        'WP_MANGA_SUBSCRIPTION_MANAGER' => class_exists('WP_MANGA_SUBSCRIPTION_MANAGER'),
        'WP_MANGA_ADDON_CHAPTER_COIN_BACKEND' => class_exists('WP_MANGA_ADDON_CHAPTER_COIN_BACKEND')
    );
    
    // Test 3: Check if key functions exist
    $results['functions'] = array(
        'wp_manga_chapter_coin_get_settings' => function_exists('wp_manga_chapter_coin_get_settings'),
        'wp_manga_chapter_coin_user_has_subscription' => function_exists('wp_manga_chapter_coin_user_has_subscription'),
        'wp_manga_chapter_coin_get_subscription_plans' => function_exists('wp_manga_chapter_coin_get_subscription_plans'),
        'wp_manga_user_subscription_shortcode' => function_exists('wp_manga_user_subscription_shortcode')
    );
    
    // Test 4: Check if database functions exist
    $results['database_functions'] = array(
        'wmcc_setup_db' => function_exists('wmcc_setup_db'),
        'wmcc_initialize_subscription_plans' => function_exists('wmcc_initialize_subscription_plans')
    );
    
    return $results;
}

/**
 * Display quick test results
 */
function wp_manga_subscription_show_quick_test() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $results = wp_manga_subscription_quick_test();
    
    echo '<div class="wrap">';
    echo '<h1>🚀 Quick Activation Test</h1>';
    
    $all_passed = true;
    
    foreach ($results as $category => $tests) {
        echo '<div class="card" style="max-width: none; margin-bottom: 20px;">';
        echo '<h2>' . ucfirst(str_replace('_', ' ', $category)) . '</h2>';
        echo '<table class="widefat">';
        
        foreach ($tests as $test_name => $result) {
            $status = $result ? '✅ Available' : '❌ Missing';
            $row_color = $result ? '#f0fff0' : '#fff0f0';
            
            if (!$result) {
                $all_passed = false;
            }
            
            echo '<tr style="background: ' . $row_color . ';">';
            echo '<td style="padding: 10px;">' . esc_html($test_name) . '</td>';
            echo '<td style="padding: 10px;">' . $status . '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
        echo '</div>';
    }
    
    // Overall status
    echo '<div class="card" style="max-width: none; padding: 20px; ' . 
         ($all_passed ? 'background: #d4edda; border-left: 4px solid #28a745;' : 'background: #f8d7da; border-left: 4px solid #dc3545;') . '">';
    
    if ($all_passed) {
        echo '<h2 style="color: #155724;">🎉 All Tests Passed!</h2>';
        echo '<p style="color: #155724;">The plugin should now activate without fatal errors. All function conflicts have been resolved.</p>';
        echo '<p><strong>Next Steps:</strong></p>';
        echo '<ol>';
        echo '<li>Try activating the main plugin</li>';
        echo '<li>Check for any remaining issues</li>';
        echo '<li>Run the full system test if needed</li>';
        echo '</ol>';
    } else {
        echo '<h2 style="color: #721c24;">⚠️ Some Issues Found</h2>';
        echo '<p style="color: #721c24;">There are still some missing components. Please check the failed items above.</p>';
    }
    
    echo '</div>';
    
    // Development mode status
    if (defined('WP_MANGA_SUBSCRIPTION_DEV_MODE') && WP_MANGA_SUBSCRIPTION_DEV_MODE) {
        echo '<div class="notice notice-info" style="margin-top: 20px;">';
        echo '<p><strong>Development Mode:</strong> License key system is bypassed. The plugin should activate without license restrictions.</p>';
        echo '</div>';
    }
    
    echo '</div>';
}

// Add quick test page to admin menu
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            'tools.php',
            'Quick Activation Test',
            'Quick Activation Test',
            'manage_options',
            'manga-quick-activation-test',
            'wp_manga_subscription_show_quick_test'
        );
    }
});

// Show activation status in admin notices
add_action('admin_notices', function() {
    // Check if user functions are available
    if (!function_exists('current_user_can') || !current_user_can('manage_options')) {
        return;
    }
    
    // Don't show on test pages
    if (isset($_GET['page']) && strpos($_GET['page'], 'manga-') !== false) {
        return;
    }
    
    $results = wp_manga_subscription_quick_test();
    $all_passed = true;
    
    foreach ($results as $tests) {
        foreach ($tests as $result) {
            if (!$result) {
                $all_passed = false;
                break 2;
            }
        }
    }
    
    if (!$all_passed) {
        echo '<div class="notice notice-warning">';
        echo '<p><strong>WP Manga Subscription:</strong> Some components are missing. ';
        echo '<a href="' . admin_url('tools.php?page=manga-quick-activation-test') . '">Run Quick Test</a> to see details.</p>';
        echo '</div>';
    } else {
        // Only show success notice once
        if (get_transient('wp_manga_subscription_show_success_notice')) {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>WP Manga Subscription:</strong> All components loaded successfully! The plugin should activate without issues.</p>';
            echo '</div>';
            delete_transient('wp_manga_subscription_show_success_notice');
        }
    }
});

// Set success notice transient when plugin loads successfully
if (class_exists('WP_MANGA_ADDON_CHAPTER_COIN') && !get_transient('wp_manga_subscription_show_success_notice')) {
    set_transient('wp_manga_subscription_show_success_notice', true, 60); // Show for 1 minute
}
