<?php 

if( !get_option('enable_requests', 1) ) {
    return;
}

function create_requests_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'manga_requests';

    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) UNSIGNED DEFAULT NULL,
        username varchar(255) NOT NULL,
        manga_title varchar(255) NOT NULL,
        content text NOT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
        PRIMARY KEY (id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}
add_action('after_switch_theme', 'create_requests_table');


function handle_submit_request() {
    // Check if necessary fields are set
    if (!isset($_POST['title'], $_POST['message'], $_POST['nonce'])) {
        wp_send_json_error('Invalid request.');
        wp_die();
    }

    // Verify nonce for security
    if (!check_ajax_referer('load_manga_nonce', 'nonce', false)) {
        wp_send_json_error('Security check failed. Invalid nonce.');
        wp_die();
    }

    $user_id = is_user_logged_in() ? get_current_user_id() : null;

    // Rate limiting (limit one request per minute)
    if ($user_id) {
        $last_request_time = get_user_meta($user_id, 'last_request_time', true);
        if ($last_request_time && (time() - $last_request_time) < 60) {
            wp_send_json_error('You are submitting requests too quickly. Please wait a while before submitting again.');
            wp_die();
        }
        update_user_meta($user_id, 'last_request_time', time());
    }

    // Backend validation for the 'title' field
    if (empty($_POST['title'])) {
        wp_send_json_error('إسم المانجا مطلوب');
        wp_die();
    }

    // Prepare and sanitize input
    $username = $user_id ? wp_get_current_user()->user_login : 'Guest';
    
    $title = sanitize_text_field(wp_unslash($_POST['title']));
    $message = sanitize_textarea_field(wp_unslash($_POST['message']));

    // Ensure the title is not too long
    if (strlen($title) > 255) {
        wp_send_json_error('عنوان المانجا طويل جدًا.');
        wp_die();
    }

    // Ensure the message isn't too long (optional)
    if (strlen($message) > 1000) {
        wp_send_json_error('محتوى الرسالة طويل جدًا.');
        wp_die();
    }

    // Database insertion (safe from SQL injection)
    global $wpdb;
    $table_name = $wpdb->prefix . 'manga_requests';
    $result = $wpdb->insert($table_name, array(
        'user_id' => $user_id,
        'username' => $username,
        'manga_title' => $title,
        'content' => $message,
        'created_at' => current_time('mysql')
    ));

    if ($result) {
        wp_send_json_success(__('تم إرسال الطلب بنجاح! شكرا', 'child-mfire'));
    } else {
        error_log('Database insertion failed for manga request: ' . $wpdb->last_error);
        wp_send_json_error(__('حدث خطأ أثناء معالجة طلبك. الرجاء إعادة المحاولة لاحقا', 'child-mfire'));
    }

    wp_die();
}
add_action('wp_ajax_submit_request', 'handle_submit_request');
add_action('wp_ajax_nopriv_submit_request', 'handle_submit_request');

function requests_callback() { 
    global $wpdb;
    $table_name = $wpdb->prefix . 'manga_requests';
    
    // Fetch requests from the database
    $requests = $wpdb->get_results("SELECT * FROM $table_name ORDER BY id DESC");

    ?>
    <div class="requests-section" style="margin-right: 20px;">
        <h1>Manga Reports</h1>
        <form id="bulk-delete-form">
            <table class="widefat fixed">
                <thead>
                    <tr>
                        <th style="width: 50px;"><input type="checkbox" id="select-all"></th> <!-- Checkbox to select all -->
                        <th style="width: 80px;">User ID</th>
                        <th style="width: 80px;">Username</th>
                        <th style="width: 300px;">Manga Name</th>
                        <th>Content</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($requests): ?>
                        <?php foreach ($requests as $request): ?>
                            <tr id="request-row-<?php echo esc_attr($request->id); ?>">
                                <td><input type="checkbox" name="request_ids[]" value="<?php echo esc_attr($request->id); ?>"></td> <!-- Add checkbox for each row -->
                                <td><?php echo esc_html($request->user_id); ?></td>
                                <td><?php echo esc_html($request->username); ?></td>
                                <td><?php echo esc_html($request->manga_title); ?></td>
                                <td><?php echo esc_html($request->content); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="5"><?php _e('No requests found.', 'child-mfire'); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            <button type="button" id="bulk-delete-button" class="button button-primary" style="margin-top: 10px;">Delete Selected</button> <!-- Bulk delete button -->
        </form>
    </div>
    <?php 
}

// Handle bulk delete requests
function handle_bulk_delete_requests() {
    if (!isset($_POST['request_ids'], $_POST['nonce'])) {
        wp_send_json_error('Invalid request.');
        wp_die();
    }

    if (!check_ajax_referer('load_manga_nonce', 'nonce', false)) {
        wp_send_json_error('Security check failed. Invalid nonce.');
        wp_die();
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('You do not have permission to perform bulk delete.');
        wp_die();
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'manga_requests';
    $request_ids = array_map('intval', $_POST['request_ids']); 

    if (empty($request_ids)) {
        wp_send_json_error('No requests selected.');
        wp_die();
    }

    $ids = implode(',', $request_ids); 
    $result = $wpdb->query("DELETE FROM $table_name WHERE id IN ($ids)");

    if ($result) {
        wp_send_json_success(__('Selected requests deleted successfully!', 'child-mfire'));
    } else {
        error_log('Failed to bulk delete requests: ' . $wpdb->last_error);
        wp_send_json_error(__('Failed to delete selected requests.', 'child-mfire'));
    }

    wp_die();
}
add_action('wp_ajax_bulk_delete_requests', 'handle_bulk_delete_requests');