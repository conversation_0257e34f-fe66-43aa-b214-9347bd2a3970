<?php
/*
Template Name: User Settings
*/

// Get current tab or default to 'account-settings'
$tab = isset($_GET['tab']) ? $_GET['tab'] : 'account-settings';

// Check if the tab file exists before anything else
$tab_file = get_stylesheet_directory() . "/template-parts/user-pages/{$tab}.php";

if (!file_exists($tab_file)) {
    // Send 404 status code and load the 404 page if tab file does not exist
    include( get_query_template( '404' ) );
    exit; // Stop further execution after showing 404
}

// Check if the user is logged in
if (!is_user_logged_in()) {
    // Load the custom 401 error page template
    include(get_stylesheet_directory() . '/401.php'); // Load 401 page if not logged in
    exit; // Stop further execution
}

get_header();

// Include the valid tab file
include($tab_file);

get_footer();