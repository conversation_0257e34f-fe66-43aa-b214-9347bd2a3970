<?php
// Check if this file is being accessed directly
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Add Ajax actions
add_action('wp_ajax_live_search', 'manga_live_search_callback');
add_action('wp_ajax_nopriv_live_search', 'manga_live_search_callback');

function manga_live_search_callback() {
    require_once __DIR__ . '/cached-functions.php';
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'search_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    // Check if search query exists
    if (!isset($_POST['search'])) {
        wp_send_json_error('No search query provided');
        return;
    }

    try {
        $search_query = sanitize_text_field($_POST['search']);
        $cache_key = 'search_' . md5($search_query);
        $cached = manga_cache_get($cache_key);
        if ($cached !== false) {
            wp_send_json_success($cached);
            return;
        }
        $args = array(
            'post_type' => 'wp-manga',
            'post_status' => 'publish',
            's' => $search_query,
            'posts_per_page' => 5,
            'orderby' => 'relevance'
        );
        $query = new WP_Query($args);
        $results = array();
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $post_id = get_the_ID();
                // Get manga thumbnail
                $thumb = get_the_post_thumbnail_url($post_id, 'manga_cover');
                if (!$thumb) {
                    $thumb = get_stylesheet_directory_uri() . '/images/default-thumbnail.jpg';
                }
                // Get rating
                $rating = get_post_meta($post_id, '_manga_total_votes', true);
                if ($rating) {
                    $rating = number_format((float)$rating, 1);
                }
                // Get manga type
                $type = '';
                $terms = get_the_terms($post_id, 'wp-manga-type');
                if (!empty($terms) && !is_wp_error($terms)) {
                    $type = $terms[0]->name;
                }
                $results[] = array(
                    'title' => get_the_title(),
                    'url' => get_permalink(),
                    'thumbnail' => $thumb,
                    'rating' => $rating,
                    'type' => $type
                );
            }
        }
        wp_reset_postdata();
        manga_cache_set($cache_key, $results, 120); // Cache for 2 minutes
        wp_send_json_success($results);
    } catch (Exception $e) {
        wp_send_json_error('Search error: ' . $e->getMessage());
    }
}