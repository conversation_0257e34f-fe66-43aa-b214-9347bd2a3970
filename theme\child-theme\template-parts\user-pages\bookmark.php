<?php

$user_id = get_current_user_id();
// $bookmarked_mangas = get_user_meta($user_id, 'bookmarked_mangas', true) ?: array(); // Original line
$raw_bookmarked_data = get_user_meta($user_id, 'bookmarked_mangas', true);
if (!is_array($raw_bookmarked_data)) {
    $raw_bookmarked_data = array();
}

$show_adult = get_user_meta($user_id, 'show_adult_content', true) ?: false;
$paged = max(1, get_query_var('paged'));
$posts_per_page = 20;

$bookmarked_manga_ids = array(); // This will store the final list of IDs to query

if (!empty($raw_bookmarked_data)) {
    $first_element = reset($raw_bookmarked_data); // Get the first element to check its type

    if (is_array($first_element) && isset($first_element['id'])) {
        // New, complex format: array of associative arrays
        $processed_ids = array_map(function($item) {
            return isset($item['id']) ? (int)$item['id'] : null;
        }, $raw_bookmarked_data);
        // Filter out any nulls that might have resulted from malformed items
        $bookmarked_manga_ids = array_filter($processed_ids, function($id) { return $id !== null; });
    } elseif (!is_array($first_element) || empty($first_element)) { // Handle empty array or array of scalars
        // Old, simple format: array of IDs (or mixed, ensure all are int and valid)
        // Filter out non-numeric items before intval, and ensure IDs are positive.
        $numeric_ids = array_filter($raw_bookmarked_data, 'is_numeric');
        $bookmarked_manga_ids = array_map('intval', $numeric_ids);
        $bookmarked_manga_ids = array_filter($bookmarked_manga_ids, function($id) { return $id > 0; });
    }
    // If $raw_bookmarked_data was empty or unprocessable, $bookmarked_manga_ids remains empty.

    // Now, filter these IDs for adult content
    if (!$show_adult && !empty($bookmarked_manga_ids)) {
        $bookmarked_manga_ids = array_filter($bookmarked_manga_ids, function($manga_id) {
            // Ensure $manga_id is a valid ID (already int and > 0 from above)
            return get_post_meta($manga_id, 'manga_adult_content', true) !== 'yes';
        });
    }
}

// Ensure $bookmarked_manga_ids is a flat, unique list of integers, re-indexed.
$bookmarked_manga_ids = array_values(array_unique($bookmarked_manga_ids));

$bookmark_query = null; // Initialize $bookmark_query
$total_posts = 0;
$total_pages = 0;

if (empty($bookmarked_manga_ids)) {
    // $total_posts and $total_pages remain 0, $bookmark_query remains null.
    // The message "Vous n'avez encore ajouté aucun manga à vos favoris"
    // will be handled by checking empty($bookmarked_manga_ids) in the display part.
} else {
    // Pagination calculations
    $total_posts = count($bookmarked_manga_ids);
    $total_pages = ceil($total_posts / $posts_per_page);
    // Ensure paged is within bounds
    $paged = max(1, min($paged, $total_pages)); // $paged could be > $total_pages after filtering
    $offset = ($paged - 1) * $posts_per_page;

    // Get current page IDs and create query
    $current_page_ids = array_slice($bookmarked_manga_ids, $offset, $posts_per_page);
    
    if (!empty($current_page_ids)) {
        $bookmark_query = new WP_Query([
            'post_type'      => 'wp-manga',
            'post__in'       => $current_page_ids,
            'orderby'        => 'post__in', // Maintain the order from $current_page_ids
            'posts_per_page' => $posts_per_page,
            'no_found_rows'  => true, 
            'update_post_term_cache' => true, 
            'update_post_meta_cache' => true
        ]);
    }
}

// Define flag mapping once (moved from original position to be after PHP block if it was inside)
// This was line 36-41 in the semantic search result. Assuming it's outside the initial PHP block or can be placed after.
// If it's already correctly placed globally in the file, this re-declaration might not be needed here.
// For safety, ensuring it's defined before use if it was part of the replaced block.
if (!isset($type_to_flag) || !isset($flag_path)) {
    $type_to_flag = [
        'manga'  => 'jp.svg',
        'manhwa' => 'kr.svg',
        'manhua' => 'cn.svg'
    ];
    $flag_path = get_stylesheet_directory_uri() . '/assets/images/flags/';
}

?>

<main class="user-panel">
    <div class="container">
        <div class="main-inner">
        <aside class="sidebar">
                <ul class="user-nav">
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=account-settings'); ?>">
                            <i class="fa-solid fa-user"></i> <span>Compte</span>
                        </a>
                    </li>
                    <li>
                        <a class="active" href="<?php echo site_url('/user/?tab=bookmark'); ?>">
                            <i class="fa-solid fa-bookmark"></i> <span>Favoris</span>
                        </a>
                    </li>

                    <?php /* if( get_option('allow_post', 1) ) : ?>
                        <li>
                            <a class="" href="<?php echo site_url('/user/?tab=add-manga'); ?>">
                                <i class="fa-solid fa-circle-plus"></i> <span>Ajouter un manga</span>
                            </a>
                        </li>
                        <li>
                            <a class="" href="<?php echo site_url('/user/?tab=chapters'); ?>">
                                <i class="fa-solid fa-book-open-reader"></i> <span>Liste des chapitres</span>
                            </a>
                        </li>
                    <?php endif; */ ?>

                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=reading'); ?>">
                            <i class="fa-solid fa-clock-rotate-left"></i> <span>Historique</span>
                        </a>
                    </li>
                    <?php /*
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=notification'); ?>">
                            <i class="fa-solid fa-bell"></i> <span>Notifications</span>
                        </a>
                    </li>
                    */ ?>
                </ul>
            </aside>

            <aside class="content">
                <section>
                    <div class="head">
                        <h2>Favoris</h2>
                        <?php if (!empty($bookmarked_mangas)) : ?>
                            <button id="clearBookmarksBtn" class="btn btn-danger"><i class="fa-solid fa-trash"></i>Effacer vos favoris</button>
                        <?php endif; ?>
                    </div>

                    <!-- Add Modal HTML -->
                    <div id="clearBookmarksModal" class="modal fade bookmark-clear-modal">
                        <div class="modal-dialog modal-confirm bookmark-modal-dialog">
                            <div class="modal-content bookmark-modal-content">
                                <div class="modal-header bookmark-modal-header">
                                    <div class="icon-box bookmark-icon-box">
                                        <i class="fa-solid fa-trash"></i>
                                    </div>
                                    <h4 class="modal-title bookmark-modal-title">Êtes-vous sûr ?</h4>
                                </div>
                                <div class="modal-body bookmark-modal-body">
                                    <p>Voulez-vous vraiment effacer vos favoris ? Ce processus ne peut pas être annulé.</p>
                                </div>
                                <div class="modal-footer bookmark-modal-footer">
                                    <button type="button" class="btn btn-secondary" id="cancelClearBookmarks">Annuler</button>
                                    <button type="button" class="btn btn-danger" id="confirmClearBookmarks">Oui, effacez-le !</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="manga-bottom">
                        <div class="content">
                            <div class="m-list">
                                <div class="original card-lg reading">
                                    <?php if ($bookmark_query && $bookmark_query->have_posts()) : ?>
                                        <?php while ($bookmark_query->have_posts()) : $bookmark_query->the_post(); 
                                            $manga_id = get_the_ID();
                                            $types = wp_get_post_terms($manga_id, 'wp-manga-type');
                                            $comic_type = !empty($types) ? strtolower($types[0]->name) : '';
                                            $flag_image = isset($type_to_flag[$comic_type]) ? $flag_path . $type_to_flag[$comic_type] : '';
                                        ?>
                                            <div class="unit">
                                                <div class="inner">
                                                    <a href="<?php the_permalink(); ?>">
                                                        <div class="hposter">
                                                            <div class="poster-image-wrapper">
                                                                <?php if($flag_image): ?>
                                                                <div class="manga-flag width-limit" data-type="<?php echo esc_attr($comic_type); ?>">
                                                                    <img src="<?php echo esc_url($flag_image); ?>" 
                                                                         alt="<?php echo esc_attr($comic_type); ?>" 
                                                                         title="<?php echo esc_attr($comic_type); ?>" 
                                                                         class="flag-icon">
                                                                </div>
                                                                <?php endif; ?>
                                                                <div>
                                                                    <img src="<?php echo esc_url(get_the_post_thumbnail_url($manga_id, 'manga_cover')); ?>" 
                                                                         alt="<?php echo esc_attr(get_the_title()); ?>" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="info">
                                                            <div class="m-name"><?php echo esc_html(get_the_title()); ?></div>
                                                        </div>
                                                    </a>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                        <?php wp_reset_postdata(); ?>
                                    <?php else : ?>
                                        <p><?php echo empty($bookmarked_manga_ids) ? 'Vous n\'avez encore ajouté aucun manga à vos favoris' : 'Nothing found'; ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <ul class="pagination">
                        <li class="page-item <?php if ($paged == 1) echo 'disabled'; ?>">
                            <a class="page-link" href="<?php echo site_url('/user/?tab=bookmark&paged=1'); ?>" rel="first">«</a>
                        </li>
                        <li class="page-item <?php if ($paged <= 1) echo 'disabled'; ?>">
                            <a class="page-link" href="<?php echo site_url('/user/?tab=bookmark&paged=' . ($paged - 1)); ?>" rel="prev" aria-label="« Previous">‹</a>
                        </li>
                        <?php for ($i = 1; $i <= $total_pages; $i++) : ?>
                            <li class="page-item <?php if ($i == $paged) echo 'active'; ?>">
                                <a class="page-link" href="<?php echo site_url('/user/?tab=bookmark&paged=' . $i); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>
                        <li class="page-item <?php if ($paged >= $total_pages) echo 'disabled'; ?>">
                            <a class="page-link" href="<?php echo site_url('/user/?tab=bookmark&paged=' . ($paged + 1)); ?>" rel="next" aria-label="Next »">›</a>
                        </li>
                        <li class="page-item <?php if ($paged == $total_pages) echo 'disabled'; ?>">
                            <a class="page-link" href="<?php echo site_url('/user/?tab=bookmark&paged=' . $total_pages); ?>" rel="last">»</a>
                        </li>
                    </ul>

                </section>
            </aside>
        </div>
    </div>


    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const modalElement = document.getElementById('clearBookmarksModal');
        if (!modalElement) return;

        const modal = new bootstrap.Modal(modalElement);
        const clearBtn = document.getElementById('clearBookmarksBtn');
        const confirmBtn = document.getElementById('confirmClearBookmarks');
        const cancelBtn = document.getElementById('cancelClearBookmarks');

        const closeModal = () => {
            modalElement.style.display = 'none';
            modalElement.classList.remove('show');
            document.querySelector('.bookmark-clear-modal')?.classList.remove('show');
            document.querySelector('.modal-backdrop.bookmark-backdrop')?.remove();
        };

        clearBtn?.addEventListener('click', e => {
            e.preventDefault();
            modal.show();
        });

        cancelBtn?.addEventListener('click', e => {
            e.preventDefault();
            modal.hide();
        });

        confirmBtn?.addEventListener('click', async e => {
            e.preventDefault();
            e.stopPropagation();
            confirmBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('action', 'clear_all_bookmarks');
                formData.append('security', '<?php echo wp_create_nonce("clear_bookmarks_nonce"); ?>');

                await fetch('<?php echo admin_url("admin-ajax.php"); ?>', {
                    method: 'POST',
                    credentials: 'same-origin',
                    body: formData
                });
            } catch (error) {
                console.error('Error clearing bookmarks:', error);
            } finally {
                closeModal();
                window.location.reload();
            }
        });

        modalElement.addEventListener('show.bs.modal', () => {
            requestAnimationFrame(() => {
                document.querySelector('.modal-backdrop')?.classList.add('bookmark-backdrop');
            });
        });
    });
    </script>
</main>