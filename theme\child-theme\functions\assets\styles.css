/* General */
.wp-person a:focus .gravatar, a:focus, a:focus .media-icon img, a:focus .plugin-icon {
    box-shadow: none;
}
.nav-tab-wrapper .nav-tab:first-child {
    margin-left: 0;
}
/* ---- */

#wpadminbar .awaiting-mod {
    display: inline-block;
    box-sizing: border-box;
    margin: 1px 0 -1px 2px;
    padding: 0 5px;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    background-color: #d63638;
    color: #fff;
    font-size: 11px;
    line-height: 1.6;
    text-align: center;
    z-index: 26;
}

.user-upload-review-requests .column-actions, .user-upload-review-requests .column-primary {
    width: 80px;
}
table.user-upload-review-requests span.request-type {
    color: #ffff;
    background: #d73e3e;
    padding: 6px;
    margin: 5px;
    border-radius: 3.5px;
}
table.user-upload-review-requests span.request-type.pending_chapter {
    background: #009688;
}




.manga-import-form {
    width: 100%;
    max-width: 400px;
    margin: 20px auto;
    padding: 20px;
    background-color: #f4f4f4;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.manga-import-group {
    margin-bottom: 15px;
}

.manga-import-label {
    display: block;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.manga-import-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
}

.manga-import-input:focus {
    border-color: #0073e6;
    outline: none;
    box-shadow: 0 0 5px rgba(0, 115, 230, 0.3);
}

.manga-button-group {
    text-align: center;
    margin-top: 20px;
}

.manga-btn {
    background-color: #0073e6;
    color: white;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.manga-btn:hover {
    background-color: #005bb5;
}

.manga-btn:active {
    background-color: #004a99;
}

.manga-import-btn {
    width: 100%;
    max-width: 100%;
}

/* SETTINGS PAGE */

.settings-group {
    max-width: 500px;
}
.settings-group .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
}
.settings-group .toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}
.settings-group .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}
.settings-group .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}
.settings-group .slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .2s;
    border-radius: 50%;
}
.settings-group input:checked + .slider {
    background-color: #2196F3;
}
.settings-group input:checked + .slider:before {
    transform: translateX(26px);
}
.settings-group .input-field {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}
