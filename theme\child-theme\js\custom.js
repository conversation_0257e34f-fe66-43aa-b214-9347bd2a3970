jQuery(document).ready(function ($) {
  $("#clear-reading-history").on("click", function () {
    if (
      confirm("Are you sure you want to clear your entire reading history?")
    ) {
      $.ajax({
        url: ajaxurl,
        type: "POST",
        data: {
          action: "clear_reading_history",
          nonce: reading_history.nonce,
        },
        success: function (response) {
          if (response.success) {
            location.reload();
          } else {
            alert("Failed to clear reading history. Please try again.");
          }
        },
      });
    }
  });
});
