<?php
/**
 * Development Configuration for WP Manga Subscription
 * 
 * This file contains development settings and can be used to toggle
 * development features on/off easily.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Development Mode Settings
 */

// Enable/Disable Development Mode
// Set to false for production
define('WP_MANGA_SUBSCRIPTION_DEV_MODE', true);

// Development Features
define('WP_MANGA_SUBSCRIPTION_DEBUG_MODE', true);
define('WP_MANGA_SUBSCRIPTION_BYPASS_LICENSE', true);
define('WP_MANGA_SUBSCRIPTION_SHOW_DEBUG_INFO', true);

/**
 * Development Helper Functions
 */

/**
 * Check if plugin is in development mode
 */
function wp_manga_subscription_is_dev_mode() {
    return defined('WP_MANGA_SUBSCRIPTION_DEV_MODE') && WP_MANGA_SUBSCRIPTION_DEV_MODE;
}

/**
 * Check if debug mode is enabled
 */
function wp_manga_subscription_is_debug_mode() {
    return defined('WP_MANGA_SUBSCRIPTION_DEBUG_MODE') && WP_MANGA_SUBSCRIPTION_DEBUG_MODE;
}

/**
 * Log debug message (only in debug mode)
 */
function wp_manga_subscription_debug_log($message) {
    if (wp_manga_subscription_is_debug_mode()) {
        error_log('WP Manga Subscription Debug: ' . $message);
    }
}

/**
 * Show development admin notice
 */
function wp_manga_subscription_dev_admin_notice() {
    if (!wp_manga_subscription_is_dev_mode()) {
        return;
    }
    
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Don't show on the test pages
    if (isset($_GET['page']) && (
        $_GET['page'] === 'manga-subscription-activation-test' || 
        $_GET['page'] === 'manga-subscription-test'
    )) {
        return;
    }
    
    echo '<div class="notice notice-warning" style="border-left-color: #ff6900;">';
    echo '<p><strong>🚧 WP Manga Subscription - Development Mode</strong></p>';
    echo '<p>The plugin is running in development mode with the following features:</p>';
    echo '<ul style="margin-left: 20px;">';
    echo '<li>✅ License key system bypassed</li>';
    echo '<li>✅ Debug logging enabled</li>';
    echo '<li>✅ Development tools available</li>';
    echo '<li>✅ Error handling enhanced</li>';
    echo '</ul>';
    echo '<p><strong>Quick Links:</strong> ';
    echo '<a href="' . admin_url('tools.php?page=manga-subscription-activation-test') . '" class="button button-secondary">System Test</a> ';
    echo '<a href="' . admin_url('options-general.php?page=wp-manga-chapter-coin') . '" class="button button-secondary">Settings</a>';
    echo '</p>';
    echo '</div>';
}

// Add development admin notice
add_action('admin_notices', 'wp_manga_subscription_dev_admin_notice');

/**
 * Development Tools Menu
 */
function wp_manga_subscription_dev_menu() {
    if (!wp_manga_subscription_is_dev_mode()) {
        return;
    }
    
    if (!current_user_can('manage_options')) {
        return;
    }
    
    add_submenu_page(
        'tools.php',
        'Manga Subscription Dev Tools',
        'Manga Dev Tools',
        'manage_options',
        'manga-subscription-dev-tools',
        'wp_manga_subscription_dev_tools_page'
    );
}

add_action('admin_menu', 'wp_manga_subscription_dev_menu');

/**
 * Development Tools Page
 */
function wp_manga_subscription_dev_tools_page() {
    echo '<div class="wrap">';
    echo '<h1>🛠️ WP Manga Subscription - Development Tools</h1>';
    
    echo '<div class="card" style="max-width: none;">';
    echo '<h2>Development Status</h2>';
    echo '<table class="form-table">';
    echo '<tr><th>Development Mode</th><td>' . (wp_manga_subscription_is_dev_mode() ? '✅ Enabled' : '❌ Disabled') . '</td></tr>';
    echo '<tr><th>Debug Mode</th><td>' . (wp_manga_subscription_is_debug_mode() ? '✅ Enabled' : '❌ Disabled') . '</td></tr>';
    echo '<tr><th>License Bypass</th><td>' . (defined('WP_MANGA_SUBSCRIPTION_BYPASS_LICENSE') && WP_MANGA_SUBSCRIPTION_BYPASS_LICENSE ? '✅ Active' : '❌ Inactive') . '</td></tr>';
    echo '<tr><th>WordPress Debug</th><td>' . (defined('WP_DEBUG') && WP_DEBUG ? '✅ Enabled' : '❌ Disabled') . '</td></tr>';
    echo '</table>';
    echo '</div>';
    
    echo '<div class="card" style="max-width: none;">';
    echo '<h2>Quick Actions</h2>';
    echo '<p>';
    echo '<a href="' . admin_url('tools.php?page=manga-subscription-activation-test') . '" class="button button-primary">Run System Test</a> ';
    echo '<a href="' . admin_url('options-general.php?page=wp-manga-chapter-coin') . '" class="button button-secondary">Plugin Settings</a> ';
    echo '<a href="' . wp_nonce_url(admin_url('tools.php?page=manga-subscription-dev-tools&action=reset_db'), 'reset_db') . '" class="button button-secondary" onclick="return confirm(\'Are you sure you want to reset the database?\')">Reset Database</a>';
    echo '</p>';
    echo '</div>';
    
    // Handle actions
    if (isset($_GET['action']) && $_GET['action'] === 'reset_db' && wp_verify_nonce($_GET['_wpnonce'], 'reset_db')) {
        wp_manga_subscription_reset_database();
        echo '<div class="notice notice-success"><p>Database reset completed!</p></div>';
    }
    
    echo '<div class="card" style="max-width: none;">';
    echo '<h2>Database Information</h2>';
    
    global $wpdb;
    $plans_table = $wpdb->prefix . 'manga_subscription_plans';
    $subscriptions_table = $wpdb->prefix . 'manga_user_subscriptions';
    
    echo '<h3>Tables Status</h3>';
    echo '<ul>';
    echo '<li>Plans Table: ' . ($wpdb->get_var("SHOW TABLES LIKE '$plans_table'") == $plans_table ? '✅ Exists' : '❌ Missing') . '</li>';
    echo '<li>Subscriptions Table: ' . ($wpdb->get_var("SHOW TABLES LIKE '$subscriptions_table'") == $subscriptions_table ? '✅ Exists' : '❌ Missing') . '</li>';
    echo '</ul>';
    
    if (class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
        try {
            $manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
            $plans = $manager->get_subscription_plans();
            $stats = $manager->get_subscription_stats();
            
            echo '<h3>Subscription Plans (' . count($plans) . ')</h3>';
            if (!empty($plans)) {
                echo '<ul>';
                foreach ($plans as $plan) {
                    echo '<li>' . esc_html($plan->plan_name) . ' - ' . $plan->duration_months . ' months - $' . number_format($plan->price, 2) . '</li>';
                }
                echo '</ul>';
            } else {
                echo '<p>No plans found.</p>';
            }
            
            echo '<h3>Subscription Statistics</h3>';
            echo '<ul>';
            echo '<li>Active: ' . $stats['active'] . '</li>';
            echo '<li>Expired: ' . $stats['expired'] . '</li>';
            echo '<li>Cancelled: ' . $stats['cancelled'] . '</li>';
            echo '</ul>';
            
        } catch (Exception $e) {
            echo '<p style="color: red;">Error loading subscription data: ' . esc_html($e->getMessage()) . '</p>';
        }
    }
    
    echo '</div>';
    echo '</div>';
}

/**
 * Reset database for development
 */
function wp_manga_subscription_reset_database() {
    global $wpdb;
    
    // Drop tables
    $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}manga_subscription_plans");
    $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}manga_user_subscriptions");
    
    // Reset database version
    delete_option('wp_manga_chapter_coin_db_ver');
    
    // Recreate tables
    if (function_exists('wmcc_setup_db')) {
        wmcc_setup_db();
    }
    
    wp_manga_subscription_debug_log('Database reset completed');
}

// Log when development mode is active
if (wp_manga_subscription_is_dev_mode()) {
    wp_manga_subscription_debug_log('Development mode is active');
}
