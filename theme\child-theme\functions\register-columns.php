<?php

function register_columns() {
    global $wpdb;

    // Table name with the correct prefix
    $table_name = $wpdb->prefix . 'manga_chapters';

    // Check and add 'views' column
    $views_column_exists = $wpdb->get_var("SHOW COLUMNS FROM `$table_name` LIKE 'views'");
    if (is_null($views_column_exists)) {
        $wpdb->query("ALTER TABLE `$table_name` ADD `views` BIGINT(20) NULL DEFAULT 0");
    }

    // Check and add 'user_id' column
    $user_id_column_exists = $wpdb->get_var("SHOW COLUMNS FROM `$table_name` LIKE 'user_id'");
    if (is_null($user_id_column_exists)) {
        $wpdb->query("ALTER TABLE `$table_name` ADD `user_id` BIGINT(20) UNSIGNED NOT NULL");
    }
}

// Hook into the 'init' action to ensure the function runs at the right time
add_action('init', 'register_columns');
