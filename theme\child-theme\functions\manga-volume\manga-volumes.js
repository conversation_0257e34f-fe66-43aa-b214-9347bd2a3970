jQuery(document).ready(function($) {
    let volumeIndex = $('#wp-manga-volumes-grid .wp-manga-volume-card').length;

    // Handle adding a new volume
    $('#wp_manga_add_volume_button').click(function() {
        const volumeCardHtml = `
            <div class="wp-manga-volume-card">
                <img src="" alt="" class="wp-manga-volume-image" />
                <input type="hidden" name="wp_manga_volumes[${volumeIndex}][image]" value="" />
                <input type="text" name="wp_manga_volumes[${volumeIndex}][number]" placeholder="Volume Number" />
                <input type="text" name="wp_manga_volumes[${volumeIndex}][chapter]" placeholder="Chapter Number" />
                <button type="button" class="button wp_manga_select_image_button">Select/Upload Image</button>
                <button type="button" class="button wp-manga-remove-volume-button">Delete Volume</button>
            </div>
        `;
        $('#wp-manga-volumes-grid').append(volumeCardHtml);
        volumeIndex++;
    });

    // Media uploader instance
    let mediaUploader;

    // Handle the "Select/Upload Image" button
    $(document).on('click', '.wp_manga_select_image_button', function(e) {
        e.preventDefault();
        const button = $(this);
        const imageField = button.prevAll('input[type="hidden"]');
        const imagePreview = button.prevAll('.wp-manga-volume-image');

        // If media uploader exists, reopen it
        if (mediaUploader) {
            mediaUploader.open();
            return;
        }

        // Create a new media uploader instance
        mediaUploader = wp.media({
            title: 'Select or Upload Volume Image',
            button: {
                text: 'Use this image'
            },
            multiple: false
        });

        // When an image is selected, run a callback
        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            imageField.val(attachment.url);
            imagePreview.attr('src', attachment.url);
        });

        // Open the uploader dialog
        mediaUploader.open();
    });

    // Handle removing volume cards
    $(document).on('click', '.wp-manga-remove-volume-button', function() {
        $(this).closest('.wp-manga-volume-card').remove();
    });

    // Handle deleting all volumes
    $('#wp_manga_delete_all_volumes_button').click(function() {
        $('#wp-manga-volumes-grid').empty();
        volumeIndex = 0;
    });

    // Handle adding multiple volumes (select multiple images)
    $('#wp_manga_select_multiple_images_button').click(function(e) {
        e.preventDefault();

        // If media uploader exists, reopen it
        if (mediaUploader) {
            mediaUploader.open();
            return;
        }

        // Create a new media uploader instance that allows multiple selection
        mediaUploader = wp.media({
            title: 'Select or Upload Volume Images',
            button: {
                text: 'Use these images'
            },
            multiple: true
        });

        // When images are selected, run a callback
        mediaUploader.on('select', function() {
            const attachments = mediaUploader.state().get('selection').toJSON();

            // Add each selected image as a new volume
            attachments.forEach(function(attachment) {
                const volumeCardHtml = `
                    <div class="wp-manga-volume-card">
                        <img src="${attachment.url}" alt="" class="wp-manga-volume-image" />
                        <input type="hidden" name="wp_manga_volumes[${volumeIndex}][image]" value="${attachment.url}" />
                        <input type="text" name="wp_manga_volumes[${volumeIndex}][number]" placeholder="Volume Number" />
                        <input type="text" name="wp_manga_volumes[${volumeIndex}][chapter]" placeholder="Chapter Number" />
                        <button type="button" class="button wp_manga_select_image_button">Select/Upload Image</button>
                        <button type="button" class="button wp-manga-remove-volume-button">Delete Volume</button>
                    </div>
                `;
                $('#wp-manga-volumes-grid').append(volumeCardHtml);
                volumeIndex++;
            });
        });

        // Open the uploader dialog
        mediaUploader.open();
    });
});
