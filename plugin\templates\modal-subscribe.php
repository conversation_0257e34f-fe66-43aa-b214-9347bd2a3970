<div class="modal fade" id="frm-wp-manga-subscribe" tabindex="-1" role="dialog">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
			</div>
			<div class="modal-body">
				<input type="hidden" name="wp-manga-subscription-nonce" value="<?php echo wp_create_nonce('wp-manga-subscription-nonce');?>"/>
				<input type="hidden" name="wp-manga-chapter" value=""/>
				<h3>
					<?php echo esc_html__( 'Premium Chapter - Subscription Required', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
				</h3>
				<?php
				if(get_current_user_id()){
					$plans = array();
					if(class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
						$subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
						$plans = $subscription_manager->get_subscription_plans();
					}
					?>
					<p class="message-subscription"><?php echo wp_kses_post(__('This chapter requires a premium subscription. Choose a plan below:', MANGA_CHAPTER_COIN_TEXT_DOMAIN )); ?></p>
					
					<?php if(!empty($plans)): ?>
					<div class="subscription-plans">
						<?php foreach($plans as $plan): ?>
						<div class="subscription-plan" data-plan-id="<?php echo esc_attr($plan->plan_id); ?>">
							<div class="plan-header">
								<h4><?php echo esc_html($plan->plan_name); ?></h4>
								<div class="plan-price">$<?php echo esc_html(number_format($plan->price, 2)); ?></div>
							</div>
							<div class="plan-details">
								<p><?php echo sprintf(esc_html__('%d months of unlimited access', MANGA_CHAPTER_COIN_TEXT_DOMAIN), $plan->duration_months); ?></p>
								<ul>
									<li><?php echo esc_html__('Unlimited access to all premium chapters', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></li>
									<li><?php echo esc_html__('No additional fees', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></li>
									<li><?php echo esc_html__('Cancel anytime', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></li>
								</ul>
							</div>
							<button class="button button-primary button-large btn-subscribe" data-plan-id="<?php echo esc_attr($plan->plan_id); ?>">
								<?php echo esc_html__( 'Subscribe Now', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
								<i class="fas fa-spinner fa-spin" style="display:none;"></i>
							</button>
						</div>
						<?php endforeach; ?>
					</div>
					<?php else: ?>
					<p class="message-no-plans"><?php echo esc_html__('No subscription plans available at the moment.', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></p>
					<?php endif; ?>
					
				<?php } else {?>
				<p class="message-login"><?php echo wp_kses(__('You are required to login first', MANGA_CHAPTER_COIN_TEXT_DOMAIN ), array('a'=>array('href'=>1, 'class'=>1))); ?></p>
				<?php }?>
			</div>
			<div class="modal-footer">
				<button class="button button-secondary button-large btn-cancel"><?php echo esc_html__( 'Cancel', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?></button>
			</div>
		</div>
	</div>
</div>
