<?php

if (get_option('allow_post', 1)) {
    if (isset($_GET['upload-chapter'])) {
    
    

    // Check if the user is logged in
    if (!is_user_logged_in()) {
        include(get_stylesheet_directory() . '/401.php'); // Render the 401 page
        exit;
    }

    get_header();

    // Get the current post title (Manga title)
    $manga_title = get_the_title();
    $post_id = get_the_ID();
    // Restrict access: only administrators, editors, or the manga author
    $current_user = wp_get_current_user();
    $author_id = get_post_field('post_author', $post_id);
    if (!in_array('administrator', $current_user->roles) && !in_array('editor', $current_user->roles) && $current_user->ID !== (int)$author_id) {
        include(get_stylesheet_directory() . '/401.php'); // Unauthorized
        exit;
    }

?>

<main>
    <div class="container mt-5 d-flex flex-column align-items-center max-md">
        <h1 class="text-white text-center"><?php echo esc_html($manga_title); ?></h1>

        <form id="uploadChapterForm" class="w-full mt-4" method="POST" enctype="multipart/form-data">

            <!-- Hidden input for post ID -->
            <input type="hidden" name="post_id" value="<?php echo esc_attr($post_id); ?>">

            <!-- Rules -->
            <div class="text-sm">
                <div class="text-danger font-bold">Lire avant de téléverser un chapitre:</div>
                <ul class="list-disc mt-1">
                    <li>Les images du chapitre ne doivent pas dépasser 12 000 pixels</li>
                    <li>Si le chapitre existe déjà, il sera soumis à révision pour choisir la meilleure traduction</li>
                    <!-- <li>If the old chapters are on MangaLek, just share the manga link <a style="color: #3c8bc6;" data-toggle="modal" data-target="#request" href="#">here</a>, and they'll be uploaded automatically.</li> -->
                    <li>Si vous rencontrez des problèmes, veuillez nous en informer sur le serveur <a style="color: #3c8bc6;" href="https://discord.gg/DkFSSEncp7">Discord</a>.</li>
                </ul>
            </div>

            <!-- Chapter Number -->
            <div class="form-group light w-full">
                <div class="control-name">Numéro du chapitre</div>
                <div><input type="text" class="form-control" name="chapter-number" placeholder="Sans 0 au début" /></div>
            </div>

            <!-- Chapter Title -->
            <div class="form-group light w-full">
                <div class="control-name">Titre du chapitre</div>
                <div><input type="text" class="form-control" name="chapter-title" placeholder="Optionnel" /></div>
            </div>

            <!-- Chapter File -->
            <div class="form-group light w-full">
                <div class="control-name">Fichier du chapitre</div>
                <div>
                    <div class="custom-file">
                        <input type="file" class="custom-file-input" id="chapter-file" name="chapter-file" accept=".zip" />
                        <label class="custom-file-label" for="chapter-file">Choisir un fichier .zip</label>
                    </div>
                    <div class="form-text text-muted">
                        Assurez-vous que le fichier zip contient uniquement des images sans autres fichiers
                    </div>
                </div>
            </div>

            <div class="form-group text-center mt-2 w-full">
                <button type="submit" class="submit-manga btn w-100 mt-1 btn-lg btn-primary">téléverser</button>
            </div>

            <div class="loading" style="display: none;"></div>
            <div id="responseMessage" class="alert mt-3" style="display: none;"></div>

            <!-- Progress Bar -->
            <div class="progress mt-3" style="display: none;">
                <div id="uploadProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
            </div>

        </form>
    </div>
</main>


<?php get_footer(); exit; }} ?>