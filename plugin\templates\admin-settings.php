<?php

 if( !defined( 'ABSPATH' ) ){
        exit;
    }

    if( !isset( $GLOBALS['wp_manga'] ) ){
        return;
    }

    $wp_manga_chapter_coin_settings = wp_manga_chapter_coin_get_settings();
	
    extract( $wp_manga_chapter_coin_settings );
	
	$default_coin = isset($default_coin) ? $default_coin : 0;
	$free_word = isset($free_word) ? $free_word : esc_html__('Free', MANGA_CHAPTER_COIN_TEXT_DOMAIN);
	$free_color = isset($free_color) ? $free_color : '#999999';
	$free_background = isset($free_background) ? $free_background : '#DCDCDC';
	$unlock_color = isset($unlock_color) ? $unlock_color : '#999999';
	$unlock_background = isset($unlock_background) ? $unlock_background : '#DCDCDC';
	$lock_color = isset($lock_color) ? $lock_color : '#ffffff';
	$lock_background = isset($lock_background) ? $lock_background : '#fe6a10';
	$ranking_background = isset($ranking_background) ? $ranking_background : 'rgba(255, 248, 26, 0.6)';
	$ranking_text_color = isset($ranking_text_color) ? $ranking_text_color : '#333333';
	$muupro_allow_creator_edit = isset($muupro_allow_creator_edit) ? $muupro_allow_creator_edit : 'no';
    $exclude_premium_chapters_in_feed = isset($exclude_premium_chapters_in_feed) ? $exclude_premium_chapters_in_feed : 'no';    
?>
<div class="section">
<h2 class="title"><?php esc_html_e( 'WP Manga Chapter Subscription - Settings', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?></h2>

<?php
// Get subscription plans and statistics
$plans = array();
$stats = array('active' => 0, 'expired' => 0, 'cancelled' => 0);

if(class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
    $subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
    $plans = $subscription_manager->get_subscription_plans();
    $stats = $subscription_manager->get_subscription_stats();
}
?>

<div class="subscription-stats">
	<h3><?php esc_html_e( 'Subscription Statistics', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?></h3>
	<div class="stats-grid">
		<div class="stat-item">
			<span class="stat-number"><?php echo esc_html($stats['active']); ?></span>
			<span class="stat-label"><?php esc_html_e('Active Subscriptions', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></span>
		</div>
		<div class="stat-item">
			<span class="stat-number"><?php echo esc_html($stats['expired']); ?></span>
			<span class="stat-label"><?php esc_html_e('Expired Subscriptions', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></span>
		</div>
		<div class="stat-item">
			<span class="stat-number"><?php echo esc_html($stats['cancelled']); ?></span>
			<span class="stat-label"><?php esc_html_e('Cancelled Subscriptions', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></span>
		</div>
	</div>
</div>

<div class="subscription-plans-management">
	<h3><?php esc_html_e( 'Current Subscription Plans', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?></h3>
	<?php if(!empty($plans)): ?>
	<table class="wp-list-table widefat fixed striped">
		<thead>
			<tr>
				<th><?php esc_html_e('Plan Name', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></th>
				<th><?php esc_html_e('Duration', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></th>
				<th><?php esc_html_e('Price', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></th>
				<th><?php esc_html_e('Status', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php foreach($plans as $plan): ?>
			<tr>
				<td><?php echo esc_html($plan->plan_name); ?></td>
				<td><?php echo sprintf(esc_html__('%d months', MANGA_CHAPTER_COIN_TEXT_DOMAIN), $plan->duration_months); ?></td>
				<td>$<?php echo esc_html(number_format($plan->price, 2)); ?></td>
				<td><span class="status-<?php echo esc_attr($plan->status); ?>"><?php echo esc_html(ucfirst($plan->status)); ?></span></td>
			</tr>
			<?php endforeach; ?>
		</tbody>
	</table>
	<?php else: ?>
	<p><?php esc_html_e('No subscription plans found.', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></p>
	<?php endif; ?>
</div>

<table class="form-table">
	<tr>
        <th scope="row">
            <?php esc_html_e( '"Free" Badge - Label', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
        </th>
        <td>
            <p>
                <input type="text" name="wp_manga_chapter_selling[free_word]" value="<?php echo $free_word;?>"/>
            </p>
			<p><?php esc_html_e('Text for free chapter\'s badge', MANGA_CHAPTER_COIN_TEXT_DOMAIN);?></p>
        </td>
    </tr>
	<tr>
        <th scope="row">
            <?php esc_html_e( '"Free" Badge - Text Color', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
        </th>
        <td>
            <p>
                <input type="text" style="background-color:<?php echo $free_color;?>" name="wp_manga_chapter_selling[free_color]" value="<?php echo $free_color;?>"/>
            </p>
			<p><?php esc_html_e('Text color of "Free" badge. Use hexa value including "#" character or rgba() value', MANGA_CHAPTER_COIN_TEXT_DOMAIN);?></p>
        </td>
    </tr>
	<tr>
        <th scope="row">
            <?php esc_html_e( '"Free" Badge - Background Color', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
        </th>
        <td>
            <p>
                <input type="text" style="background-color:<?php echo $free_background;?>" name="wp_manga_chapter_selling[free_background]" value="<?php echo $free_background;?>"/>
            </p>
			<p><?php esc_html_e('Background color of "Free" badge. Use hexa value including "#" character or rgba() value', MANGA_CHAPTER_COIN_TEXT_DOMAIN);?></p>
        </td>
    </tr>
	<tr>
        <th scope="row">
            <?php esc_html_e( 'Subscription Required Badge - Text Color', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
        </th>
        <td>
            <p>
                <input type="text" style="background-color:<?php echo $lock_color;?>" name="wp_manga_chapter_selling[lock_color]" value="<?php echo $lock_color;?>"/>
            </p>
			<p><?php esc_html_e('Text color of subscription required chapter\'s badge. Use hexa value including "#" character or rgba() value', MANGA_CHAPTER_COIN_TEXT_DOMAIN);?></p>
        </td>
    </tr>
	<tr>
        <th scope="row">
            <?php esc_html_e( 'Subscription Required Badge - Background Color', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
        </th>
        <td>
            <p>
                <input type="text" style="background-color:<?php echo $lock_background;?>" name="wp_manga_chapter_selling[lock_background]" value="<?php echo $lock_background;?>"/>
            </p>
			<p><?php esc_html_e('Background color of subscription required chapter\'s badge. Use hexa value including "#" character or rgba() value', MANGA_CHAPTER_COIN_TEXT_DOMAIN);?></p>
        </td>
    </tr>
	<tr>
        <th scope="row">
            <?php esc_html_e( 'Subscriber Access Badge - Text Color', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
        </th>
        <td>
            <p>
                <input type="text" style="background-color:<?php echo $unlock_color;?>" name="wp_manga_chapter_selling[unlock_color]" value="<?php echo $unlock_color;?>"/>
            </p>
			<p><?php esc_html_e('Text color of accessible premium chapter\'s badge for subscribers. Use hexa value including "#" character or rgba() value', MANGA_CHAPTER_COIN_TEXT_DOMAIN);?></p>
        </td>
    </tr>
	<tr>
        <th scope="row">
            <?php esc_html_e( 'Ranking badge - Background Color', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
        </th>
        <td>
            <p>
                <input type="text" style="background-color:<?php echo $ranking_background;?>" name="wp_manga_chapter_selling[ranking_background]" value="<?php echo $ranking_background;?>"/>
            </p>
			<p><?php esc_html_e('Background color of the Ranking Badge (of Top Bought Listing shortcodes). Use hexa value including "#" character or rgba() value', MANGA_CHAPTER_COIN_TEXT_DOMAIN);?></p>
        </td>
    </tr>
	<tr>
        <th scope="row">
            <?php esc_html_e( 'Ranking badge - Text Color', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
        </th>
        <td>
            <p>
                <input type="text" style="background-color:<?php echo $ranking_text_color;?>" name="wp_manga_chapter_selling[ranking_text_color]" value="<?php echo $ranking_text_color;?>"/>
            </p>
			<p><?php esc_html_e('Text color of the Ranking Badge (of Top Bought Listing shortcodes). Use hexa value including "#" character or rgba() value', MANGA_CHAPTER_COIN_TEXT_DOMAIN);?></p>
        </td>
    </tr>
	<tr>
        <th scope="row">
            <?php esc_html_e( 'Unlocked Badge - Background Color', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
        </th>
        <td>
            <p>
                <input type="text" style="background-color:<?php echo $unlock_background;?>" name="wp_manga_chapter_selling[unlock_background]" value="<?php echo $unlock_background;?>"/>
            </p>
			<p><?php esc_html_e('Background color of bought/unlocked premium chapter\'s badge. Use hexa value including "#" character or rgba() value', MANGA_CHAPTER_COIN_TEXT_DOMAIN);?></p>
        </td>
    </tr>
	<tr>
        <th scope="row">
            <?php esc_html_e( '[For WP Manga Member Upload PRO] Allow Manga Owner to set coin', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
        </th>
        <td>
            <p>
                <select name="wp_manga_chapter_selling[muupro_allow_creator_edit]" value="<?php echo esc_attr(  $muupro_allow_creator_edit ); ?>">
                    <option value="yes" <?php selected( $muupro_allow_creator_edit, 'yes' ); ?>><?php esc_html_e('Yes', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></option>
                    <option value="no" <?php selected( $muupro_allow_creator_edit, 'no' ); ?>><?php esc_html_e('No', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></option>
                </select>
            </p>
			<p><?php esc_html_e('Allow Manga Owner to set coin value for all chapters of his manga in Front-end Edit page. Admin and Editor can edit coin as well.', MANGA_CHAPTER_COIN_TEXT_DOMAIN);?></p>
        </td>
    </tr>
    <tr>
        <th scope="row">
            <?php esc_html_e( 'Exclude Premium Chapters in RSS Feed', MANGA_CHAPTER_COIN_TEXT_DOMAIN ); ?>
        </th>
        <td>
            <p>
                <select name="wp_manga_chapter_selling[exclude_premium_chapters_in_feed]" value="<?php echo esc_attr(  $exclude_premium_chapters_in_feed ); ?>">
                    <option value="yes" <?php selected( $exclude_premium_chapters_in_feed, 'yes' ); ?>><?php esc_html_e('Yes', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></option>
                    <option value="no" <?php selected( $exclude_premium_chapters_in_feed, 'no' ); ?>><?php esc_html_e('No', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></option>
                </select>
            </p>
			<p><?php esc_html_e('Exclude Premium Chapters in RSS Feed so it will not appear in the feed. When it becomes Free, it may appear again if the updated date is still in the Feed range', MANGA_CHAPTER_COIN_TEXT_DOMAIN);?></p>
        </td>
    </tr>
</table>
<button type="submit" class="button button-primary"><?php esc_attr_e( 'Save All Changes', WP_MANGA_TEXTDOMAIN ) ?></button>
</div>