<?php

function import_chapters_logic() {
    global $wpdb;

    // Get the list of all 'wp-manga' custom post types (manga posts)
    $mangas = get_posts([
        'post_type' => 'wp-manga',
        'posts_per_page' => -1
    ]);

    // Define the chapter directory path
    $chapter_test_dir = get_stylesheet_directory() . '/functions/rm-settings/chapter-test';

    if (!is_dir($chapter_test_dir)) {
        return 'Chapter test directory not found.';
    }

    // Loop through each manga and import chapters
    foreach ($mangas as $manga) {
        $post_id = $manga->ID;

        // Check if the post has '_wp_manga_chapter_type' meta key set to 'text'
        $chapter_type = get_post_meta($post_id, '_wp_manga_chapter_type', true);
        if ($chapter_type === 'text') {
            continue; // Skip importing chapters for posts with 'text' chapter type
        }

        // Get or create the manga unique ID
        $manga_unique_id = get_post_meta($post_id, 'manga_unique_id', true);
        if (!$manga_unique_id) {
            $manga_unique_id = wp_generate_uuid4();
            update_post_meta($post_id, 'manga_unique_id', $manga_unique_id);
        }

        // Create 20 chapters for each manga
        for ($i = 1; $i <= 20; $i++) {
            $chapter_number = $i;
            $chapter_title = 'عنوان';
            $chapter_uid = wp_generate_uuid4();

            // Check if chapter already exists for this manga
            $existing_chapter = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}manga_chapters WHERE post_id = %d AND chapter_name = %s",
                $post_id, $chapter_number
            ));

            if ($existing_chapter > 0) {
                continue; // Skip if chapter already exists
            }

            // Define chapter storage location
            $upload_dir = wp_upload_dir();
            $chapter_dir = $upload_dir['basedir'] . "/WP-manga/data/$manga_unique_id/$chapter_uid";
            wp_mkdir_p($chapter_dir);

            // Copy the chapter files from the test folder to the new chapter folder
            $files = scandir($chapter_test_dir);
            foreach ($files as $file) {
                if (in_array(pathinfo($file, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    copy("$chapter_test_dir/$file", "$chapter_dir/$file");
                }
            }

            // Prepare the chapter data
            $chapter_data = [];
            $counter = 1;
            foreach ($files as $file) {
                if (in_array(pathinfo($file, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    $chapter_data[$counter] = [
                        'src' => "$manga_unique_id/$chapter_uid/$file",
                        'mime' => mime_content_type("$chapter_dir/$file")
                    ];
                    $counter++;
                }
            }

            // Insert chapter data into {$wpdb->prefix}manga_chapters
            $wpdb->insert('{$wpdb->prefix}manga_chapters', [
                'post_id' => $post_id,
                'chapter_name' => $chapter_number,
                'chapter_name_extend' => $chapter_title,
                'chapter_slug' => $chapter_number,
                'storage_in_use' => 'local',
                'date' => current_time('mysql'),
                'date_gmt' => current_time('mysql', 1),
                'volume_id' => 0,
                'chapter_metas' => maybe_serialize(['uid' => $chapter_uid]),
                'user_id' => get_current_user_id(),
                'chapter_status' => 0
            ]);

            // Get inserted chapter ID
            $chapter_id = $wpdb->insert_id;

            // Insert chapter data into {$wpdb->prefix}manga_chapters_data
            $wpdb->insert('{$wpdb->prefix}manga_chapters_data', [
                'chapter_id' => $chapter_id,
                'storage' => 'local',
                'data' => json_encode($chapter_data)
            ]);
        }
    }

    return '20 chapters imported for each manga successfully, except for text-type chapters.';
}
