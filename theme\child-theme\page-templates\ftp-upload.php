<?php
/*
Template Name: FTP Test
*/

if (!current_user_can('administrator')) {
    include(get_stylesheet_directory() . '/401.php');
    exit;
}

// Handle AJAX request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['ftp-file'])) {
    $file = $_FILES['ftp-file'];
    $new_file_name = isset($_POST['newFileName']) && !empty($_POST['newFileName']) ? trim($_POST['newFileName']) : '';
    $progress_file_path = get_stylesheet_directory() . '/ftp_progress.txt'; // Path for progress file

    header('Content-Type: application/json');

    // Create progress file with initial value
    file_put_contents($progress_file_path, "Progress: 0%");

    // Check if the file is uploaded successfully
    if ($file['error'] === UPLOAD_ERR_OK) {
        $ftp_host = '*************';
        $ftp_user = '<EMAIL>';
        $ftp_pass = 'HaneKawa@2003';
        $ftp_port = 21;

        // Connect to the FTP server
        $ftp_conn = ftp_connect($ftp_host, $ftp_port);
        if (!$ftp_conn) {
            echo json_encode(['status' => 'error', 'message' => 'Could not connect to FTP server: ' . error_get_last()['message']]);
            exit;
        }

        // Login to the FTP server
        $login_result = ftp_login($ftp_conn, $ftp_user, $ftp_pass);
        if (!$login_result) {
            echo json_encode(['status' => 'error', 'message' => 'FTP login failed. Please check your username and password.']);
            ftp_close($ftp_conn);
            exit;
        }

        // Set passive mode if required
        ftp_pasv($ftp_conn, true); // Enable passive mode if the server requires it

        // Define the upload path on the server
        $upload_dir = '/uploads/'; // The directory where you want to upload the file

        // Get the file extension
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);

        // Determine the new file name (with extension)
        if (!empty($new_file_name)) {
            $upload_file = $upload_dir . $new_file_name . '.' . $file_extension; // Use the new file name
        } else {
            $upload_file = $upload_dir . basename($file['name']); // Use the original file name
        }

        // Check if the directory exists
        if (!@ftp_chdir($ftp_conn, $upload_dir)) {
            echo json_encode(['status' => 'error', 'message' => 'FTP directory does not exist or cannot be accessed. Please check the path: ' . $upload_dir]);
            ftp_close($ftp_conn);
            exit;
        }

        // Upload in chunks
        $fs = filesize($file['tmp_name']);
        define('FTP_CHUNK_SIZE', intval($fs * 0.02)); // upload ~10% per iteration

        $localfile = fopen($file['tmp_name'], 'rb');
        $i = 0;

        while ($i < $fs) {
            $tmpfile = fopen('tmp_ftp_upload.bin', 'ab');
            fwrite($tmpfile, fread($localfile, FTP_CHUNK_SIZE));
            fclose($tmpfile);

            // Upload the temporary file, starting from the offset $i
            ftp_put($ftp_conn, $upload_file, 'tmp_ftp_upload.bin', FTP_BINARY, $i);

            // Update progress
            $progress = (100 * round(($i += FTP_CHUNK_SIZE) / $fs, 2));
            file_put_contents($progress_file_path, "Progress: {$progress}%");
        }

        fclose($localfile);
        unlink('tmp_ftp_upload.bin'); // Delete temporary file

        // Send success response after all uploads are done
        echo json_encode(['status' => 'success', 'message' => 'File uploaded successfully!']);

        // Close the FTP connection
        ftp_close($ftp_conn);
        
        // Delete the progress file here, after the entire process
        unlink($progress_file_path);
    } else {
        // Handle different file upload errors
        $error_messages = [
            UPLOAD_ERR_INI_SIZE   => 'The uploaded file exceeds the upload_max_filesize directive in php.ini.',
            UPLOAD_ERR_FORM_SIZE  => 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.',
            UPLOAD_ERR_PARTIAL    => 'The uploaded file was only partially uploaded.',
            UPLOAD_ERR_NO_FILE    => 'No file was uploaded.',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder on the server.',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk.',
            UPLOAD_ERR_EXTENSION  => 'A PHP extension stopped the file upload.',
        ];

        $upload_error_message = isset($error_messages[$file['error']]) ? $error_messages[$file['error']] : 'Unknown error occurred during file upload.';

        echo json_encode(['status' => 'error', 'message' => $upload_error_message]);
    }
    exit;
}

get_header();
?>

<main class="">
    <div class="container mt-5 d-flex flex-column align-items-center">
        <h1 class="text-white">FTP Test</h1>
        <span>This is just a page to test what you want to do with the FTP account</span>
        <div class="default-style p-4 mt-5 max-sm w-100">
            <form id="ftpForm">
                <div class="form-group">
                    <input type="text" class="form-control" name="newFileName" placeholder="Rename file (optional)" />
                </div>
                <div class="form-group">
                    <div class="custom-file">
                        <input type="file" class="custom-file-input" id="ftp-file" name="ftp-file" accept=".zip, .jpg, .png, .txt, .pdf" />
                        <label class="custom-file-label" for="ftp-file">Choose a file</label>
                    </div>
                </div>
                <button type="submit" class="btn w-100 btn-lg btn-primary">Submit <i class="fa-solid fa-paper-plane-top"></i></button>

                <div class="progress mt-3" style="display: none;">
                    <div id="ftpUploadProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                        <span class="progress-text" style="color: white; font-weight: bold;">0%</span>
                    </div>
                </div>

                <div id="responseMessage" class="alert mt-3" style="display: none;"></div>
            </form>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function () {
    const ftpForm = document.getElementById('ftpForm');
    const progressBar = document.getElementById('ftpUploadProgress');
    const progressText = progressBar.querySelector('.progress-text'); // Select the progress text span
    const progressWrapper = document.querySelector('.progress');
    const responseMessage = document.getElementById('responseMessage');

    ftpForm.addEventListener('submit', function (e) {
        e.preventDefault();

        const formData = new FormData(ftpForm);
        const xhr = new XMLHttpRequest();
        xhr.open('POST', window.location.href, true);

        // Show progress bar
        progressWrapper.style.display = 'flex';

        // Handle the AJAX response
        xhr.onload = function () {
            const response = JSON.parse(xhr.responseText);
            if (response.status === 'success') {
                responseMessage.classList.remove('alert-danger');
                responseMessage.classList.add('alert-success');
                responseMessage.textContent = response.message;
            } else {
                responseMessage.classList.add('alert-danger');
                responseMessage.textContent = response.message;
            }

            // Hide the progress bar after completion
            setTimeout(() => {
                progressWrapper.style.display = 'none';
                progressBar.style.width = '0%';  // Reset progress bar width
                progressText.textContent = '0%';  // Reset progress text
                responseMessage.style.display = 'block';
            }, 2000);

            // Clear the interval to stop checking progress
            clearInterval(checkProgress);
        };

        // Handle errors
        xhr.onerror = function () {
            progressWrapper.style.display = 'none';
            responseMessage.classList.add('alert-danger');
            responseMessage.textContent = 'An error occurred during the upload.';
            // Clear the interval to stop checking progress
            clearInterval(checkProgress);
        };

        // Send the form data
        xhr.send(formData);

        // Polling function to check progress
        const progressFileUrl = '<?php echo get_stylesheet_directory_uri(); ?>/ftp_progress.txt'; // Ensure correct path
        const checkProgress = setInterval(() => {
            fetch(progressFileUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.text();
                })
                .then(data => {
                    const match = data.match(/Progress:\s*(\d+)%/);
                    if (match) {
                        const progress = parseInt(match[1], 10);
                        // Update progress bar width
                        progressBar.style.width = progress + '%'; // Update width based on progress
                        progressBar.setAttribute('aria-valuenow', progress);
                        progressText.textContent = progress + '%'; // Update progress text
                    }
                })
                .catch(err => {
                    console.error('Error fetching progress:', err);
                    clearInterval(checkProgress); // Stop checking if there's an error
                });
        }, 1000); // Check progress every second
    });
});
</script>

<?php get_footer(); ?>