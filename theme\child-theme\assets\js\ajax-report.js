jQuery(document).ready(function($) {
    $('.submit').on('click', function(e) {
        e.preventDefault();

        var message = $('textarea[name="message"]').val();
        var mangaName = $('input[name="manga_name"]').val();
        var chapterNumber = $('input[name="chapter_number"]').val();
        var reportType = $('input[name="report_type"]').val();

        $.ajax({
            type: 'POST',
            url: ajaxReport.ajax_url,
            data: {
                action: 'submit_report',
                message: message,
                manga_name: mangaName,
                chapter_number: chapterNumber,
                report_type: reportType
            },
            success: function(response) {
                if (response.success) {
                    $('#report-message').text('Report submitted successfully.'); 
                    $('textarea[name="message"]').val(''); 
                    $('#report').modal('hide');
                }
            }
        });
    });
});

