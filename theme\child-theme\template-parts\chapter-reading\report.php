<div class="modal fade" id="report">
    <div class="modal-dialog limit-w modal-dialog-centered">
        <div class="modal-content p-4">

            <div class="modal-close" data-dismiss="modal">
                <i class="fa-solid fa-xmark"></i>
            </div>

            <h5 class="text-white mb-0">Signaler un chapitre</h5>

            <div class="mt-2">
                <div><b><?php the_title(); ?></b></div>
                <div class="text-primary current-type-number text-title">Chapitre <?php echo esc_html($reading_chapter['chapter_name']); ?></div>
            </div>

            <form class="mt-3" method="post" id="report-form">
                <input type="hidden" name="report_type" value="<?php echo is_user_logged_in() ? 'member' : 'guest'; ?>">
                <input type="hidden" name="manga_name" value="<?php echo esc_html(get_the_title()); ?>">
                <input type="hidden" name="chapter_number" value="<?php echo esc_html($reading_chapter['chapter_name']); ?>">

                <div class="form-group mt-4 mb-2">
                    <textarea class="form-control" name="message" rows="3" 
                        maxlength="60" 
                        pattern="[A-Za-z0-9 ]+"
                        onkeyup="this.value=this.value.replace(/[^A-Za-z0-9 ]/g,''); updateCharCount(this);"
                        placeholder="Quel est le problème avec le chapitre ?" 
                        required></textarea>
                    <small class="text-muted mt-1" id="charCount">Caractères restants: 60</small>
                </div>
                <button id="report-submit" type="submit" class="submit btn mt-3 btn-lg btn-primary w-100">Envoyer <i class="fa-solid fa-chevron-right fa-xs"></i></button>
            </form>
            <div class="loading" style="display: none;"></div>
            <div id="report-message" class="mt-2"></div>
        </div>
    </div>
</div>

<script>
function updateCharCount(textarea) {
    const maxLength = 60;
    const currentLength = textarea.value.length;
    const remaining = maxLength - currentLength;
    document.getElementById('charCount').textContent = `Caractères restants: ${remaining}`;
}
</script>
