<?php

function export_manga_callback() {
    // Handle export logic if form submitted
    if (isset($_POST['export_manga'])) {
        $message = export_manga_logic();
        $notice_type = $message['success'] ? 'success' : 'error';
        echo '<div class="notice notice-' . $notice_type . ' is-dismissible"><p>' . $message['message'] . '</p></div>';
    }
    
    ?>
    <div class="wrap">
        <h1>Manga Export</h1>
        <p>Export all manga data from the site to manga-info.txt file.</p>
        
        <form method="post">
            <?php wp_nonce_field('export_manga_action', 'export_manga_nonce'); ?>
            <input type="hidden" name="export_manga" value="1">
            <p>
                <input type="submit" class="button-primary" value="Export All Manga">
            </p>
        </form>
    </div>
    <?php
}

function export_manga_logic() {
    // Use WP_Query to fetch all published 'wp-manga' posts
    $args = [
        'post_type'      => 'wp-manga',
        'post_status'    => 'publish',
        'posts_per_page' => -1,
    ];
    $query = new WP_Query($args);
    
    if (!$query->have_posts()) {
        return [
            'success' => false,
            'message' => 'No manga found to export.'
        ];
    }
    
    $export_data = '';
    while ($query->have_posts()) {
        $query->the_post();
        $post_id      = get_the_ID();
        $manga_name   = get_the_title();
        $thumbnail    = get_the_post_thumbnail_url($post_id, 'full');
        $story        = wp_strip_all_tags(get_the_content());
        $status       = get_post_meta($post_id, '_wp_manga_status', true);
        $genres       = wp_get_post_terms($post_id, 'wp-manga-genre', ['fields' => 'names']);
        $authors      = wp_get_post_terms($post_id, 'wp-manga-author', ['fields' => 'names']);
        $release_date = get_post_meta($post_id, 'release_date', true);
        $manga_type   = get_post_meta($post_id, '_wp_manga_type', true);
        $alternative  = get_post_meta($post_id, '_wp_manga_alternative', true);
        
        $export_data .= "Manga name: {$manga_name}\n";
        $export_data .= "thumbnail: " . ($thumbnail ? basename($thumbnail) : '') . "\n";
        $export_data .= "story: {$story}\n";
        $export_data .= "status: {$status}\n";
        $export_data .= "genres: " . implode(', ', $genres) . "\n";
        $export_data .= "author: " . implode(', ', $authors) . "\n";
        $export_data .= "release-date: {$release_date}\n";
        $export_data .= "type: {$manga_type}\n";
        $export_data .= "alternative name: {$alternative}\n\n";
    }
    wp_reset_postdata();
    
    // Save the data to manga-info.txt with UTF-8 BOM prepended for proper encoding
    $upload_dir = wp_upload_dir();
    $export_file = $upload_dir['basedir'] . '/manga-info.txt';
    $bom = pack('C*', 0xEF, 0xBB, 0xBF);
    if (false === file_put_contents($export_file, $bom . trim($export_data))) {
        return [
            'success' => false,
            'message' => 'Failed to write export file. Check permissions.'
        ];
    }
    return [
        'success' => true,
        'message' => 'Successfully exported manga data to ' . $export_file
    ];
}