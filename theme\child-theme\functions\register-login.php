<?php

/*--------------------------------------------------------------
>>> Login
--------------------------------------------------------------*/

// Handle AJAX Login
function ajax_process_login() {
    check_ajax_referer('load_manga_nonce', 'nonce'); // Security check

    $response = array('success' => false, 'message' => '');

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // Retrieve login details
        $login = sanitize_text_field($_POST['login']); // Expecting the name 'login'
        $password = $_POST['password'];

        // Validate inputs
        if (empty($login)) {
            $response['message'] = 'Username and email are required'; 
            wp_send_json($response);
        }
        if (empty($password)) {
            $response['message'] = 'Password is required';
            wp_send_json($response);
        }

        // Check if the input is an email or username and authenticate the user
        $user = is_email($login) ? get_user_by('email', $login) : get_user_by('login', $login);

        // If no user found, return an error
        if (!$user) {
            $response['message'] = 'Invalid username or password';
            wp_send_json($response);
        }

        // Authenticate user with username
        $user = wp_authenticate($user->user_login, $password);

        if (is_wp_error($user)) {
            $response['message'] = 'Invalid username or password';
            wp_send_json($response);
        } else {
            // Set the user cookie with a long expiration time (1 year)
            wp_set_current_user($user->ID);
            set_logged_in_cookie($user->ID);

            $response['success'] = true;
            wp_send_json($response);
        }
    }
}
add_action('wp_ajax_process_login', 'ajax_process_login');
add_action('wp_ajax_nopriv_process_login', 'ajax_process_login');

/*--------------------------------------------------------------
>>> Register
----------------------------------------------------------------*/

// Handle AJAX for validating the username and email
function ajax_validate_register_fields() {
    check_ajax_referer('load_manga_nonce', 'nonce'); // Security check

    $response = array('success' => false, 'message' => '');

    // Validate username
    if (!empty($_POST['username'])) {
        $username = sanitize_text_field($_POST['username']);
        if (username_exists($username)) {
            $response['message'] = 'Username is already taken';
            wp_send_json($response);
        }
    } else {
        $response['message'] = 'Username is required';
        wp_send_json($response);
    }

    // Validate email
    if (!empty($_POST['email'])) {
        $email = sanitize_email($_POST['email']);
        if (!is_email($email)) {
            $response['message'] = 'Invalid email address';
            wp_send_json($response);
        }
        if (email_exists($email)) {
            $response['message'] = 'Email is already registered';
            wp_send_json($response);
        }
    } else {
        $response['message'] = 'Email is required';
        wp_send_json($response);
    }

    // If everything is good
    $response['success'] = true;
    wp_send_json($response);
}
add_action('wp_ajax_validate_register_fields', 'ajax_validate_register_fields');
add_action('wp_ajax_nopriv_validate_register_fields', 'ajax_validate_register_fields');

// Handle AJAX for validating the username, email, and password, and processing registration
function ajax_process_register() {
    
    check_ajax_referer('load_manga_nonce', 'nonce'); // Security check

    $response = array('success' => false, 'message' => '');

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $username = sanitize_text_field($_POST['username']);
        $email = $_POST['email'];  // Use the raw input for email before sanitization
        $password = $_POST['password'];

        // Validate Username
        if (empty($username)) {
            $response['message'] = 'Username is required';
            wp_send_json($response);
        } elseif (username_exists($username)) {
            $response['message'] = 'Username is already taken';
            wp_send_json($response);
        }

        // Validate Email
        if (empty($email)) {
            $response['message'] = 'Email is required';
            wp_send_json($response);
        } elseif (!is_email($email)) {
            $response['message'] = 'Please enter a valid email address';
            wp_send_json($response);
        } 

        // Now sanitize the email only after it has passed the validity check
        $sanitized_email = sanitize_email($email);

        if (email_exists($sanitized_email)) {
            $response['message'] = 'Email is already registered';
            wp_send_json($response);
        }

        // Validate Password
        if (empty($password)) {
            $response['message'] = 'Password is required';
            wp_send_json($response);
        } elseif (strlen($password) < 6) {
            $response['message'] = 'Password must be at least 6 characters long';
            wp_send_json($response);
        }

        // If everything is valid, proceed with user registration
        $user_id = wp_create_user($username, $password, $sanitized_email);

        if (is_wp_error($user_id)) {
            $response['message'] = $user_id->get_error_message();
            wp_send_json($response);
        } else {
            // Set the user cookie with a long expiration time (1 year) after registration
            set_logged_in_cookie($user_id);
            wp_set_current_user($user_id); // Optional, but ensures consistency

            $response['success'] = true;
            wp_send_json($response); // Send success response
        }
    }
}

add_action('wp_ajax_process_register', 'ajax_process_register');
add_action('wp_ajax_nopriv_process_register', 'ajax_process_register');

/*--------------------------------------------------------------
>>> Login Cookie Function
--------------------------------------------------------------*/

// Function to set the authentication cookies with an extended expiration time
function set_logged_in_cookie($user_id) {
    $remember = true; // Force "remember me" behavior
    $expiration = time() + YEAR_IN_SECONDS; // Set cookie expiration to 1 year
    $secure = is_ssl(); // Use SSL if the site supports it

    wp_set_auth_cookie($user_id, $remember, $secure);
    wp_set_current_user($user_id); // Optional, but keeps things consistent
}