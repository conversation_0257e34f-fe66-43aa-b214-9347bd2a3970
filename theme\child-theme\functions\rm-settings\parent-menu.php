<?php

// Register settings
function mfire_register_settings() {
    register_setting('mfire-settings-group', 'display_slider');
    register_setting('mfire-settings-group', 'display_most_viewed');
    register_setting('mfire-settings-group', 'display_continue_reading');
    register_setting('mfire-settings-group', 'display_recent_chapters');
    register_setting('mfire-settings-group', 'display_new_releases');
    register_setting('mfire-settings-group', 'slider_settings');
    register_setting('mfire-settings-group', 'footer_settings');

    register_setting('mfire-settings-group', 'allow_registration');
    register_setting('mfire-settings-group', 'allow_bookmark');
    register_setting('mfire-settings-group', 'reading_history');
    register_setting('mfire-settings-group', 'allow_post');
    register_setting('mfire-settings-group', 'allow_duplicate_upload');
    register_setting('mfire-settings-group', 'upload_volume_covers');
    register_setting('mfire-settings-group', 'enable_review');
    register_setting('mfire-settings-group', 'enable_requests');
    register_setting('mfire-settings-group', 'enable_chapter_reports');
    register_setting('mfire-settings-group', 'enable_view_count');
}
add_action('admin_init', 'mfire_register_settings');

// Callback to display settings
function mfire_settings_callback() {
    $display_slider = get_option('display_slider', 1);
    $display_most_viewed = get_option('display_most_viewed', 1);
    $display_continue_reading = get_option('display_continue_reading', 1);
    $display_recent_chapters = get_option('display_recent_chapters', 1);
    $display_new_releases = get_option('display_new_releases', 1);
    $slider_settings = get_option('slider_settings', 10);
    $footer_settings = get_option('footer_settings', 'جميع الحقوق محفوظة');


    $allow_post = get_option('allow_post', 1);
    $enable_requests = get_option('enable_requests', 1);
    $enable_chapter_reports = get_option('enable_chapter_reports', 1);
    $allow_registration = get_option('allow_registration', 1);
    $allow_bookmark = get_option('allow_bookmark', 1);
    $reading_history = get_option('reading_history', 1);
    $allow_duplicate_upload = get_option('allow_duplicate_upload', 1);
    $upload_volume_covers = get_option('upload_volume_covers', 1);
    $enable_review = get_option('enable_review', 1);
    $enable_view_count = get_option('enable_view_count', 1);
?>

<div class="settings-group">
    <h1>General Settings</h1>
    
    <h2 class="nav-tab-wrapper">
        <a class="nav-tab nav-tab-active" href="#homepage-settings"><span class="dashicons dashicons-smiley"></span> Home Settings</a>
        <a class="nav-tab" href="#general-settings"><span class="dashicons dashicons-format-aside"></span> General Settings</a>
    </h2>

    <form method="post" action="options.php">
        <?php settings_fields('mfire-settings-group'); ?>

        <!-- Home Page Settings Tab -->
        <div id="homepage-settings" class="settings-tab" style="display: block;">
            <h2>Home Page Settings</h2>
            <div class="setting-item">
                <label for="display_slider">Display Slider:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="display_slider" name="display_slider" value="1" <?php checked(1, $display_slider, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-item">
                <label for="display_most_viewed">Display Most Viewed:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="display_most_viewed" name="display_most_viewed" value="1" <?php checked(1, $display_most_viewed, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-item">
                <label for="display_continue_reading">Display Continue Reading:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="display_continue_reading" name="display_continue_reading" value="1" <?php checked(1, $display_continue_reading, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>
            
            <div class="setting-item">
                <label for="display_recent_chapters">Display Recent Chapters:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="display_recent_chapters" name="display_recent_chapters" value="1" <?php checked(1, $display_recent_chapters, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>
            
            <div class="setting-item">
                <label for="display_new_releases">Display New Releases:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="display_new_releases" name="display_new_releases" value="1" <?php checked(1, $display_new_releases, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>
            
            <div class="setting-item">
                <label for="slider_settings">Control Slider (Number of Items):</label>
                <input type="number" class="input-field" id="slider_settings" name="slider_settings" value="<?php echo esc_attr($slider_settings); ?>" placeholder="Enter number of items">
            </div>

            <div class="setting-item">
                <label for="footer_settings">Control Footer (Social Media Links):</label>
                <input type="text" class="input-field" id="footer_settings" name="footer_settings" value="<?php echo esc_attr($footer_settings); ?>" placeholder="Enter social media links">
            </div>

        </div>

        <!-- General Settings Tab -->
        <div id="general-settings" class="settings-tab" style="display: none;">
            <h2>General Settings</h2>
            <div class="setting-item">
                <label for="allow_post">Allow Post Manga/Chapter:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="allow_post" name="allow_post" value="1" <?php checked(1, $allow_post, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-item">
                <label for="enable_requests">Enable Getting Requests:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="enable_requests" name="enable_requests" value="1" <?php checked(1, $enable_requests, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-item">
                <label for="enable_chapter_reports">Enable Getting Chapter Reports:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="enable_chapter_reports" name="enable_chapter_reports" value="1" <?php checked(1, $enable_chapter_reports, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>

            <h2>General Settings - NOT WORKING YET</h2>

            <div class="setting-item">
                <label for="allow_registration">Allow Registration/Login:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="allow_registration" name="allow_registration" value="1" <?php checked(1, $allow_registration, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-item">
                <label for="allow_bookmark">Allow Bookmark:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="allow_bookmark" name="allow_bookmark" value="1" <?php checked(1, $allow_bookmark, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>
            
            <div class="setting-item">
                <label for="reading_history">Allow Reading History:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="reading_history" name="reading_history" value="1" <?php checked(1, $reading_history, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-item">
                <label for="allow_duplicate_upload">Allow Upload Duplicated Chapters:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="allow_duplicate_upload" name="allow_duplicate_upload" value="1" <?php checked(1, $allow_duplicate_upload, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-item">
                <label for="upload_volume_covers">Allow Upload Volume Covers:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="upload_volume_covers" name="upload_volume_covers" value="1" <?php checked(1, $upload_volume_covers, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-item">
                <label for="enable_review">Enable Review Manga/Chapter:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="enable_review" name="enable_review" value="1" <?php checked(1, $enable_review, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-item">
                <label for="enable_view_count">Enable Manga/Chapter Views:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="enable_view_count" name="enable_view_count" value="1" <?php checked(1, $enable_view_count, true); ?>>
                    <span class="slider"></span>
                </label>
            </div>

        </div>

        <?php submit_button('Save Settings'); ?>
    </form>
</div>

<script>
    // JavaScript for tab switching
    document.addEventListener("DOMContentLoaded", function() {
        const tabs = document.querySelectorAll('.nav-tab');
        const settingsTabs = document.querySelectorAll('.settings-tab');

        tabs.forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all tabs and hide all tab content
                tabs.forEach(t => t.classList.remove('nav-tab-active'));
                settingsTabs.forEach(t => t.style.display = 'none');

                // Add active class to the clicked tab and show corresponding content
                this.classList.add('nav-tab-active');
                const targetTab = this.getAttribute('href');
                document.querySelector(targetTab).style.display = 'block';
            });
        });
    });
</script>

<?php 
}
