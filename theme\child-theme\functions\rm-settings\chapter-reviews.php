<?php
function chapter_reviews_callback() {
    global $wpdb;
    $prefix = $wpdb->prefix;

    // Query to get all chapters that need review (where chapter_status = 3)
    $chapters = $wpdb->get_results( 
        $wpdb->prepare("SELECT * FROM {$prefix}manga_chapters WHERE chapter_status = %d", 3)
    );
    ?>

    <div class="wrap wp-manga-studio admin-filter">
        <h1 class="wp-heading-inline">Review Requests</h1>

        <input type="hidden" id="_wpnonce" name="_wpnonce" value="<?php echo wp_create_nonce('accept_chapter_review'); ?>" />
        <input type="hidden" name="_wp_http_referer" value="/wp-admin/edit.php?post_type=wp-manga&amp;page=manga-review-request" />
        
        <div class="tablenav top">
            <div class="alignleft actions bulkactions"></div>
            <div class="tablenav-pages one-page">
                <span class="displaying-num"><?php echo count($chapters); ?> items</span>
                <br class="clear" />
            </div>
        </div>

        <table class="wp-list-table widefat fixed striped table-view-list user-upload-review-requests">
            <thead>
                <tr>
                    <th scope="col" class="manage-column column-type column-primary">Type</th>
                    <th scope="col" class="manage-column column-manga">Manga</th>
                    <th scope="col" class="manage-column column-chapter_number">Chapter number</th>
                    <th scope="col" class="manage-column column-chapter_title">Chapter title</th>
                    <th scope="col" class="manage-column column-user">User</th>
                    <th scope="col" class="manage-column column-actions">Actions</th>
                </tr>
            </thead>

            <tbody id="the-list" data-wp-lists="list:user-upload-review-request">
                <?php foreach ($chapters as $chapter) { 
                    // Get the manga post link
                    $manga_url = get_permalink($chapter->post_id);

                    // Construct the full chapter URL by appending the chapter_slug to the manga URL
                    $chapter_url = trailingslashit($manga_url) . $chapter->chapter_slug;
                ?>
                    <tr>
                        <td class="type column-type has-row-actions column-primary" data-colname="Type">
                            <span class="request-type pending_chapter">Chapter</span>
                        </td>
                        <td class="manga column-manga" data-colname="Manga">
                            <a href="<?php echo esc_url($manga_url); ?>" title="Preview manga" target="_blank">
                                <span><?php echo get_the_title($chapter->post_id); ?></span>
                            </a>
                        </td>
                        <td class="chapter_number column-chapter_number" data-colname="Chapter number">
                            <a href="<?php echo esc_url($chapter_url); ?>" title="Preview chapter" target="_blank">
                                <span>Chapter <?php echo $chapter->chapter_name; ?></span>
                            </a>
                        </td>
                        <td class="chapter_title column-chapter_title" data-colname="Chapter title">
                            <span><?php echo $chapter->chapter_name_extend; ?></span>
                        </td>
                        <td class="user column-user" data-colname="User">
                            <span><?php echo get_userdata($chapter->user_id)->user_login; ?></span>
                        </td>
                        <td class="actions column-actions" data-colname="Actions">
                            <button class="button button-primary request-action" data-action="accept" data-id="<?php echo $chapter->chapter_id; ?>">Accept</button>
                        </td>
                    </tr>
                <?php } ?>
            </tbody>

            <tfoot>
                <tr>
                    <th scope="col" class="manage-column column-type column-primary">Type</th>
                    <th scope="col" class="manage-column column-manga">Manga</th>
                    <th scope="col" class="manage-column column-chapter_number">Chapter number</th>
                    <th scope="col" class="manage-column column-chapter_title">Chapter title</th>
                    <th scope="col" class="manage-column column-user">User</th>
                    <th scope="col" class="manage-column column-actions">Actions</th>
                </tr>
            </tfoot>
        </table>

        <div class="tablenav bottom">
            <div class="alignleft actions bulkactions"></div>
            <div class="tablenav-pages one-page">
                <span class="displaying-num"><?php echo count($chapters); ?> items</span>
                <br class="clear" />
            </div>
        </div>
    </div>

    <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('.request-action').on('click', function() {
                var chapter_id = $(this).data('id');
                var action = $(this).data('action');
                var nonce = $('#_wpnonce').val();

                if (action === 'accept') {
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'accept_chapter_review',
                            chapter_id: chapter_id,
                            _wpnonce: nonce
                        },
                        success: function(response) {
                            if (response.success) {
                                alert('Chapter accepted successfully!');
                                location.reload(); // Reload the page to update the list
                            } else {
                                alert('Failed to accept the chapter.');
                            }
                        }
                    });
                }
            });
        });
    </script>

<?php
}

add_action('wp_ajax_accept_chapter_review', 'accept_chapter_review');
function accept_chapter_review() {
    check_ajax_referer('accept_chapter_review'); // Security check

    if (isset($_POST['chapter_id'])) {
        global $wpdb;
        $prefix = $wpdb->prefix;
        $chapter_id = intval($_POST['chapter_id']);

        // Update chapter status to 0 (approved)
        $updated = $wpdb->update(
            "{$prefix}manga_chapters",
            array('chapter_status' => 0),
            array('chapter_id' => $chapter_id)
        );

        if ($updated !== false) {
            wp_send_json_success();
        } else {
            wp_send_json_error();
        }
    }
    wp_die();
}
?>