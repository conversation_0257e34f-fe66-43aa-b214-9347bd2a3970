<?php
if( !defined( 'ABSPATH' ) ){
    exit;
}

$user_id = get_current_user_id();
if(!$user_id){
    return;
}

$manager = WP_MANGA_ADDON_CHAPTER_COIN::get_instance();
$subscription_status = $manager->get_user_subscription_status($user_id);

$available_plans = array();
if(class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
    $subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
    $available_plans = $subscription_manager->get_subscription_plans();
}
?>

<div class="user-subscription-panel">
    <h3><?php echo esc_html__('My Subscription', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></h3>
    
    <?php if($subscription_status['has_subscription']): ?>
        <div class="current-subscription active">
            <div class="subscription-info">
                <h4><?php echo esc_html__('Current Subscription', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></h4>
                <div class="subscription-details">
                    <p><strong><?php echo esc_html__('Plan:', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></strong> <?php echo esc_html($subscription_status['plan_name']); ?></p>
                    <p><strong><?php echo esc_html__('Status:', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></strong> 
                        <span class="status-badge status-<?php echo esc_attr($subscription_status['status']); ?>">
                            <?php echo esc_html(ucfirst($subscription_status['status'])); ?>
                        </span>
                    </p>
                    <p><strong><?php echo esc_html__('Expires:', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></strong> 
                        <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($subscription_status['end_date']))); ?>
                    </p>
                </div>
                
                <?php 
                $days_remaining = ceil((strtotime($subscription_status['end_date']) - current_time('timestamp')) / (60 * 60 * 24));
                if($days_remaining > 0): 
                ?>
                <div class="subscription-countdown">
                    <p class="days-remaining">
                        <?php echo sprintf(esc_html__('%d days remaining', MANGA_CHAPTER_COIN_TEXT_DOMAIN), $days_remaining); ?>
                    </p>
                </div>
                <?php endif; ?>
            </div>
            
            <div class="subscription-benefits">
                <h5><?php echo esc_html__('Your Benefits', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></h5>
                <ul>
                    <li><i class="fas fa-check"></i> <?php echo esc_html__('Unlimited access to all premium chapters', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></li>
                    <li><i class="fas fa-check"></i> <?php echo esc_html__('No additional fees', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></li>
                    <li><i class="fas fa-check"></i> <?php echo esc_html__('Priority support', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></li>
                </ul>
            </div>
        </div>
        
    <?php else: ?>
        <div class="no-subscription">
            <div class="subscription-info">
                <h4><?php echo esc_html__('No Active Subscription', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></h4>
                <p><?php echo esc_html__('You currently do not have an active subscription. Subscribe now to get unlimited access to all premium chapters.', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></p>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if(!empty($available_plans)): ?>
    <div class="available-plans">
        <h4><?php echo esc_html__('Available Subscription Plans', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></h4>
        <div class="plans-grid">
            <?php foreach($available_plans as $plan): ?>
            <div class="subscription-plan-card">
                <div class="plan-header">
                    <h5><?php echo esc_html($plan->plan_name); ?></h5>
                    <div class="plan-price">$<?php echo esc_html(number_format($plan->price, 2)); ?></div>
                </div>
                <div class="plan-features">
                    <ul>
                        <li><?php echo sprintf(esc_html__('%d months of access', MANGA_CHAPTER_COIN_TEXT_DOMAIN), $plan->duration_months); ?></li>
                        <li><?php echo esc_html__('Unlimited premium chapters', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></li>
                        <li><?php echo esc_html__('Cancel anytime', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></li>
                    </ul>
                </div>
                <div class="plan-action">
                    <?php if($subscription_status['has_subscription']): ?>
                        <button class="button button-secondary" disabled>
                            <?php echo esc_html__('Already Subscribed', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?>
                        </button>
                    <?php else: ?>
                        <button class="button button-primary btn-subscribe-plan" data-plan-id="<?php echo esc_attr($plan->plan_id); ?>">
                            <?php echo esc_html__('Subscribe Now', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?>
                        </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if($subscription_status['has_subscription']): ?>
    <div class="subscription-actions">
        <h4><?php echo esc_html__('Manage Subscription', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></h4>
        <p><?php echo esc_html__('Need help with your subscription? Contact our support team.', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?></p>
        <button class="button button-secondary btn-cancel-subscription" data-user-id="<?php echo esc_attr($user_id); ?>">
            <?php echo esc_html__('Cancel Subscription', MANGA_CHAPTER_COIN_TEXT_DOMAIN); ?>
        </button>
    </div>
    <?php endif; ?>
</div>
