<?php 
include get_template_directory() . '/inc/vars/manga-archive-var.php';
get_header();
?>

<main>
    <div class="container">
        <section class="mt-5">
            <div class="head">
                <h2><?php single_term_title(); ?></h2>
            </div>

            <div class="tab-content" data-name="all"></div>
            <div class="original card-lg">
                <?php
                if ($manga_query->have_posts()) :
                    while ($manga_query->have_posts()) : $manga_query->the_post();
                        ?>
                        <div class="unit item-<?php echo esc_attr(get_the_ID()); ?>">
                            <div class="inner">
                                <a href="<?php the_permalink(); ?>" class="poster" data-tip="<?php echo esc_attr(get_the_ID()); ?>">
                                    <div>
                                        <img src="<?php echo esc_url(get_the_post_thumbnail_url()); ?>" alt="<?php the_title(); ?>" style="height: 100%; object-fit: cover;">
                                    </div>
                                </a>
                                <div class="info">
                                    <div>
                                        <span class="type"><?php echo esc_html(get_manga_type(get_the_ID())); ?></span>
                                    </div>
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>

                                    <ul class="content" data-name="chap">
                                        <?php
                                        $chapters = get_latest_chapters(get_the_ID(), 3);
                                        if (!empty($chapters)) :
                                            foreach ($chapters as $chapter) :
                                                $chapter_url = get_permalink() . $chapter->chapter_slug;
                                                $chapter_time_diff = human_time_diff(strtotime($chapter->date), current_time('timestamp'));
                                        ?>
                                            <li>
                                                <a href="<?php echo esc_url($chapter_url); ?>">
                                                    <span><?php echo 'Chapter ' . esc_html($chapter->chapter_name); ?></span>
                                                    <span><?php echo esc_html($chapter_time_diff) . ' ago'; ?></span>
                                                </a>
                                            </li>
                                        <?php endforeach; else : ?>
                                            <li><span><?php _e('No chapters available.', 'wp-fire'); ?></span></li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else : ?>
                    <p><?php _e('No manga found for these filters.', 'wp-fire'); ?></p>
                <?php endif; wp_reset_postdata(); ?>
            </div>

            <?php
            if ($total_pages > 1): ?>
                <nav class="navigation">
                    <ul class="pagination">
                        <li class="page-item<?php echo ($current_page <= 1) ? ' disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo ($current_page > 1) ? add_query_arg('paged', 1) : '#'; ?>" rel="first">«</a>
                        </li>
                        <li class="page-item<?php echo ($current_page <= 1) ? ' disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo ($current_page > 1) ? add_query_arg('paged', $current_page - 1) : '#'; ?>" rel="prev" aria-label="« Previous">‹</a>
                        </li>
                        <?php
                        $start_page = max(1, $current_page - 2);
                        $end_page = min($total_pages, $current_page + 2);

                        for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <li class="page-item<?php echo ($i == $current_page) ? ' active' : ''; ?>">
                                <?php if ($i == $current_page): ?>
                                    <span class="page-link"><?php echo $i; ?></span>
                                <?php else: ?>
                                    <a class="page-link" href="<?php echo add_query_arg('paged', $i); ?>"><?php echo $i; ?></a>
                                <?php endif; ?>
                            </li>
                        <?php endfor; ?>
                        <li class="page-item<?php echo ($current_page >= $total_pages) ? ' disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo ($current_page < $total_pages) ? add_query_arg('paged', $current_page + 1) : '#'; ?>" rel="next" aria-label="Next »">›</a>
                        </li>
                        <li class="page-item<?php echo ($current_page >= $total_pages) ? ' disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo ($current_page < $total_pages) ? add_query_arg('paged', $total_pages) : '#'; ?>" rel="last">»</a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </section>
    </div>
</main>

<?php get_footer(); ?>