document.addEventListener('DOMContentLoaded', function () {
    // Keyboard navigation for chapters (left/right arrows)
    if (typeof $ !== 'undefined') {
        if ($('body').hasClass('keyboard-navigate')) {
            $(document).on('keydown', function(e) {
                if (document.activeElement) {
                    const tagName = document.activeElement.tagName.toUpperCase();
                    const activeElementClassName = (typeof document.activeElement.className === 'string') ? document.activeElement.className : '';
                    if (tagName === 'INPUT' || tagName === 'TEXTAREA' || activeElementClassName.includes('ql-editor')) {
                        return; 
                    }
                }
                if (e.keyCode === 37) { // Left arrow key (should go right)
                    const nextNavHolder = $('button#number-go-right'); 
                    if (nextNavHolder.length > 0) {
                        const nextButton = $('button#number-go-right'); 
                        if (nextButton.length > 0 && !nextButton.prop('disabled')) {
                            nextButton[0].click();
                            document.body.focus();
                        }
                    }
                } else if (e.keyCode === 39) { // Right arrow key (should go left)
                    const prevNavHolder = $('button#number-go-left'); 
                    if (prevNavHolder.length > 0) {
                        const prevButton = $('button#number-go-left'); 
                        if (prevButton.length > 0 && !prevButton.prop('disabled')) {
                            prevButton[0].click();
                            document.body.focus();
                        }
                    }
                }
            });
        }
    }
});
