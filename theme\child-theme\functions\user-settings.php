<?php

add_action('wp_ajax_update_user_profile', 'update_user_profile');
add_action('wp_ajax_nopriv_update_user_profile', 'update_user_profile'); // Optional for non-logged-in users

function update_user_profile() {
    if (!is_user_logged_in()) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }

    $current_user = wp_get_current_user();
    
    // Sanitize user input
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $password_confirmation = isset($_POST['password_confirmation']) ? $_POST['password_confirmation'] : '';

    // Validate name (not empty)
    if (empty($name)) {
        wp_send_json_error(['message' => 'Le nom est requis']);
        return;
    }

    // Validate email format
    if (!is_email($email)) {
        wp_send_json_error(['message' => "L'adresse e-mail n'est pas valide"]);
        return;
    }

    // Validate email uniqueness
    if ($email !== $current_user->user_email && email_exists($email)) {
        wp_send_json_error(['message' => "Cette adresse e-mail est déjà utilisée"]);
        return;
    }

    // Update user display name and email
    $update_data = [
        'ID' => $current_user->ID,
        'display_name' => $name,
        'user_email' => $email,
    ];
    
    // Update password if it is provided and matches confirmation
    if (!empty($password)) {
        if (strlen($password) < 6) {
            wp_send_json_error(['message' => 'Le mot de passe doit comporter au moins 6 caractères']);
            return;
        }

        if ($password !== $password_confirmation) {
            wp_send_json_error(['message' => 'Les mots de passe ne correspondent pas']);
            return;
        }

        // Update the password
        wp_set_password($password, $current_user->ID);
        
        // Send success response for password change
        wp_send_json_success(['message' => 'Mot de passe changé avec succès. Vous allez être déconnecté.', 'redirect' => true]);
        
        // Logout user
        wp_logout();
        return; // Ensure we don't run the rest of the code
    }

    // Perform user update
    $updated_user = wp_update_user($update_data);
    
    if (is_wp_error($updated_user)) {
        wp_send_json_error(['message' => 'Une erreur est survenue lors de la mise à jour du profil. Veuillez réessayer.']);
    } else {
        // Handle profile picture upload if provided
        if (isset($_FILES['profile_picture']) && !empty($_FILES['profile_picture']['name'])) {
            $file = $_FILES['profile_picture'];
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            // Validate file type and size
            if (in_array($file['type'], $allowed_types) && $file['size'] <= 5 * 1024 * 1024) {
                require_once ABSPATH . 'wp-admin/includes/file.php';
                $uploaded = wp_handle_upload($file, ['test_form' => false]);
                if (isset($uploaded['url'])) {
                    update_user_meta($current_user->ID, 'profile_picture', esc_url_raw($uploaded['url']));
                }
            }
        }
        // Send success response for profile update
        wp_send_json_success(['message' => 'Profil mis à jour avec succès']);
    }
}
