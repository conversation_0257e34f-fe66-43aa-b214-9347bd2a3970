<?php
$current_user = wp_get_current_user();
?>


<div class="dropdown u-menu">
    <button class="btn nav-btn ani-toggle" type="button" data-placeholder="false" data-toggle="dropdown" aria-expanded="false" data-target="user-ul">
        <?php 
        $profile_pic = get_user_meta($current_user->ID, 'profile_picture', true);
        $avatar_url = $profile_pic ? esc_url($profile_pic) : get_avatar_url($current_user->ID);
        echo '<img src="' . $avatar_url . '" alt="User Avatar" />';
        ?>
    </button>
    <div class="dropdown-menu dropdown-menu-right">
        <a class="dropdown-item" href="<?php echo site_url('/user/?tab=account-settings'); ?>"><i class="fa-solid fa-user"></i> <?php echo esc_attr( ucfirst($current_user->display_name) ); ?></a>
        <a class="dropdown-item" href="<?php echo site_url('/user/?tab=bookmark'); ?>"><i class="fa-solid fa-bookmark"></i> Favoris</a>
        <a class="dropdown-item" href="<?php echo site_url('/user/?tab=reading'); ?>"><i class="fa-solid fa-clock-rotate-left"></i> Historique</a>
        <?php if (is_user_logged_in()) : ?>
            <a class="dropdown-item" href="<?php echo wp_logout_url(home_url()); ?>">
                <i class="fa-solid fa-arrow-up-left-from-circle"></i>
                <span>Déconnexion</span>
            </a>
        <?php endif; ?>
    </div>
</div>