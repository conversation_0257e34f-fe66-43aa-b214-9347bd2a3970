<?php

if( !get_option('allow_post', 1) ) {
    return; // Just exit without doing anything if posting is not allowed
}

// Handle AJAX chapter upload
function ajax_upload_chapter() {
    // Disable watermark processing to prevent imagefill errors
    remove_action('upload_after_extract', 'add_watermark_folder');
    remove_action('upload_after_extract', 'add_watermark_file');

    // Check nonce for security
    check_ajax_referer('load_manga_nonce', 'nonce');

    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('Unauthorized access.', 401);
        wp_die();
    }

    // Handle the form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['chapter-file'])) {
        // Restrict access: only administrators, editors, or the manga author can upload chapters
        $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
        $current_user = wp_get_current_user();
        $author_id = get_post_field('post_author', $post_id);
        if (!$post_id || (!in_array('administrator', $current_user->roles) && !in_array('editor', $current_user->roles) && $current_user->ID !== (int) $author_id)) {
            wp_send_json_error('Unauthorized access.', 403);
            wp_die();
        }

        global $wpdb;

        $storage_type = get_option('manga_storage_type', 'local');

        $chapter_number = sanitize_text_field($_POST['chapter-number']);
        $chapter_title = sanitize_text_field($_POST['chapter-title']);
        $user_id = get_current_user_id();

        // Initialize an array to hold error messages
        $errors = [];

        // Validate Chapter Number
        if (empty($chapter_number)) {
            $errors[] = 'Le numéro de chapitre est requis.';
        } elseif (!preg_match('/^\d+(\.\d+)?$/', $chapter_number)) {
            $errors[] = 'Le numéro de chapitre ne doit contenir que des chiffres.';
        }
    
        // Validate Chapter Title (if provided)
        if ($chapter_title && preg_match('/[<>]/', $chapter_title)) {
            $errors[] = 'Le titre du chapitre ne peut pas contenir de symboles.';
        }

        // Validate that the chapter file is a .zip
        if (!isset($_FILES['chapter-file']['name']) || empty($_FILES['chapter-file']['name'])) {
            $errors[] = 'Le fichier du chapitre est requis.';
        } elseif ($_FILES['chapter-file']['type'] !== 'application/zip' && pathinfo($_FILES['chapter-file']['name'], PATHINFO_EXTENSION) !== 'zip') {
            $errors[] = 'Veuillez téléverser un fichier valide.';
        }

        // If there are errors, return them as a response
        if (!empty($errors)) {
            wp_send_json_error(implode('<br>', $errors)); // Use <br> for line breaks between error messages
            wp_die();
        }

        // Get the current post ID and title
        $post_id = intval($_POST['post_id']);
        $manga_title = get_the_title($post_id);

        // Get the manga unique ID from post meta
        $manga_unique_id = get_post_meta($post_id, 'manga_unique_id', true);

        // Generate a unique ID for the chapter folder
        $chapter_uid = wp_generate_uuid4();

        $existing_chapter = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}manga_chapters WHERE post_id = %d AND chapter_name = %s",
            $post_id, $chapter_number
        ));

        // Generate unique slug for the chapter
        $chapter_slug = $chapter_number;
        if ($existing_chapter > 0) {
            $suffix = 1;
            while ($wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}manga_chapters WHERE post_id = %d AND chapter_slug = %s",
                $post_id, $chapter_slug . '-' . $suffix
            )) > 0) {
                $suffix++;
            }
            $chapter_slug = $chapter_number . '-' . $suffix;
        }

        // Check if chapter already exists
        $chapter_status = ($existing_chapter > 0) ? 3 : 0;
        $success_message = ($chapter_status === 3) 
            ? 'Chapitre téléchargé avec succès, en attente de révision.' 
            : 'Chapitre téléchargé avec succès';


        // Handle file upload
        $file = $_FILES['chapter-file'];
        $upload_dir = wp_upload_dir();
        $zip_path = $upload_dir['path'] . '/' . basename($file['name']);

        // Move uploaded ZIP file to the uploads directory
        if (move_uploaded_file($file['tmp_name'], $zip_path)) {
            // Extract ZIP file
            $zip = new ZipArchive;
            if ($zip->open($zip_path) === TRUE) {
                // Create chapter directory
                $chapter_dir = $upload_dir['basedir'] . "/WP-manga/data/$manga_unique_id/$chapter_uid"; // Updated path
                wp_mkdir_p($chapter_dir);

                // Function to extract files from ZIP recursively
                function extract_images_from_zip($zip, $chapter_dir) {
                    $image_files = [];
                    for ($i = 0; $i < $zip->numFiles; $i++) {
                        $filename = $zip->getNameIndex($i);

                        // Skip directories
                        if (substr($filename, -1) == '/') {
                            continue;
                        }

                        // Get the path inside the zip
                        $fileinfo = pathinfo($filename);
                        
                        // Extract file if it's an image
                        if (in_array(strtolower($fileinfo['extension']), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) { // Added 'webp' here
                            // Create directories inside chapter_dir if needed
                            $filepath = $chapter_dir . '/' . $fileinfo['basename'];
                            copy("zip://".$zip->filename."#".$filename, $filepath);
                            $image_files[] = $fileinfo['basename'];
                        }
                    }
                    return $image_files;
                }

                // Extract images from the ZIP file, even if they are in directories
                $image_files = extract_images_from_zip($zip, $chapter_dir);
                $zip->close();

                // If no images were found, return an error
                if (empty($image_files)) {
                    wp_send_json_error('Aucune image valide trouvée dans le fichier.');
                    wp_die();
                }

                // Sort the image files numerically based on their names
                usort($image_files, function($a, $b) {
                    return intval(pathinfo($a, PATHINFO_FILENAME)) - intval(pathinfo($b, PATHINFO_FILENAME));
                });

                // Prepare chapter data with dynamic URL paths
                $chapter_data = [];
                $counter = 1;
                foreach ($image_files as $image_file) {
                    if ($storage_type === 'external-ftp-server') {
                        // Use the external FTP server URL format
                        $chapter_data[$counter] = [
                            'src' => home_url("/wp-content/uploads/WP-manga/data/$manga_unique_id/$chapter_uid/$image_file"),  // [--- EXTERNAL ---]
                            'mime' => mime_content_type("$chapter_dir/$image_file")
                        ];
                    } else {
                        // Use the local URL format
                        $chapter_data[$counter] = [
                            'src' => "$manga_unique_id/$chapter_uid/$image_file",  // [--- LOCAL ---]
                            'mime' => mime_content_type("$chapter_dir/$image_file")
                        ];
                    }
                    $counter++;
                }

                // Insert chapter into {$wpdb->prefix}manga_chapters
                $wpdb->insert( $wpdb->prefix . 'manga_chapters', [
                    'post_id' => $post_id,
                    'chapter_name' => $chapter_number,
                    'chapter_name_extend' => $chapter_title,
                    'chapter_slug' => $chapter_slug,
                    'storage_in_use' => $storage_type,  // Dynamically set storage based on admin choice
                    'date' => current_time('mysql'),
                    'date_gmt' => current_time('mysql', 1),
                    'volume_id' => 0,
                    'chapter_metas' => maybe_serialize(['uid' => $chapter_uid]),
                    'user_id' => $user_id,
                    'chapter_status' => $chapter_status
                ]);

                // Get inserted chapter ID
                $chapter_id = $wpdb->insert_id;

                // Encode the chapter data as JSON before storing it
                $json_chapter_data = json_encode($chapter_data);

                // Insert chapter data into {$wpdb->prefix}manga_chapters_data
                $wpdb->insert( $wpdb->prefix . 'manga_chapters_data', [
                    'chapter_id' => $chapter_id,
                    'storage' => $storage_type, // Dynamically set storage based on admin choice
                    'data' => $json_chapter_data // Store JSON data here
                ]);

                // Success message
                wp_send_json_success($success_message);
            } else {
                wp_send_json_error('Échec de l\'extraction des images du fichier.');
            }

            // Delete the uploaded ZIP file
            unlink($zip_path);
        } else {
            wp_send_json_error('Échec du téléchargement du chapitre.');
        }


    }
    wp_die();
}
add_action('wp_ajax_upload_chapter', 'ajax_upload_chapter');