<section class="side-manga default-style overflow-hidden">
    <div class="head">
        <h2>Recommended to you</h2>
    </div>
    <div class="original card-sm body">
        <?php
        global $wpdb;

        // Get the ID of the current post
        $current_post_id = get_the_ID();

        // Meta query for filtering adult manga
        $meta_query = array();

        if (!$show_adult) {
            $meta_query[] = array(
                'key'     => 'manga_adult_content',
                'value'   => 'yes',
                'compare' => 'NOT LIKE'
            );
        }

        // Fetch a larger number of wp-manga posts to ensure we get 6 with chapters
        $args = array(
            'post_type'      => 'wp-manga',
            'posts_per_page' => 20, // Fetch more to account for posts without chapters
            'orderby'        => 'rand',
            'post__not_in'   => array($current_post_id), // Exclude the current post
            'meta_query'     => $meta_query // Add the meta query here
        );

        $random_manga = new WP_Query($args);

        $posts_with_chapters = array();
        $post_count = 0; // Counter for valid posts with chapters

        if ($random_manga->have_posts()) :
            while ($random_manga->have_posts() && $post_count < 6) : $random_manga->the_post();

                $post_id = get_the_ID();
                $table_name = $wpdb->prefix . 'manga_chapters';

                // Check if the post has chapters
                $has_chapters = $wpdb->get_var(
                    $wpdb->prepare("
                        SELECT COUNT(*) 
                        FROM {$table_name} 
                        WHERE post_id = %d
                    ", $post_id)
                );

                // If the post has chapters, add it to the list
                if ($has_chapters > 0) {
                    $latest_chapter = $wpdb->get_var(
                        $wpdb->prepare("
                            SELECT MAX(CAST(chapter_name AS UNSIGNED)) 
                            FROM {$table_name} 
                            WHERE post_id = %d
                        ", $post_id)
                    );

                    $posts_with_chapters[] = array(
                        'permalink'       => get_the_permalink(),
                        'thumbnail_url'   => get_the_post_thumbnail_url( get_the_ID(), 'manga_cover' ),
                        'title'           => get_the_title(),
                        'latest_chapter'  => $latest_chapter,
                        'genres'          => wp_get_post_terms(get_the_ID(), 'wp-manga-genre')
                    );

                    $post_count++;
                }

            endwhile;
            wp_reset_postdata();
        endif;

        // Display exactly 6 posts with chapters
        foreach ($posts_with_chapters as $post) :
        ?>
            <a class="unit" href="<?php echo esc_url($post['permalink']); ?>">
                <div class="poster">
                    <div><img src="<?php echo esc_url($post['thumbnail_url']); ?>" alt="<?php echo esc_attr($post['title']); ?>" /></div>
                </div>
                <div class="info">
                    <h6><?php echo esc_html($post['title']); ?></h6>
                    <div class="genres-wrapper">
                        <?php 
                        $count = 0;
                        foreach ($post['genres'] as $genre) {
                            if ($count >= 3) break;
                            echo '<span class="genre-tag">' . esc_html($genre->name) . '</span>';
                            $count++;
                        }
                        ?>
                    </div>
                    <?php
                    // Get post ID from the permalink
                    $current_post_id = url_to_postid($post['permalink']);
                    
                    // Get rating data for the current post
                    $total_votes = get_post_meta($current_post_id, '_manga_total_votes', true);
                    $user_votes = get_post_meta($current_post_id, '_manga_user_votes', true);
                    $vote_count = is_array($user_votes) ? count($user_votes) : 0;
                    
                    // Calculate average rating
                    $avg_rating = $vote_count > 0 ? ($total_votes / $vote_count) : 0;
                    ?>
                    <div class="rating-wrapper">
                        <i class="fas fa-star"></i>
                        <span><?php echo number_format($avg_rating, 1); ?></span>
                    </div>
                </div>
            </a>
        <?php endforeach; ?>
    </div>
</section>