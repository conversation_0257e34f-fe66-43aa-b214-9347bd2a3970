<li class="menu-item">
    <a href="#" class="random-manga-link" title="Random Manga">
    <i class="fa-solid fa-ice-cream"></i> Aléatoire 
    </a>
</li>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add event listener to the Random link
    document.querySelector('.random-manga-link').addEventListener('click', function(event) {
        event.preventDefault(); // Prevent the default link behavior

        // Create an XMLHttpRequest to fetch a random post URL
        var xhr = new XMLHttpRequest();
        xhr.open('GET', '<?php echo esc_url(admin_url('admin-ajax.php')); ?>?action=random_manga', true);
        xhr.onload = function() {
            if (xhr.status === 200) {
                // Redirect to the random manga post URL
                window.location.href = xhr.responseText;
            } else {
                console.error('Failed to fetch random manga URL');
            }
        };
        xhr.send();
    });
});
</script>
