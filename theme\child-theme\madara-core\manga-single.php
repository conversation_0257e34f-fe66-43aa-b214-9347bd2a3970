<?php 
require_once __DIR__ . '/../functions/cached-functions.php';
include get_stylesheet_directory() . '/template-parts/manga-single/upload-chapter.php'; // Moved here
$post_id = get_the_ID();
$modified = get_post_field('post_modified', $post_id);
$cache_key = 'manga_single_' . $post_id . '_' . strtotime($modified);

if (!is_user_logged_in()) {
    $cached = manga_cache_get($cache_key);
    if ($cached !== false) {
        echo $cached;
        return;
    }
}

ob_start();

global $wpdb;

$post_id = get_the_ID();
$view_count = get_post_meta($post_id, '_wp_manga_views', true) ?: 0;
update_post_meta($post_id, '_wp_manga_views', ++$view_count);

$chapter_data = $wpdb->get_row($wpdb->prepare("
    SELECT 
        (SELECT chapter_slug 
         FROM {$wpdb->prefix}manga_chapters 
         WHERE post_id = %d 
         ORDER BY chapter_name ASC 
         LIMIT 1) AS first_chapter_slug,
        (SELECT user_id 
         FROM {$wpdb->prefix}manga_chapters 
         WHERE post_id = %d 
         ORDER BY chapter_name DESC 
         LIMIT 1) AS last_chapter_user_id
", $post_id, $post_id));

$start_reading_ch = $chapter_data->first_chapter_slug ?? '';
$ts_id = $chapter_data->last_chapter_user_id ?: get_post_field('post_author', $post_id);
$ts_avatar = get_avatar($ts_id, 50);
$label = $chapter_data->last_chapter_user_id ? 'آخر مترجم' : 'نشر بواسطة';
$content = get_the_content();
$trimmed_content = wp_trim_words($content, 30, '...');
$ts_name = get_the_author_meta('display_name', $ts_id);
$manga_status = get_manga_status($post_id);
$alternative_title = get_post_meta($post_id, '_wp_manga_alternative', true);
$authors = wp_get_post_terms($post_id, 'wp-manga-author');
$artists = wp_get_post_terms($post_id, 'wp-manga-artist');
$releases = wp_get_post_terms($post_id, 'wp-manga-release');
$genres = wp_get_post_terms($post_id, 'wp-manga-genre');
$types = wp_get_post_terms($post_id, 'wp-manga-type');
$manga_meta_type = get_post_meta($post_id, '_wp_manga_type', true);
$volumes = get_post_meta(get_the_ID(), 'wp_manga_volumes', true);
$allow_post = get_option('allow_post', 1);

function human_readable_date($datetime) {
    $now = current_time('timestamp');
    $diff = $now - strtotime($datetime);

    if ($diff < 60) return sprintf('%d seconds ago', $diff);
    if ($diff < 3600) return sprintf('%d minutes ago', floor($diff / 60));
    if ($diff < 86400) return sprintf('%d hours ago', floor($diff / 3600));
    return date('d M, Y', strtotime($datetime));
}

$chapters = $wpdb->get_results($wpdb->prepare("
    SELECT * 
    FROM {$wpdb->prefix}manga_chapters 
    WHERE post_id = %d AND chapter_status != 3  -- Exclude chapters with status 3
    ORDER BY CAST(chapter_name AS DECIMAL(10,2)) DESC
", $post_id));

$user_id = get_current_user_id();
$bookmarked_mangas = get_user_meta($user_id, 'bookmarked_mangas', true);

if (!empty($bookmarked_mangas)) {
    $query = new WP_Query(array(
        'post_type' => 'wp-manga',
        'post__in' => $bookmarked_mangas,
    ));

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            // Display bookmarked manga posts here
        }
        wp_reset_postdata();
    }
} 

get_header(); ?>

<main>
    <div id="manga-page" data-id="<?php echo get_the_ID(); ?>">
        <div class="manga-detail">
            <div class="container">
                <div class="main-inner">
            <div class="container">
            <?php 
                include get_stylesheet_directory() . '/template-parts/manga-single/content.php'; 
                ?>
        </div>
                </div>
            </div>
        </div>
    </div>
</main>

<div class="modal fade" id="synopsis" style="display: none;" aria-hidden="true">
    <div class="modal-dialog limit-w modal-dialog-centered">
        <div class="modal-content p-4">
            <div class="modal-close" data-dismiss="modal"><i class="fa-solid fa-xmark"></i></div>
                <?php the_content(); ?>
            </div>
    </div>
</div>

<?php get_footer();
$content = ob_get_clean();
if (!is_user_logged_in()) {
    manga_cache_set($cache_key, $content, 86400); // Cache for 24 hours
}
echo $content;