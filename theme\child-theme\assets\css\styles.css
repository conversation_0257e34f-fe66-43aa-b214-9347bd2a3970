*,
::after,
::before {
  box-sizing: border-box;
}
html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: #fff0;
}
article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
  display: block;
}
body {
  margin: 0;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--icol);
  text-align: left;
  background-color: var(--bgc);
}
[tabindex="-1"]:focus:not(:focus-visible) {
  outline: 0 !important;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}
p {
  margin-top: 0;
  margin-bottom: 1rem;
}
abbr[data-original-title],
abbr[title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
  -webkit-text-decoration-skip-ink: none;
  text-decoration-skip-ink: none;
}
address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}
dl,
ol,
ul {
  margin-top: 0;
  margin-bottom: 1rem;
}
ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0;
}
dt {
  font-weight: 700;
}
dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1rem;
}
b,
strong {
  font-weight: bolder;
}
small {
  font-size: 80%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
a {
  color: var(--color-11);
  text-decoration: none;
  background-color: #fff0;
}
a:hover {
  color: var(--color-7);
  text-decoration: none;
}
a:not([href]):not([class]) {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}
code,
kbd,
pre,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;
  font-size: 1em;
}
pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar;
}
figure {
  margin: 0 0 1rem;
}
img {
  vertical-align: middle;
  border-style: none;
}
svg {
  overflow: hidden;
  vertical-align: middle;
}
table {
  border-collapse: collapse;
}
caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: var(--pt);
  text-align: left;
  caption-side: bottom;
}
th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}
label {
  display: inline-block;
  margin-bottom: 0.5rem;
}
button {
  border-radius: 0;
}
button:focus:not(:focus-visible) {
  outline: 0;
}
button,
input,
optgroup,
select,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
[role="button"] {
  cursor: pointer;
}
select {
  word-wrap: normal;
}
[type="button"],
[type="reset"],
[type="submit"],
button {
  -webkit-appearance: button;
}
[type="button"]:not(:disabled),
[type="reset"]:not(:disabled),
[type="submit"]:not(:disabled),
button:not(:disabled) {
  cursor: pointer;
}
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner,
button::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}
textarea {
  overflow: auto;
  resize: vertical;
}
fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
summary {
  display: list-item;
  cursor: pointer;
}
template {
  display: none;
}
[hidden] {
  display: none !important;
}
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}
.h1,
h1 {
  font-size: 2.5rem;
}
.h2,
h2 {
  font-size: 2rem;
}
.h3,
h3 {
  font-size: 1.75rem;
}
.h4,
h4 {
  font-size: 1.5rem;
}
.h5,
h5 {
  font-size: 1.25rem;
}
.h6,
h6 {
  font-size: 1rem;
}
.lead {
  font-size: 1.25rem;
  font-weight: 300;
}
.display-1 {
  font-size: 6rem;
  font-weight: 300;
  line-height: 1.2;
}
.display-2 {
  font-size: 5.5rem;
  font-weight: 300;
  line-height: 1.2;
}
.display-3 {
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.2;
}
.display-4 {
  font-size: 3.5rem;
  font-weight: 300;
  line-height: 1.2;
}
hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgb(0 0 0 / 0.1);
}
.small,
small {
  font-size: 0.875em;
  font-weight: 400;
}
.mark,
mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}
.list-unstyled {
  padding-left: 0;
  list-style: none;
}
.list-inline {
  padding-left: 0;
  list-style: none;
}
.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}
.initialism {
  font-size: 90%;
  text-transform: uppercase;
}
.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}
.blockquote-footer {
  display: block;
  font-size: 0.875em;
  color: #6c6c6c;
}
.blockquote-footer::before {
  content: "— ";
}
.img-fluid {
  max-width: 100%;
  height: auto;
}
.img-thumbnail {
  padding: 0.25rem;
  background-color: var(--bgc);
  border: 1px solid #dedede;
  border-radius: 0.5rem;
  max-width: 100%;
  height: auto;
}
.figure {
  display: inline-block;
}
.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}
.figure-caption {
  font-size: 90%;
  color: #6c6c6c;
}
code {
  font-size: 87.5%;
  color: #e83e8c;
  word-wrap: break-word;
}
a > code {
  color: inherit;
}
kbd {
  padding: 4px 8px;
  color: #9c9c9c;
  background-color: #212121;
  border-radius: 0.4rem;
  border: 1px solid #292929;
}
kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
}
pre {
  display: block;
  font-size: 87.5%;
  color: #212121;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}
.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 576px) {
  .container,
  .container-sm {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container,
  .container-md,
  .container-sm {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl {
    max-width: 1140px;
  }
}
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}
.no-gutters {
  margin-right: 0;
  margin-left: 0;
}
.no-gutters > .col,
.no-gutters > [class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}
.col,
.col-1,
.col-10,
.col-11,
.col-12,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-auto,
.col-lg,
.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-auto,
.col-md,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-auto,
.col-sm,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-auto,
.col-xl,
.col-xl-1,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-auto,
.col-xxl,
.col-xxl-1,
.col-xxl-10,
.col-xxl-11,
.col-xxl-12,
.col-xxl-2,
.col-xxl-3,
.col-xxl-4,
.col-xxl-5,
.col-xxl-6,
.col-xxl-7,
.col-xxl-8,
.col-xxl-9,
.col-xxl-auto {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}
.col {
  flex-basis: 0%;
  flex-grow: 1;
  max-width: 100%;
}
.row-cols-1 > * {
  flex: 0 0 100%;
  max-width: 100%;
}
.row-cols-2 > * {
  flex: 0 0 50%;
  max-width: 50%;
}
.row-cols-3 > * {
  flex: 0 0 33.3333333333%;
  max-width: 33.3333333333%;
}
.row-cols-4 > * {
  flex: 0 0 25%;
  max-width: 25%;
}
.row-cols-5 > * {
  flex: 0 0 20%;
  max-width: 20%;
}
.row-cols-6 > * {
  flex: 0 0 16.6666666667%;
  max-width: 16.6666666667%;
}
.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}
.col-1 {
  flex: 0 0 8.33333333%;
  max-width: 8.33333333%;
}
.col-2 {
  flex: 0 0 16.66666667%;
  max-width: 16.66666667%;
}
.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}
.col-4 {
  flex: 0 0 33.33333333%;
  max-width: 33.33333333%;
}
.col-5 {
  flex: 0 0 41.66666667%;
  max-width: 41.66666667%;
}
.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}
.col-7 {
  flex: 0 0 58.33333333%;
  max-width: 58.33333333%;
}
.col-8 {
  flex: 0 0 66.66666667%;
  max-width: 66.66666667%;
}
.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}
.col-10 {
  flex: 0 0 83.33333333%;
  max-width: 83.33333333%;
}
.col-11 {
  flex: 0 0 91.66666667%;
  max-width: 91.66666667%;
}
.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}
.order-first {
  order: -1;
}
.order-last {
  order: 13;
}
.order-0 {
  order: 0;
}
.order-1 {
  order: 1;
}
.order-2 {
  order: 2;
}
.order-3 {
  order: 3;
}
.order-4 {
  order: 4;
}
.order-5 {
  order: 5;
}
.order-6 {
  order: 6;
}
.order-7 {
  order: 7;
}
.order-8 {
  order: 8;
}
.order-9 {
  order: 9;
}
.order-10 {
  order: 10;
}
.order-11 {
  order: 11;
}
.order-12 {
  order: 12;
}
.offset-1 {
  margin-left: 8.33333333%;
}
.offset-2 {
  margin-left: 16.66666667%;
}
.offset-3 {
  margin-left: 25%;
}
.offset-4 {
  margin-left: 33.33333333%;
}
.offset-5 {
  margin-left: 41.66666667%;
}
.offset-6 {
  margin-left: 50%;
}
.offset-7 {
  margin-left: 58.33333333%;
}
.offset-8 {
  margin-left: 66.66666667%;
}
.offset-9 {
  margin-left: 75%;
}
.offset-10 {
  margin-left: 83.33333333%;
}
.offset-11 {
  margin-left: 91.66666667%;
}
@media (min-width: 576px) {
  .col-sm {
    flex-basis: 0%;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-sm-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-sm-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-sm-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .row-cols-sm-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-sm-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-sm-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-sm-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-sm-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-sm-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-sm-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-sm-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-sm-first {
    order: -1;
  }
  .order-sm-last {
    order: 13;
  }
  .order-sm-0 {
    order: 0;
  }
  .order-sm-1 {
    order: 1;
  }
  .order-sm-2 {
    order: 2;
  }
  .order-sm-3 {
    order: 3;
  }
  .order-sm-4 {
    order: 4;
  }
  .order-sm-5 {
    order: 5;
  }
  .order-sm-6 {
    order: 6;
  }
  .order-sm-7 {
    order: 7;
  }
  .order-sm-8 {
    order: 8;
  }
  .order-sm-9 {
    order: 9;
  }
  .order-sm-10 {
    order: 10;
  }
  .order-sm-11 {
    order: 11;
  }
  .order-sm-12 {
    order: 12;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.33333333%;
  }
  .offset-sm-2 {
    margin-left: 16.66666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.33333333%;
  }
  .offset-sm-5 {
    margin-left: 41.66666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.33333333%;
  }
  .offset-sm-8 {
    margin-left: 66.66666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.33333333%;
  }
  .offset-sm-11 {
    margin-left: 91.66666667%;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex-basis: 0%;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-md-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-md-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-md-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .row-cols-md-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-md-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-md-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-md-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-md-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-md-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-md-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-md-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-md-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-md-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-md-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-md-first {
    order: -1;
  }
  .order-md-last {
    order: 13;
  }
  .order-md-0 {
    order: 0;
  }
  .order-md-1 {
    order: 1;
  }
  .order-md-2 {
    order: 2;
  }
  .order-md-3 {
    order: 3;
  }
  .order-md-4 {
    order: 4;
  }
  .order-md-5 {
    order: 5;
  }
  .order-md-6 {
    order: 6;
  }
  .order-md-7 {
    order: 7;
  }
  .order-md-8 {
    order: 8;
  }
  .order-md-9 {
    order: 9;
  }
  .order-md-10 {
    order: 10;
  }
  .order-md-11 {
    order: 11;
  }
  .order-md-12 {
    order: 12;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.33333333%;
  }
  .offset-md-2 {
    margin-left: 16.66666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.33333333%;
  }
  .offset-md-5 {
    margin-left: 41.66666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.33333333%;
  }
  .offset-md-8 {
    margin-left: 66.66666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.33333333%;
  }
  .offset-md-11 {
    margin-left: 91.66666667%;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex-basis: 0%;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-lg-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-lg-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-lg-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .row-cols-lg-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-lg-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-lg-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-lg-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-lg-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-lg-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-lg-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-lg-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-lg-first {
    order: -1;
  }
  .order-lg-last {
    order: 13;
  }
  .order-lg-0 {
    order: 0;
  }
  .order-lg-1 {
    order: 1;
  }
  .order-lg-2 {
    order: 2;
  }
  .order-lg-3 {
    order: 3;
  }
  .order-lg-4 {
    order: 4;
  }
  .order-lg-5 {
    order: 5;
  }
  .order-lg-6 {
    order: 6;
  }
  .order-lg-7 {
    order: 7;
  }
  .order-lg-8 {
    order: 8;
  }
  .order-lg-9 {
    order: 9;
  }
  .order-lg-10 {
    order: 10;
  }
  .order-lg-11 {
    order: 11;
  }
  .order-lg-12 {
    order: 12;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.33333333%;
  }
  .offset-lg-2 {
    margin-left: 16.66666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.33333333%;
  }
  .offset-lg-5 {
    margin-left: 41.66666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.33333333%;
  }
  .offset-lg-8 {
    margin-left: 66.66666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.33333333%;
  }
  .offset-lg-11 {
    margin-left: 91.66666667%;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex-basis: 0%;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-xl-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-xl-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-xl-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .row-cols-xl-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-xl-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-xl-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-xl-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-xl-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-xl-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-xl-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-xl-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-xl-first {
    order: -1;
  }
  .order-xl-last {
    order: 13;
  }
  .order-xl-0 {
    order: 0;
  }
  .order-xl-1 {
    order: 1;
  }
  .order-xl-2 {
    order: 2;
  }
  .order-xl-3 {
    order: 3;
  }
  .order-xl-4 {
    order: 4;
  }
  .order-xl-5 {
    order: 5;
  }
  .order-xl-6 {
    order: 6;
  }
  .order-xl-7 {
    order: 7;
  }
  .order-xl-8 {
    order: 8;
  }
  .order-xl-9 {
    order: 9;
  }
  .order-xl-10 {
    order: 10;
  }
  .order-xl-11 {
    order: 11;
  }
  .order-xl-12 {
    order: 12;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xl-11 {
    margin-left: 91.66666667%;
  }
}
@media (min-width: 1400px) {
  .col-xxl {
    flex-basis: 0%;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-xxl-1 > * {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-xxl-2 > * {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-xxl-3 > * {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .row-cols-xxl-4 > * {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-xxl-5 > * {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-xxl-6 > * {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-xxl-1 {
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-xxl-2 {
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-xxl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-xxl-4 {
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-xxl-5 {
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-xxl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-xxl-7 {
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-xxl-8 {
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-xxl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-xxl-10 {
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-xxl-11 {
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-xxl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-xxl-first {
    order: -1;
  }
  .order-xxl-last {
    order: 13;
  }
  .order-xxl-0 {
    order: 0;
  }
  .order-xxl-1 {
    order: 1;
  }
  .order-xxl-2 {
    order: 2;
  }
  .order-xxl-3 {
    order: 3;
  }
  .order-xxl-4 {
    order: 4;
  }
  .order-xxl-5 {
    order: 5;
  }
  .order-xxl-6 {
    order: 6;
  }
  .order-xxl-7 {
    order: 7;
  }
  .order-xxl-8 {
    order: 8;
  }
  .order-xxl-9 {
    order: 9;
  }
  .order-xxl-10 {
    order: 10;
  }
  .order-xxl-11 {
    order: 11;
  }
  .order-xxl-12 {
    order: 12;
  }
  .offset-xxl-0 {
    margin-left: 0;
  }
  .offset-xxl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xxl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xxl-3 {
    margin-left: 25%;
  }
  .offset-xxl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xxl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xxl-6 {
    margin-left: 50%;
  }
  .offset-xxl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xxl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xxl-9 {
    margin-left: 75%;
  }
  .offset-xxl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xxl-11 {
    margin-left: 91.66666667%;
  }
}
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--icol);
}
.table td,
.table th {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #dedede;
}
.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dedede;
}
.table tbody + tbody {
  border-top: 2px solid #dedede;
}
.table-sm td,
.table-sm th {
  padding: 0.3rem;
}
.table-bordered {
  border: 1px solid #dedede;
}
.table-bordered td,
.table-bordered th {
  border: 1px solid #dedede;
}
.table-bordered thead td,
.table-bordered thead th {
  border-bottom-width: 2px;
}
.table-borderless tbody + tbody,
.table-borderless td,
.table-borderless th,
.table-borderless thead th {
  border: 0;
}
.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgb(0 0 0 / 0.05);
}
.table-hover tbody tr:hover {
  color: var(--icol);
  background-color: rgb(0 0 0 / 0.075);
}
.table-primary,
.table-primary > td,
.table-primary > th {
  background-color: #c8dfef;
}
.table-primary tbody + tbody,
.table-primary td,
.table-primary th,
.table-primary thead th {
  border-color: #9ac3e1;
}
.table-hover .table-primary:hover {
  background-color: #b4d4e9;
}
.table-hover .table-primary:hover > td,
.table-hover .table-primary:hover > th {
  background-color: #b4d4e9;
}
.table-primary1,
.table-primary1 > td,
.table-primary1 > th {
  background-color: #dfecf5;
}
.table-primary1 tbody + tbody,
.table-primary1 td,
.table-primary1 th,
.table-primary1 thead th {
  border-color: #c3dbed;
}
.table-hover .table-primary1:hover {
  background-color: #cce0ef;
}
.table-hover .table-primary1:hover > td,
.table-hover .table-primary1:hover > th {
  background-color: #cce0ef;
}
.table-primary2,
.table-primary2 > td,
.table-primary2 > th {
  background-color: #c1cfd9;
}
.table-primary2 tbody + tbody,
.table-primary2 td,
.table-primary2 th,
.table-primary2 thead th {
  border-color: #8da6b9;
}
.table-hover .table-primary2:hover {
  background-color: #b1c3cf;
}
.table-hover .table-primary2:hover > td,
.table-hover .table-primary2:hover > th {
  background-color: #b1c3cf;
}
.table-secondary,
.table-secondary > td,
.table-secondary > th {
  background-color: #bec1c6;
}
.table-secondary tbody + tbody,
.table-secondary td,
.table-secondary th,
.table-secondary thead th {
  border-color: #878d96;
}
.table-hover .table-secondary:hover {
  background-color: #b0b4ba;
}
.table-hover .table-secondary:hover > td,
.table-hover .table-secondary:hover > th {
  background-color: #b0b4ba;
}
.table-secondary1,
.table-secondary1 > td,
.table-secondary1 > th {
  background-color: #c0c4ca;
}
.table-secondary1 tbody + tbody,
.table-secondary1 td,
.table-secondary1 th,
.table-secondary1 thead th {
  border-color: #8a919d;
}
.table-hover .table-secondary1:hover {
  background-color: #b2b7be;
}
.table-hover .table-secondary1:hover > td,
.table-hover .table-secondary1:hover > th {
  background-color: #b2b7be;
}
.table-secondary2,
.table-secondary2 > td,
.table-secondary2 > th {
  background-color: #bdc0c4;
}
.table-secondary2 tbody + tbody,
.table-secondary2 td,
.table-secondary2 th,
.table-secondary2 thead th {
  border-color: #858991;
}
.table-hover .table-secondary2:hover {
  background-color: #b0b3b8;
}
.table-hover .table-secondary2:hover > td,
.table-hover .table-secondary2:hover > th {
  background-color: #b0b3b8;
}
.table-major-color,
.table-major-color > td,
.table-major-color > th {
  background-color: #fff;
}
.table-major-color tbody + tbody,
.table-major-color td,
.table-major-color th,
.table-major-color thead th {
  border-color: #fff;
}
.table-hover .table-major-color:hover {
  background-color: #f2f2f2;
}
.table-hover .table-major-color:hover > td,
.table-hover .table-major-color:hover > th {
  background-color: #f2f2f2;
}
.table-success,
.table-success > td,
.table-success > th {
  background-color: #c3e6cb;
}
.table-success tbody + tbody,
.table-success td,
.table-success th,
.table-success thead th {
  border-color: #8fd19e;
}
.table-hover .table-success:hover {
  background-color: #b1dfbb;
}
.table-hover .table-success:hover > td,
.table-hover .table-success:hover > th {
  background-color: #b1dfbb;
}
.table-info,
.table-info > td,
.table-info > th {
  background-color: #bee5eb;
}
.table-info tbody + tbody,
.table-info td,
.table-info th,
.table-info thead th {
  border-color: #86cfda;
}
.table-hover .table-info:hover {
  background-color: #abdde5;
}
.table-hover .table-info:hover > td,
.table-hover .table-info:hover > th {
  background-color: #abdde5;
}
.table-warning,
.table-warning > td,
.table-warning > th {
  background-color: #ffeeba;
}
.table-warning tbody + tbody,
.table-warning td,
.table-warning th,
.table-warning thead th {
  border-color: #ffdf7e;
}
.table-hover .table-warning:hover {
  background-color: #ffe8a1;
}
.table-hover .table-warning:hover > td,
.table-hover .table-warning:hover > th {
  background-color: #ffe8a1;
}
.table-danger,
.table-danger > td,
.table-danger > th {
  background-color: #f5c6cb;
}
.table-danger tbody + tbody,
.table-danger td,
.table-danger th,
.table-danger thead th {
  border-color: #ed969e;
}
.table-hover .table-danger:hover {
  background-color: #f1b0b7;
}
.table-hover .table-danger:hover > td,
.table-hover .table-danger:hover > th {
  background-color: #f1b0b7;
}
.table-light,
.table-light > td,
.table-light > th {
  background-color: #fafafa;
}
.table-light tbody + tbody,
.table-light td,
.table-light th,
.table-light thead th {
  border-color: #f5f5f5;
}
.table-hover .table-light:hover {
  background-color: #ededed;
}
.table-hover .table-light:hover > td,
.table-hover .table-light:hover > th {
  background-color: #ededed;
}
.table-dark,
.table-dark > td,
.table-dark > th {
  background-color: #c6c6c6;
}
.table-dark tbody + tbody,
.table-dark td,
.table-dark th,
.table-dark thead th {
  border-color: #959595;
}
.table-hover .table-dark:hover {
  background-color: #b9b9b9;
}
.table-hover .table-dark:hover > td,
.table-hover .table-dark:hover > th {
  background-color: #b9b9b9;
}
.table-active,
.table-active > td,
.table-active > th {
  background-color: rgb(0 0 0 / 0.075);
}
.table-hover .table-active:hover {
  background-color: rgb(0 0 0 / 0.075);
}
.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th {
  background-color: rgb(0 0 0 / 0.075);
}
.table .thead-dark th {
  color: #fff;
  background-color: #343434;
  border-color: #474747;
}
.table .thead-light th {
  color: #494949;
  background-color: #e9e9e9;
  border-color: #dedede;
}
.table-dark {
  color: #fff;
  background-color: #343434;
}
.table-dark td,
.table-dark th,
.table-dark thead th {
  border-color: #474747;
}
.table-dark.table-bordered {
  border: 0;
}
.table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgb(255 255 255 / 0.05);
}
.table-dark.table-hover tbody tr:hover {
  color: #fff;
  background-color: rgb(255 255 255 / 0.075);
}
@media (max-width: 575.98px) {
  .table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-sm > .table-bordered {
    border: 0;
  }
}
@media (max-width: 767.98px) {
  .table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-md > .table-bordered {
    border: 0;
  }
}
@media (max-width: 991.98px) {
  .table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-lg > .table-bordered {
    border: 0;
  }
}
@media (max-width: 1199.98px) {
  .table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-xl > .table-bordered {
    border: 0;
  }
}
@media (max-width: 1399.98px) {
  .table-responsive-xxl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-xxl > .table-bordered {
    border: 0;
  }
}
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.table-responsive > .table-bordered {
  border: 0;
}
.form-control {
  display: block;
  width: 100%;
  padding: 0.475rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #fff;
  background-color: var(--color-3);
  background-clip: padding-box;
  border: 1px solid #333333;
  border-radius: 0.5rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control::-ms-expand {
  background-color: #fff0;
  border: 0;
}
.form-control:focus {
  color: #fff;
  background-color: var(--color-3);
  border-color: #9fc6e3;
  outline: 0;
  box-shadow: 0 0 0 0 rgba(138, 138, 138, 0.25);
}
.form-control::-moz-placeholder {
  color: var(--pt);
  opacity: 1;
}
.form-control::placeholder {
  color: #4f4f4f;
  opacity: 1;
}
.form-control:disabled,
.form-control[readonly] {
  background-color: #4f4f4f;
  opacity: 1;
}
input[type="date"].form-control,
input[type="datetime-local"].form-control,
input[type="month"].form-control,
input[type="time"].form-control {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
select.form-control:-moz-focusring {
  color: #fff0;
  text-shadow: 0 0 0 #fff;
}
select.form-control:focus::-ms-value {
  color: #fff;
  background-color: var(--color-3);
}
.form-control-file,
.form-control-range {
  display: block;
  width: 100%;
}
.col-form-label {
  padding-top: calc(0.475rem + 1px);
  padding-bottom: calc(0.475rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}
.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
  line-height: 1.5;
}
.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
  line-height: 1.5;
}
.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.475rem 0;
  margin-bottom: 0;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--icol);
  background-color: #fff0;
  border: solid #fff0;
  border-width: 1px 0;
}
.form-control-plaintext.form-control-lg,
.form-control-plaintext.form-control-sm {
  padding-right: 0;
  padding-left: 0;
}
.form-control-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}
.form-control-lg {
  height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 1rem;
}
select.form-control[multiple],
select.form-control[size] {
  height: auto;
}
textarea.form-control {
  height: auto;
}
.form-group {
  margin-bottom: 1rem;
}
.form-text {
  display: block;
  margin-top: 0.25rem;
}
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}
.form-row > .col,
.form-row > [class*="col-"] {
  padding-right: 5px;
  padding-left: 5px;
}
.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}
.form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
}
.form-check-input:disabled ~ .form-check-label,
.form-check-input[disabled] ~ .form-check-label {
  color: var(--pt);
}
.form-check-label {
  margin-bottom: 0;
}
.form-check-inline {
  display: inline-flex;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;
}
.form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
  margin-right: 0.3125rem;
  margin-left: 0;
}
.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #28a745;
}
.valid-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgb(40 167 69 / 0.9);
  border-radius: 0.5rem;
}
.form-row > .col > .valid-tooltip,
.form-row > [class*="col-"] > .valid-tooltip {
  left: 5px;
}
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip,
.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip {
  display: block;
}
.form-control.is-valid,
.was-validated .form-control:valid {
  border-color: #28a745;
  padding-right: calc(1.5em + 0.95rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.2375rem) center;
  background-size: calc(0.75em + 0.475rem) calc(0.75em + 0.475rem);
}
.form-control.is-valid:focus,
.was-validated .form-control:valid:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0 rgb(40 167 69 / 0.25);
}
.was-validated select.form-control:valid,
select.form-control.is-valid {
  padding-right: 3rem !important;
  background-position: right 1.5rem center;
}
.was-validated textarea.form-control:valid,
textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.95rem);
  background-position: top calc(0.375em + 0.2375rem) right
    calc(0.375em + 0.2375rem);
}
.custom-select.is-valid,
.was-validated .custom-select:valid {
  border-color: #28a745;
  padding-right: calc(0.75em + 2.4625rem) !important;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343434' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e")
      right 0.75rem center/8px 10px no-repeat,
    var(--color-3)
      url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e")
      center right 1.75rem / calc(0.75em + 0.475rem) calc(0.75em + 0.475rem)
      no-repeat;
}
.custom-select.is-valid:focus,
.was-validated .custom-select:valid:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0 rgb(40 167 69 / 0.25);
}
.form-check-input.is-valid ~ .form-check-label,
.was-validated .form-check-input:valid ~ .form-check-label {
  color: #28a745;
}
.form-check-input.is-valid ~ .valid-feedback,
.form-check-input.is-valid ~ .valid-tooltip,
.was-validated .form-check-input:valid ~ .valid-feedback,
.was-validated .form-check-input:valid ~ .valid-tooltip {
  display: block;
}
.custom-control-input.is-valid ~ .custom-control-label,
.was-validated .custom-control-input:valid ~ .custom-control-label {
  color: #28a745;
}
.custom-control-input.is-valid ~ .custom-control-label::before,
.was-validated .custom-control-input:valid ~ .custom-control-label::before {
  border-color: #28a745;
}
.custom-control-input.is-valid:checked ~ .custom-control-label::before,
.was-validated
  .custom-control-input:valid:checked
  ~ .custom-control-label::before {
  border-color: #34ce57;
  background-color: #34ce57;
}
.custom-control-input.is-valid:focus ~ .custom-control-label::before,
.was-validated
  .custom-control-input:valid:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 0 rgb(40 167 69 / 0.25);
}
.custom-control-input.is-valid:focus:not(:checked)
  ~ .custom-control-label::before,
.was-validated
  .custom-control-input:valid:focus:not(:checked)
  ~ .custom-control-label::before {
  border-color: #28a745;
}
.custom-file-input.is-valid ~ .custom-file-label,
.was-validated .custom-file-input:valid ~ .custom-file-label {
  border-color: #28a745;
}
.custom-file-input.is-valid:focus ~ .custom-file-label,
.was-validated .custom-file-input:valid:focus ~ .custom-file-label {
  border-color: #28a745;
  box-shadow: 0 0 0 0 rgb(40 167 69 / 0.25);
}
.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #dc3545;
}
.invalid-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgb(220 53 69 / 0.9);
  border-radius: 0.5rem;
}
.form-row > .col > .invalid-tooltip,
.form-row > [class*="col-"] > .invalid-tooltip {
  left: 5px;
}
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip,
.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip {
  display: block;
}
.form-control.is-invalid,
.was-validated .form-control:invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.95rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.2375rem) center;
  background-size: calc(0.75em + 0.475rem) calc(0.75em + 0.475rem);
}
.form-control.is-invalid:focus,
.was-validated .form-control:invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0 rgb(220 53 69 / 0.25);
}
.was-validated select.form-control:invalid,
select.form-control.is-invalid {
  padding-right: 3rem !important;
  background-position: right 1.5rem center;
}
.was-validated textarea.form-control:invalid,
textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.95rem);
  background-position: top calc(0.375em + 0.2375rem) right
    calc(0.375em + 0.2375rem);
}
.custom-select.is-invalid,
.was-validated .custom-select:invalid {
  border-color: #dc3545;
  padding-right: calc(0.75em + 2.4625rem) !important;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343434' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e")
      right 0.75rem center/8px 10px no-repeat,
    var(--color-3)
      url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e")
      center right 1.75rem / calc(0.75em + 0.475rem) calc(0.75em + 0.475rem)
      no-repeat;
}
.custom-select.is-invalid:focus,
.was-validated .custom-select:invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0 rgb(220 53 69 / 0.25);
}
.form-check-input.is-invalid ~ .form-check-label,
.was-validated .form-check-input:invalid ~ .form-check-label {
  color: #dc3545;
}
.form-check-input.is-invalid ~ .invalid-feedback,
.form-check-input.is-invalid ~ .invalid-tooltip,
.was-validated .form-check-input:invalid ~ .invalid-feedback,
.was-validated .form-check-input:invalid ~ .invalid-tooltip {
  display: block;
}
.custom-control-input.is-invalid ~ .custom-control-label,
.was-validated .custom-control-input:invalid ~ .custom-control-label {
  color: #dc3545;
}
.custom-control-input.is-invalid ~ .custom-control-label::before,
.was-validated .custom-control-input:invalid ~ .custom-control-label::before {
  border-color: #dc3545;
}
.custom-control-input.is-invalid:checked ~ .custom-control-label::before,
.was-validated
  .custom-control-input:invalid:checked
  ~ .custom-control-label::before {
  border-color: #e4606d;
  background-color: #e4606d;
}
.custom-control-input.is-invalid:focus ~ .custom-control-label::before,
.was-validated
  .custom-control-input:invalid:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 0 rgb(220 53 69 / 0.25);
}
.custom-control-input.is-invalid:focus:not(:checked)
  ~ .custom-control-label::before,
.was-validated
  .custom-control-input:invalid:focus:not(:checked)
  ~ .custom-control-label::before {
  border-color: #dc3545;
}
.custom-file-input.is-invalid ~ .custom-file-label,
.was-validated .custom-file-input:invalid ~ .custom-file-label {
  border-color: #dc3545;
}
.custom-file-input.is-invalid:focus ~ .custom-file-label,
.was-validated .custom-file-input:invalid:focus ~ .custom-file-label {
  border-color: #dc3545;
  box-shadow: 0 0 0 0 rgb(220 53 69 / 0.25);
}
.form-inline {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
}
.form-inline .form-check {
  width: 100%;
}
@media (min-width: 576px) {
  .form-inline label {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
  }
  .form-inline .form-group {
    display: flex;
    flex: 0 0 auto;
    flex-flow: row wrap;
    align-items: center;
    margin-bottom: 0;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-plaintext {
    display: inline-block;
  }
  .form-inline .custom-select,
  .form-inline .input-group {
    width: auto;
  }
  .form-inline .form-check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    padding-left: 0;
  }
  .form-inline .form-check-input {
    position: relative;
    flex-shrink: 0;
    margin-top: 0;
    margin-right: 0.25rem;
    margin-left: 0;
  }
  .form-inline .custom-control {
    align-items: center;
    justify-content: center;
  }
  .form-inline .custom-control-label {
    margin-bottom: 0;
  }
}
.btn {
  display: inline-block;
  font-weight: 400;
  color: var(--icol);
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  background-color: #fff0;
  border: 1px solid #fff0;
  padding: 0.475rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.5rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}
.btn:hover {
  color: var(--icol);
  text-decoration: none;
}
.btn.focus,
.btn:focus {
  outline: 0;
  border: 1px solid #333333;
  box-shadow: #333333;
}
.btn.disabled,
.btn:disabled {
  opacity: 0.65;
}
.btn:not(:disabled):not(.disabled) {
  cursor: pointer;
}
a.btn.disabled,
fieldset:disabled a.btn {
  pointer-events: none;
}

/*load more button */

.sloading:before {
  content: " ";
  display: block;
  background: 0;
  animation: ldld-default .5s ease-in-out infinite;
  border-radius: 50%;
  width: 100%;
  height: 100%;
  margin: 0;
  box-sizing: border-box;
  border: 2px solid #fff;
  border-color: currentColor transparent
}

.sloading {
  position: relative;
  display: inline-block;
  width: 1em;
  height: 1em;
  margin-left: 8px;
  vertical-align: middle
}

.load-more-wrapper {
  position: relative
}

#load-more-manga {
  position: relative;
  width: 100%;
  background: 0 0;
  padding: 10px 0;
  border-radius: 8px;
  font-size: 16px;
  color: #5f5f5f;
  gap: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid #1d1d1d;
  justify-content: center
}

#load-more-manga:hover {
  background: #242424;
  color: #fff
}

#load-more-manga .sloading {
  position: relative;
  display: inline-block;
  top: auto;
  left: auto;
  margin-left: 8px;
  vertical-align: middle
}

.load-more-wrapper.text-center {
  padding-top: 1rem;
  margin-top: 1rem
}

/* end */

.btn-primary {
  color: #151515;
  background-color: var(--color-7);
  border-color: var(--color-7);
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: center;
  gap: 4px;
}
.btn-primary:hover {
  color: #151515;
  background-color: #4c85bb;
  border-color: #4c85bb;
}
.btn-primary.focus,
.btn-primary:focus {
  background-color: #a18505;
  border-color: #a18505;
  box-shadow: 0 0 0 0 rgba(235, 194, 14, 0.5);
}
.btn-primary.disabled,
.btn-primary:disabled {
  color: #151515;
  background-color: var(--color-7);
  border-color: var(--color-7);
}
.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show > .btn-primary.dropdown-toggle {
  color: #151515;
  background-color: #edc718;
  border-color: #edc718;
}
.btn-primary:not(:disabled):not(.disabled).active:focus,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgba(235, 194, 14, 0.5);
}
.btn-primary1 {
  color: #212121;
  background-color: var(--color-10);
  border-color: var(--color-10);
}
.btn-primary1:hover {
  color: #212121;
  background-color: #a9cce6;
  border-color: #b2d1e9;
}
.btn-primary1.focus,
.btn-primary1:focus {
  color: #212121;
  background-color: #a9cce6;
  border-color: #b2d1e9;
  box-shadow: 0 0 0 0 rgb(123 163 193 / 0.5);
}
.btn-primary1.disabled,
.btn-primary1:disabled {
  color: #212121;
  background-color: var(--color-10);
  border-color: var(--color-10);
}
.btn-primary1:not(:disabled):not(.disabled).active,
.btn-primary1:not(:disabled):not(.disabled):active,
.show > .btn-primary1.dropdown-toggle {
  color: #212121;
  background-color: #b2d1e9;
  border-color: #bcd7eb;
}
.btn-primary1:not(:disabled):not(.disabled).active:focus,
.btn-primary1:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary1.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(123 163 193 / 0.5);
}
.btn-primary2 {
  color: #fff;
  background-color: var(--color-6);
  border-color: var(--color-6);
}
.btn-primary2:hover {
  color: #fff;
  background-color: #edc718;
  border-color: #2f70a0;
}
.btn-primary2.focus,
.btn-primary2:focus {
  color: #fff;
  background-color: #edc718;
  border-color: #2f70a0;
  box-shadow: 0 0 0 0 rgb(68 110 141 / 0.5);
}
.btn-primary2.disabled,
.btn-primary2:disabled {
  color: #fff;
  background-color: var(--color-6);
  border-color: var(--color-6);
}
.btn-primary2:not(:disabled):not(.disabled).active,
.btn-primary2:not(:disabled):not(.disabled):active,
.show > .btn-primary2.dropdown-toggle {
  color: #fff;
  background-color: #2f70a0;
  border-color: #3277aa;
}
.btn-primary2:not(:disabled):not(.disabled).active:focus,
.btn-primary2:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary2.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(68 110 141 / 0.5);
}
.btn-secondary {
  color: #fff;
  background-color: var(--bgcards);
  border-color: var(--bgcards);
}
.btn-secondary:hover {
  color: #fff;
  background-color: #464646;
  border-color: #171717;
}
.btn-secondary.focus,
.btn-secondary:focus {
  color: #fff;
  background-color: #ff6a7b;
  border-color: #171717;
  box-shadow: 0 0 0 0 rgb(59 68 83 / 0.5);
}
.btn-secondary.disabled,
.btn-secondary:disabled {
  color: #fff;
  background-color: var(--bgcards);
  border-color: var(--bgcards);
}
.btn-secondary:not(:disabled):not(.disabled).active,
.btn-secondary:not(:disabled):not(.disabled):active,
.show > .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #171717;
}
.btn-secondary:not(:disabled):not(.disabled).active:focus,
.btn-secondary:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(59 68 83 / 0.5);
}
.btn-secondary1 {
  color: #818181;
  border-color: var(--secb);
}
.btn-secondary1:hover {
  color: #fff;
  background-color: #333333;
  border-color: #333333;
}
.btn-secondary1.focus,
.btn-secondary1:focus {
  color: #fff;
  background-color: #333333;
  border-color: #333333;
  box-shadow: 0 0 0 0 rgb(64 76 95 / 0.5);
}
.btn-secondary1.disabled,
.btn-secondary1:disabled {
  color: #fff;
  background-color: var(--secb);
  border-color: var(--secb);
}
.btn-secondary1:not(:disabled):not(.disabled).active,
.btn-secondary1:not(:disabled):not(.disabled):active,
.show > .btn-secondary1.dropdown-toggle {
  color: #fff;
  background-color: #333333;
  border-color: #2e2e2e;
}
.btn-secondary1:not(:disabled):not(.disabled).active:focus,
.btn-secondary1:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary1.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(64 76 95 / 0.5);
}
.btn-secondary2 {
  color: #fff;
  background-color: var(--color-3);
  border-color: var(--color-3);
}
.btn-secondary2:hover {
  color: #fff;
  background-color: #202f47;
  border-color: #24344f;
}
.btn-secondary2.focus,
.btn-secondary2:focus {
  color: #fff;
  background-color: #202f47;
  border-color: #24344f;
  box-shadow: 0 0 0 0 rgb(55 63 76 / 0.5);
}
.btn-secondary2.disabled,
.btn-secondary2:disabled {
  color: #fff;
  background-color: var(--color-3);
  border-color: var(--color-3);
}
.btn-secondary2:not(:disabled):not(.disabled).active,
.btn-secondary2:not(:disabled):not(.disabled):active,
.show > .btn-secondary2.dropdown-toggle {
  color: #fff;
  background-color: #24344f;
  border-color: #171717;
}
.btn-secondary2:not(:disabled):not(.disabled).active:focus,
.btn-secondary2:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary2.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(55 63 76 / 0.5);
}
.btn-major-color {
  color: #212121;
  background-color: #fff;
  border-color: #fff;
}
.btn-major-color:hover {
  color: #212121;
  background-color: #fff;
  border-color: #fff;
}
.btn-major-color.focus,
.btn-major-color:focus {
  color: #212121;
  background-color: #fff;
  border-color: #fff;
  box-shadow: 0 0 0 0 rgb(222 222 222 / 0.5);
}
.btn-major-color.disabled,
.btn-major-color:disabled {
  color: #212121;
  background-color: #fff;
  border-color: #fff;
}
.btn-major-color:not(:disabled):not(.disabled).active,
.btn-major-color:not(:disabled):not(.disabled):active,
.show > .btn-major-color.dropdown-toggle {
  color: #212121;
  background-color: #fff;
  border-color: #fff;
}
.btn-major-color:not(:disabled):not(.disabled).active:focus,
.btn-major-color:not(:disabled):not(.disabled):active:focus,
.show > .btn-major-color.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(222 222 222 / 0.5);
}
.btn-success {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.btn-success:hover {
  color: #fff;
  background-color: #2fc652;
  border-color: #34ce57;
}
.btn-success.focus,
.btn-success:focus {
  color: #fff;
  background-color: #2fc652;
  border-color: #34ce57;
  box-shadow: 0 0 0 0 rgb(72 180 97 / 0.5);
}
.btn-success.disabled,
.btn-success:disabled {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.btn-success:not(:disabled):not(.disabled).active,
.btn-success:not(:disabled):not(.disabled):active,
.show > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #34ce57;
  border-color: #3ed160;
}
.btn-success:not(:disabled):not(.disabled).active:focus,
.btn-success:not(:disabled):not(.disabled):active:focus,
.show > .btn-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(72 180 97 / 0.5);
}
.btn-info {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.btn-info:hover {
  color: #fff;
  background-color: #1bc0da;
  border-color: #1fc8e3;
}
.btn-info.focus,
.btn-info:focus {
  color: #fff;
  background-color: #1bc0da;
  border-color: #1fc8e3;
  box-shadow: 0 0 0 0 rgb(58 176 195 / 0.5);
}
.btn-info.disabled,
.btn-info:disabled {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.btn-info:not(:disabled):not(.disabled).active,
.btn-info:not(:disabled):not(.disabled):active,
.show > .btn-info.dropdown-toggle {
  color: #212121;
  background-color: #1fc8e3;
  border-color: #2acbe4;
}
.btn-info:not(:disabled):not(.disabled).active:focus,
.btn-info:not(:disabled):not(.disabled):active:focus,
.show > .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(58 176 195 / 0.5);
}
.btn-warning {
  color: #212121;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-warning:hover {
  color: #212121;
  background-color: #ffcb2d;
  border-color: #ffce3a;
}
.btn-warning.focus,
.btn-warning:focus {
  color: #212121;
  background-color: #ffcb2d;
  border-color: #ffce3a;
  box-shadow: 0 0 0 0 rgb(222 169 11 / 0.5);
}
.btn-warning.disabled,
.btn-warning:disabled {
  color: #212121;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-warning:not(:disabled):not(.disabled).active,
.btn-warning:not(:disabled):not(.disabled):active,
.show > .btn-warning.dropdown-toggle {
  color: #212121;
  background-color: #ffce3a;
  border-color: #ffd147;
}
.btn-warning:not(:disabled):not(.disabled).active:focus,
.btn-warning:not(:disabled):not(.disabled):active:focus,
.show > .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(222 169 11 / 0.5);
}
.btn-danger {
  border-color: #dc3545;
  background: transparent;
  padding: 6px 10px;
  border-radius: 8px;
  color: #5f5f5f;
  gap: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid #1d1d1d;
}
.btn-danger:hover {
  color: #fff;
  background-color: #e25663;
  border-color: #e4606d;
}
.btn-danger.focus,
.btn-danger:focus {
  color: #fff;
  background-color: #e25663;
  border-color: #e4606d;
  box-shadow: 0 0 0 0 rgb(225 83 97 / 0.5);
}
.btn-danger.disabled,
.btn-danger:disabled {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}
.btn-danger:not(:disabled):not(.disabled).active,
.btn-danger:not(:disabled):not(.disabled):active,
.show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #e4606d;
  border-color: #e56b77;
}
.btn-danger:not(:disabled):not(.disabled).active:focus,
.btn-danger:not(:disabled):not(.disabled):active:focus,
.show > .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(225 83 97 / 0.5);
}
.btn-light {
  color: #212121;
  background-color: #ececec;
  border-color: #ececec;
}
.btn-light:hover {
  color: #212121;
  background-color: #fff;
  border-color: #fff;
}
.btn-light.focus,
.btn-light:focus {
  color: #212121;
  background-color: #fff;
  border-color: #fff;
  box-shadow: 0 0 0 0 rgb(206 206 206 / 0.5);
}
.btn-light.disabled,
.btn-light:disabled {
  color: #212121;
  background-color: #ececec;
  border-color: #ececec;
}
.btn-light:not(:disabled):not(.disabled).active,
.btn-light:not(:disabled):not(.disabled):active,
.show > .btn-light.dropdown-toggle {
  color: #212121;
  background-color: #fff;
  border-color: #fff;
}
.btn-light:not(:disabled):not(.disabled).active:focus,
.btn-light:not(:disabled):not(.disabled):active:focus,
.show > .btn-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(206 206 206 / 0.5);
}
.btn-dark {
  color: #fff;
  background-color: #343434;
  border-color: #343434;
}
.btn-dark:hover {
  color: #fff;
  background-color: #474747;
  border-color: #4e4e4e;
}
.btn-dark.focus,
.btn-dark:focus {
  color: #fff;
  background-color: #474747;
  border-color: #4e4e4e;
  box-shadow: 0 0 0 0 rgb(82 82 82 / 0.5);
}
.btn-dark.disabled,
.btn-dark:disabled {
  color: #fff;
  background-color: #343434;
  border-color: #343434;
}
.btn-dark:not(:disabled):not(.disabled).active,
.btn-dark:not(:disabled):not(.disabled):active,
.show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #4e4e4e;
  border-color: #545454;
}
.btn-dark:not(:disabled):not(.disabled).active:focus,
.btn-dark:not(:disabled):not(.disabled):active:focus,
.show > .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(82 82 82 / 0.5);
}
.btn-outline-primary {
  color: var(--color-7);
  border-color: var(--color-7);
}
.btn-outline-primary:hover {
  color: #fff;
  background-color: var(--color-7);
  border-color: var(--color-7);
}
.btn-outline-primary.focus,
.btn-outline-primary:focus {
  box-shadow: 0 0 0 0 rgb(60 139 198 / 0.5);
}
.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
  color: var(--color-7);
  background-color: #fff0;
}
.btn-outline-primary:not(:disabled):not(.disabled).active,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: var(--color-7);
  border-color: var(--color-7);
}
.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(60 139 198 / 0.5);
}
.btn-outline-primary1 {
  color: var(--color-10);
  border-color: var(--color-10);
}
.btn-outline-primary1:hover {
  color: #212121;
  background-color: var(--color-10);
  border-color: var(--color-10);
}
.btn-outline-primary1.focus,
.btn-outline-primary1:focus {
  box-shadow: 0 0 0 0 rgb(139 186 221 / 0.5);
}
.btn-outline-primary1.disabled,
.btn-outline-primary1:disabled {
  color: var(--color-10);
  background-color: #fff0;
}
.btn-outline-primary1:not(:disabled):not(.disabled).active,
.btn-outline-primary1:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary1.dropdown-toggle {
  color: #212121;
  background-color: var(--color-10);
  border-color: var(--color-10);
}
.btn-outline-primary1:not(:disabled):not(.disabled).active:focus,
.btn-outline-primary1:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-primary1.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(139 186 221 / 0.5);
}
.btn-outline-primary2 {
  color: var(--color-6);
  border-color: var(--color-6);
}
.btn-outline-primary2:hover {
  color: #fff;
  background-color: var(--color-6);
  border-color: var(--color-6);
}
.btn-outline-primary2.focus,
.btn-outline-primary2:focus {
  box-shadow: 0 0 0 0 rgb(35 84 121 / 0.5);
}
.btn-outline-primary2.disabled,
.btn-outline-primary2:disabled {
  color: var(--color-6);
  background-color: #fff0;
}
.btn-outline-primary2:not(:disabled):not(.disabled).active,
.btn-outline-primary2:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary2.dropdown-toggle {
  color: #fff;
  background-color: var(--color-6);
  border-color: var(--color-6);
}
.btn-outline-primary2:not(:disabled):not(.disabled).active:focus,
.btn-outline-primary2:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-primary2.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(35 84 121 / 0.5);
}
.btn-outline-secondary {
  color: var(--bgcards);
  border-color: var(--bgcards);
}
.btn-outline-secondary:hover {
  color: #fff;
  background-color: var(--bgcards);
  border-color: var(--bgcards);
}
.btn-outline-secondary.focus,
.btn-outline-secondary:focus {
  box-shadow: 0 0 0 0 rgb(24 35 53 / 0.5);
}
.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
  color: var(--bgcards);
  background-color: #fff0;
}
.btn-outline-secondary:not(:disabled):not(.disabled).active,
.btn-outline-secondary:not(:disabled):not(.disabled):active,
.show > .btn-outline-secondary.dropdown-toggle {
  color: #fff;
  background-color: var(--bgcards);
  border-color: var(--bgcards);
}
.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(24 35 53 / 0.5);
}
.btn-outline-secondary1 {
  color: var(--secb);
  border-color: var(--secb);
}
.btn-outline-secondary1:hover {
  color: #fff;
  background-color: var(--secb);
  border-color: var(--secb);
}
.btn-outline-secondary1.focus,
.btn-outline-secondary1:focus {
  box-shadow: 0 0 0 0 rgb(30 44 67 / 0.5);
}
.btn-outline-secondary1.disabled,
.btn-outline-secondary1:disabled {
  color: var(--secb);
  background-color: #fff0;
}
.btn-outline-secondary1:not(:disabled):not(.disabled).active,
.btn-outline-secondary1:not(:disabled):not(.disabled):active,
.show > .btn-outline-secondary1.dropdown-toggle {
  color: #fff;
  background-color: var(--secb);
  border-color: var(--secb);
}
.btn-outline-secondary1:not(:disabled):not(.disabled).active:focus,
.btn-outline-secondary1:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-secondary1.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(30 44 67 / 0.5);
}
.btn-outline-secondary2 {
  color: var(--color-3);
  border-color: var(--color-3);
}
.btn-outline-secondary2:hover {
  color: #fff;
  background-color: var(--color-3);
  border-color: var(--color-3);
}
.btn-outline-secondary2.focus,
.btn-outline-secondary2:focus {
  box-shadow: 0 0 0 0 rgb(20 29 44 / 0.5);
}
.btn-outline-secondary2.disabled,
.btn-outline-secondary2:disabled {
  color: var(--color-3);
  background-color: #fff0;
}
.btn-outline-secondary2:not(:disabled):not(.disabled).active,
.btn-outline-secondary2:not(:disabled):not(.disabled):active,
.show > .btn-outline-secondary2.dropdown-toggle {
  color: #fff;
  background-color: var(--color-3);
  border-color: var(--color-3);
}
.btn-outline-secondary2:not(:disabled):not(.disabled).active:focus,
.btn-outline-secondary2:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-secondary2.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(20 29 44 / 0.5);
}
.btn-outline-major-color {
  color: #fff;
  border-color: #fff;
}
.btn-outline-major-color:hover {
  color: #212121;
  background-color: #fff;
  border-color: #fff;
}
.btn-outline-major-color.focus,
.btn-outline-major-color:focus {
  box-shadow: 0 0 0 0 rgb(255 255 255 / 0.5);
}
.btn-outline-major-color.disabled,
.btn-outline-major-color:disabled {
  color: #fff;
  background-color: #fff0;
}
.btn-outline-major-color:not(:disabled):not(.disabled).active,
.btn-outline-major-color:not(:disabled):not(.disabled):active,
.show > .btn-outline-major-color.dropdown-toggle {
  color: #212121;
  background-color: #fff;
  border-color: #fff;
}
.btn-outline-major-color:not(:disabled):not(.disabled).active:focus,
.btn-outline-major-color:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-major-color.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(255 255 255 / 0.5);
}
.btn-outline-success {
  color: #28a745;
  border-color: #28a745;
}
.btn-outline-success:hover {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.btn-outline-success.focus,
.btn-outline-success:focus {
  box-shadow: 0 0 0 0 rgb(40 167 69 / 0.5);
}
.btn-outline-success.disabled,
.btn-outline-success:disabled {
  color: #28a745;
  background-color: #fff0;
}
.btn-outline-success:not(:disabled):not(.disabled).active,
.btn-outline-success:not(:disabled):not(.disabled):active,
.show > .btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}
.btn-outline-success:not(:disabled):not(.disabled).active:focus,
.btn-outline-success:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(40 167 69 / 0.5);
}
.btn-outline-info {
  color: #17a2b8;
  border-color: #17a2b8;
}
.btn-outline-info:hover {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.btn-outline-info.focus,
.btn-outline-info:focus {
  box-shadow: 0 0 0 0 rgb(23 162 184 / 0.5);
}
.btn-outline-info.disabled,
.btn-outline-info:disabled {
  color: #17a2b8;
  background-color: #fff0;
}
.btn-outline-info:not(:disabled):not(.disabled).active,
.btn-outline-info:not(:disabled):not(.disabled):active,
.show > .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}
.btn-outline-info:not(:disabled):not(.disabled).active:focus,
.btn-outline-info:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(23 162 184 / 0.5);
}
.btn-outline-warning {
  color: #ffc107;
  border-color: #ffc107;
}
.btn-outline-warning:hover {
  color: #212121;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-outline-warning.focus,
.btn-outline-warning:focus {
  box-shadow: 0 0 0 0 rgb(255 193 7 / 0.5);
}
.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
  color: #ffc107;
  background-color: #fff0;
}
.btn-outline-warning:not(:disabled):not(.disabled).active,
.btn-outline-warning:not(:disabled):not(.disabled):active,
.show > .btn-outline-warning.dropdown-toggle {
  color: #212121;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-outline-warning:not(:disabled):not(.disabled).active:focus,
.btn-outline-warning:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(255 193 7 / 0.5);
}
.btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
}
.btn-outline-danger:hover {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}
.btn-outline-danger.focus,
.btn-outline-danger:focus {
  box-shadow: 0 0 0 0 rgb(220 53 69 / 0.5);
}
.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
  color: #dc3545;
  background-color: #fff0;
}
.btn-outline-danger:not(:disabled):not(.disabled).active,
.btn-outline-danger:not(:disabled):not(.disabled):active,
.show > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}
.btn-outline-danger:not(:disabled):not(.disabled).active:focus,
.btn-outline-danger:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(220 53 69 / 0.5);
}
.btn-outline-light {
  color: #ececec;
  border-color: #ececec;
}
.btn-outline-light:hover {
  color: #212121;
  background-color: #ececec;
  border-color: #ececec;
}
.btn-outline-light.focus,
.btn-outline-light:focus {
  box-shadow: 0 0 0 0 rgb(236 236 236 / 0.5);
}
.btn-outline-light.disabled,
.btn-outline-light:disabled {
  color: #ececec;
  background-color: #fff0;
}
.btn-outline-light:not(:disabled):not(.disabled).active,
.btn-outline-light:not(:disabled):not(.disabled):active,
.show > .btn-outline-light.dropdown-toggle {
  color: #212121;
  background-color: #ececec;
  border-color: #ececec;
}
.btn-outline-light:not(:disabled):not(.disabled).active:focus,
.btn-outline-light:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(236 236 236 / 0.5);
}
.btn-outline-dark {
  color: #343434;
  border-color: #343434;
}
.btn-outline-dark:hover {
  color: #fff;
  background-color: #343434;
  border-color: #343434;
}
.btn-outline-dark.focus,
.btn-outline-dark:focus {
  box-shadow: 0 0 0 0 rgb(52 52 52 / 0.5);
}
.btn-outline-dark.disabled,
.btn-outline-dark:disabled {
  color: #343434;
  background-color: #fff0;
}
.btn-outline-dark:not(:disabled):not(.disabled).active,
.btn-outline-dark:not(:disabled):not(.disabled):active,
.show > .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #343434;
  border-color: #343434;
}
.btn-outline-dark:not(:disabled):not(.disabled).active:focus,
.btn-outline-dark:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgb(52 52 52 / 0.5);
}
.btn-link {
  font-weight: 400;
  color: var(--color-11);
  text-decoration: none;
}
.btn-link:hover {
  color: var(--color-7);
  text-decoration: none;
}
.btn-link.focus,
.btn-link:focus {
  text-decoration: none;
}
.btn-link.disabled,
.btn-link:disabled {
  color: #6c6c6c;
  pointer-events: none;
}
.btn-group-lg > .btn,
.btn-lg {
  padding: 1rem;
  font-size: 1.15rem;
  line-height: 1.5;
  border-radius: 0.5rem;
}
.btn-group-sm > .btn,
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}
.btn-block {
  display: block;
  width: 100%;
}
.btn-block + .btn-block {
  margin-top: 0.5rem;
}
input[type="button"].btn-block,
input[type="reset"].btn-block,
input[type="submit"].btn-block {
  width: 100%;
}
.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}
.collapse:not(.show) {
  display: none;
}
.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.collapsing.width {
  width: 0;
  height: auto;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.width {
    transition: none;
  }
}
.dropdown,
.dropleft,
.dropright,
.dropup {
  position: relative;
}
.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid #fff0;
  border-bottom: 0;
  border-left: 0.3em solid #fff0;
}
.dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: var(--icol);
  text-align: left;
  list-style: none;
  background-color: var(--bgcards);
  background-clip: padding-box;
  border-radius: 0.5rem;
}
.dropdown-menu-left {
  right: auto;
  left: 0;
}
.dropdown-menu-right {
  right: 0;
  left: auto;
}
@media (min-width: 576px) {
  .dropdown-menu-sm-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-sm-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-md-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-lg-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xl-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1400px) {
  .dropdown-menu-xxl-left {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xxl-right {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid #fff0;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid #fff0;
}
.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropright .dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}
.dropright .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid #fff0;
  border-right: 0;
  border-bottom: 0.3em solid #fff0;
  border-left: 0.3em solid;
}
.dropright .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropright .dropdown-toggle::after {
  vertical-align: 0;
}
.dropleft .dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}
.dropleft .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.dropleft .dropdown-toggle::after {
  display: none;
}
.dropleft .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid #fff0;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid #fff0;
}
.dropleft .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropleft .dropdown-toggle::before {
  vertical-align: 0;
}
.dropdown-menu[x-placement^="bottom"],
.dropdown-menu[x-placement^="left"],
.dropdown-menu[x-placement^="right"],
.dropdown-menu[x-placement^="top"] {
  right: auto;
  bottom: auto;
}
.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #e9e9e9;
}
.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: var(--color-11);
  text-align: inherit;
  white-space: nowrap;
  background-color: #fff0;
  border: 0;
}
.dropdown-item:focus,
.dropdown-item:hover {
  color: #fff;
  text-decoration: none;
  background-color: var(--secb);
}
.dropdown-item.active,
.dropdown-item:active {
  color: var(--color-7);
  text-decoration: none;
  background-color: var(--secb);
}
.dropdown-item.disabled,
.dropdown-item:disabled {
  color: #ababab;
  pointer-events: none;
  background-color: #fff0;
}
.dropdown-menu.show {
  display: block;
}
.dropdown-header {
  display: block;
  padding: 0.5rem 1.5rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: #6c6c6c;
  white-space: nowrap;
}
.dropdown-item-text {
  display: block;
  padding: 0.25rem 1.5rem;
  color: var(--color-11);
}
.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group-vertical > .btn,
.btn-group > .btn {
  position: relative;
  flex: 1 1 auto;
}
.btn-group-vertical > .btn:hover,
.btn-group > .btn:hover {
  z-index: 1;
}
.btn-group-vertical > .btn.active,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn:focus,
.btn-group > .btn.active,
.btn-group > .btn:active,
.btn-group > .btn:focus {
  z-index: 1;
}
.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}
.btn-group > .btn-group:not(:first-child),
.btn-group > .btn:not(:first-child) {
  margin-left: -1px;
}
.btn-group > .btn-group:not(:last-child) > .btn,
.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn-group:not(:first-child) > .btn,
.btn-group > .btn:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}
.dropdown-toggle-split::after,
.dropright .dropdown-toggle-split::after,
.dropup .dropdown-toggle-split::after {
  margin-left: 0;
}
.dropleft .dropdown-toggle-split::before {
  margin-right: 0;
}
.btn-group-sm > .btn + .dropdown-toggle-split,
.btn-sm + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}
.btn-group-lg > .btn + .dropdown-toggle-split,
.btn-lg + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}
.btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn-group:not(:first-child),
.btn-group-vertical > .btn:not(:first-child) {
  margin-top: -1px;
}
.btn-group-vertical > .btn-group:not(:last-child) > .btn,
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn-group:not(:first-child) > .btn,
.btn-group-vertical > .btn:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.btn-group-toggle > .btn,
.btn-group-toggle > .btn-group > .btn {
  margin-bottom: 0;
}
.btn-group-toggle > .btn input[type="checkbox"],
.btn-group-toggle > .btn input[type="radio"],
.btn-group-toggle > .btn-group > .btn input[type="checkbox"],
.btn-group-toggle > .btn-group > .btn input[type="radio"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.input-group > .custom-file,
.input-group > .custom-select,
.input-group > .form-control,
.input-group > .form-control-plaintext {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
  margin-bottom: 0;
}
.input-group > .custom-file + .custom-file,
.input-group > .custom-file + .custom-select,
.input-group > .custom-file + .form-control,
.input-group > .custom-select + .custom-file,
.input-group > .custom-select + .custom-select,
.input-group > .custom-select + .form-control,
.input-group > .form-control + .custom-file,
.input-group > .form-control + .custom-select,
.input-group > .form-control + .form-control,
.input-group > .form-control-plaintext + .custom-file,
.input-group > .form-control-plaintext + .custom-select,
.input-group > .form-control-plaintext + .form-control {
  margin-left: -1px;
}
.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label,
.input-group > .custom-select:focus,
.input-group > .form-control:focus {
  z-index: 3;
}
.input-group > .custom-file .custom-file-input:focus {
  z-index: 4;
}
.input-group > .custom-select:not(:first-child),
.input-group > .form-control:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group > .custom-file {
  display: flex;
  align-items: center;
}
.input-group > .custom-file:not(:last-child) .custom-file-label,
.input-group > .custom-file:not(:last-child) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > .custom-file:not(:first-child) .custom-file-label {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group:not(.has-validation)
  > .custom-file:not(:last-child)
  .custom-file-label,
.input-group:not(.has-validation)
  > .custom-file:not(:last-child)
  .custom-file-label::after,
.input-group:not(.has-validation) > .custom-select:not(:last-child),
.input-group:not(.has-validation) > .form-control:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation
  > .custom-file:nth-last-child(n + 3)
  .custom-file-label,
.input-group.has-validation
  > .custom-file:nth-last-child(n + 3)
  .custom-file-label::after,
.input-group.has-validation > .custom-select:nth-last-child(n + 3),
.input-group.has-validation > .form-control:nth-last-child(n + 3) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group-append,
.input-group-prepend {
  display: flex;
}
.input-group-append .btn,
.input-group-prepend .btn {
  position: relative;
  z-index: 2;
}
.input-group-append .btn:focus,
.input-group-prepend .btn:focus {
  z-index: 3;
}
.input-group-append .btn + .btn,
.input-group-append .btn + .input-group-text,
.input-group-append .input-group-text + .btn,
.input-group-append .input-group-text + .input-group-text,
.input-group-prepend .btn + .btn,
.input-group-prepend .btn + .input-group-text,
.input-group-prepend .input-group-text + .btn,
.input-group-prepend .input-group-text + .input-group-text {
  margin-left: -1px;
}
.input-group-prepend {
  margin-right: -1px;
}
.input-group-append {
  margin-left: -1px;
}
.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.475rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #e9e9e9;

  border-radius: 0.5rem;
}
.input-group-text input[type="checkbox"],
.input-group-text input[type="radio"] {
  margin-top: 0;
}
.input-group-lg > .custom-select,
.input-group-lg > .form-control:not(textarea) {
  height: calc(1.5em + 1rem + 2px);
}
.input-group-lg > .custom-select,
.input-group-lg > .form-control,
.input-group-lg > .input-group-append > .btn,
.input-group-lg > .input-group-append > .input-group-text,
.input-group-lg > .input-group-prepend > .btn,
.input-group-lg > .input-group-prepend > .input-group-text {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 1rem;
}
.input-group-sm > .custom-select,
.input-group-sm > .form-control:not(textarea) {
  height: calc(1.5em + 0.5rem + 2px);
}
.input-group-sm > .custom-select,
.input-group-sm > .form-control,
.input-group-sm > .input-group-append > .btn,
.input-group-sm > .input-group-append > .input-group-text,
.input-group-sm > .input-group-prepend > .btn,
.input-group-sm > .input-group-prepend > .input-group-text {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}
.input-group-lg > .custom-select,
.input-group-sm > .custom-select {
  padding-right: 1.75rem;
}
.input-group.has-validation > .input-group-append:nth-last-child(n + 3) > .btn,
.input-group.has-validation
  > .input-group-append:nth-last-child(n + 3)
  > .input-group-text,
.input-group:not(.has-validation) > .input-group-append:not(:last-child) > .btn,
.input-group:not(.has-validation)
  > .input-group-append:not(:last-child)
  > .input-group-text,
.input-group
  > .input-group-append:last-child
  > .btn:not(:last-child):not(.dropdown-toggle),
.input-group
  > .input-group-append:last-child
  > .input-group-text:not(:last-child),
.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group
  > .input-group-prepend:first-child
  > .input-group-text:not(:first-child),
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.custom-control {
  position: relative;
  z-index: 1;
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5rem;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}
.custom-control-inline {
  display: inline-flex;
  margin-right: 1rem;
}
.custom-control-input {
  position: absolute;
  left: 0;
  z-index: -1;
  width: 1rem;
  height: 1.25rem;
  opacity: 0;
}
.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: var(--color-7);
  background-color: var(--color-7);
}
.custom-control-input:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0 rgb(60 139 198 / 0.25);
}
.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #9fc6e3;
}
.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  color: #fff;
  background-color: #c6ddee;
  border-color: #c6ddee;
}
.custom-control-input:disabled ~ .custom-control-label,
.custom-control-input[disabled] ~ .custom-control-label {
  color: #454a51;
}
.custom-control-input:disabled ~ .custom-control-label::before,
.custom-control-input[disabled] ~ .custom-control-label::before {
  background-color: #2d3136;
}
.custom-control-label {
  position: relative;
  margin-bottom: 0;
  vertical-align: top;
}
.custom-control-label::before {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: "";
  background-color: var(--color-3);
  border: 1px solid #ababab;
}
.custom-control-label::after {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  content: "";
  background: 50%/50% 50% no-repeat;
}
.custom-checkbox .custom-control-label::before {
  border-radius: 0.3rem;
}
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
}
.custom-checkbox
  .custom-control-input:indeterminate
  ~ .custom-control-label::before {
  border-color: var(--color-7);
  background-color: var(--color-7);
}
.custom-checkbox
  .custom-control-input:indeterminate
  ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e");
}
.custom-checkbox
  .custom-control-input:disabled:checked
  ~ .custom-control-label::before {
  background-color: rgb(60 139 198 / 0.5);
}
.custom-checkbox
  .custom-control-input:disabled:indeterminate
  ~ .custom-control-label::before {
  background-color: rgb(60 139 198 / 0.5);
}
.custom-radio .custom-control-label::before {
  border-radius: 50%;
}
.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}
.custom-radio
  .custom-control-input:disabled:checked
  ~ .custom-control-label::before {
  background-color: rgb(60 139 198 / 0.5);
}
.custom-switch {
  padding-left: 2.25rem;
}
.custom-switch .custom-control-label::before {
  left: -2.25rem;
  width: 1.75rem;
  pointer-events: all;
  border-radius: 0.5rem;
}
.custom-switch .custom-control-label::after {
  top: calc(0.25rem + 2px);
  left: calc(-2.25rem + 2px);
  width: calc(1rem - 4px);
  height: calc(1rem - 4px);
  background-color: #ababab;
  border-radius: 0.5rem;
  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .custom-switch .custom-control-label::after {
    transition: none;
  }
}
.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
  background-color: var(--color-3);
  transform: translateX(0.75rem);
}
.custom-switch
  .custom-control-input:disabled:checked
  ~ .custom-control-label::before {
  background-color: rgb(60 139 198 / 0.5);
}
.custom-select {
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.95rem + 2px);
  padding: 0.475rem 1.75rem 0.475rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #fff;
  vertical-align: middle;
  background: var(--color-3)
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343434' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e")
    right 0.75rem center/8px 10px no-repeat;

  border-radius: 0.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.custom-select:focus {
  border-color: #9fc6e3;
  outline: 0;
  box-shadow: 0 0 0 0 rgb(60 139 198 / 0.25);
}
.custom-select:focus::-ms-value {
  color: #fff;
  background-color: var(--color-3);
}
.custom-select[multiple],
.custom-select[size]:not([size="1"]) {
  height: auto;
  padding-right: 0.75rem;
  background-image: none;
}
.custom-select:disabled {
  color: #6c6c6c;
  background-color: #e9e9e9;
}
.custom-select::-ms-expand {
  display: none;
}
.custom-select:-moz-focusring {
  color: #fff0;
  text-shadow: 0 0 0 #fff;
}
.custom-select-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.875rem;
}
.custom-select-lg {
  height: calc(1.5em + 1rem + 2px);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.25rem;
}
.custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.95rem + 2px);
  margin-bottom: 0;
}
.custom-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: calc(1.5em + 0.95rem + 2px);
  margin: 0;
  overflow: hidden;
  opacity: 0;
}
.custom-file-input:focus ~ .custom-file-label {
  border-color: #9fc6e3;
  box-shadow: 0 0 0 0 rgb(60 139 198 / 0.25);
}
.custom-file-input:disabled ~ .custom-file-label,
.custom-file-input[disabled] ~ .custom-file-label {
  background-color: #2d3136;
}
.custom-file-input:lang(en) ~ .custom-file-label::after {
  content: "Browse";
}
.custom-file-input ~ .custom-file-label[data-browse]::after {
  content: attr(data-browse);
}
.custom-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: calc(1.5em + 0.95rem + 2px);
  padding: 0.475rem 0.75rem;
  overflow: hidden;
  font-weight: 400;
  line-height: 1.5;
  color: #fff;
  background-color: var(--color-3);

  border-radius: 0.5rem;
}
.custom-file-label::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  display: block;
  height: calc(1.5em + 0.95rem);
  padding: 0.475rem 0.75rem;
  line-height: 1.5;
  color: #fff;
  content: "Browse";
  background-color: var(--secb);
  border-left: inherit;
  border-radius: 0 0.5rem 0.5rem 0;
}
.custom-range {
  width: 100%;
  height: 1rem;
  padding: 0;
  background-color: #fff0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.custom-range:focus {
  outline: 0;
}
.custom-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px var(--bgc), 0 0 0 0 rgb(60 139 198 / 0.25);
}
.custom-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px var(--bgc), 0 0 0 0 rgb(60 139 198 / 0.25);
}
.custom-range:focus::-ms-thumb {
  box-shadow: 0 0 0 1px var(--bgc), 0 0 0 0 rgb(60 139 198 / 0.25);
}
.custom-range::-moz-focus-outer {
  border: 0;
}
.custom-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: var(--color-7);
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.custom-range::-webkit-slider-thumb:active {
  background-color: #c6ddee;
}
.custom-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: #fff0;
  cursor: pointer;
  background-color: #dedede;
  border-color: #fff0;
  border-radius: 1rem;
}
.custom-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: var(--color-7);
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.custom-range::-moz-range-thumb:active {
  background-color: #c6ddee;
}
.custom-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: #fff0;
  cursor: pointer;
  background-color: #dedede;
  border-color: #fff0;
  border-radius: 1rem;
}
.custom-range::-ms-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: 0;
  margin-right: 0;
  margin-left: 0;
  background-color: var(--color-7);
  border: 0;
  border-radius: 1rem;
  -ms-transition: background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-ms-thumb {
    -ms-transition: none;
    transition: none;
  }
}
.custom-range::-ms-thumb:active {
  background-color: #c6ddee;
}
.custom-range::-ms-track {
  width: 100%;
  height: 0.5rem;
  color: #fff0;
  cursor: pointer;
  background-color: #fff0;
  border-color: #fff0;
  border-width: 0.5rem;
}
.custom-range::-ms-fill-lower {
  background-color: #dedede;
  border-radius: 1rem;
}
.custom-range::-ms-fill-upper {
  margin-right: 15px;
  background-color: #dedede;
  border-radius: 1rem;
}
.custom-range:disabled::-webkit-slider-thumb {
  background-color: #ababab;
}
.custom-range:disabled::-webkit-slider-runnable-track {
  cursor: default;
}
.custom-range:disabled::-moz-range-thumb {
  background-color: #ababab;
}
.custom-range:disabled::-moz-range-track {
  cursor: default;
}
.custom-range:disabled::-ms-thumb {
  background-color: #ababab;
}
.custom-control-label::before,
.custom-file-label,
.custom-select {
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .custom-control-label::before,
  .custom-file-label,
  .custom-select {
    transition: none;
  }
}
.nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.nav-link {
  display: block;
  padding: 0.5rem 1rem;
}
.nav-link:focus,
.nav-link:hover {
  text-decoration: none;
}
.nav-link.disabled {
  color: #6c6c6c;
  pointer-events: none;
  cursor: default;
}
.nav-tabs {
  border-bottom: 0 solid #dedede;
}
.nav-tabs .nav-link {
  margin-bottom: 0;
  background-color: #fff0;
  border: 0 solid #fff0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
  isolation: isolate;
  border-color: #e9e9e9 #e9e9e9 #dedede;
}
.nav-tabs .nav-link.disabled {
  color: #6c6c6c;
  background-color: #fff0;
  border-color: #fff0;
}
.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  color: #fff;
  background-color: var(--color-7);
  border-color: #dedede #dedede var(--color-7);
}
.nav-tabs .dropdown-menu {
  margin-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.nav-pills .nav-link {
  background: 0 0;
  border: 0;
  border-radius: 0.5rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: var(--color-7);
}
.nav-fill .nav-item,
.nav-fill > .nav-link {
  flex: 1 1 auto;
  text-align: center;
}
.nav-justified .nav-item,
.nav-justified > .nav-link {
  flex-basis: 0%;
  flex-grow: 1;
  text-align: center;
}
.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}
.navbar {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}
.navbar .container,
.navbar .container-fluid,
.navbar .container-lg,
.navbar .container-md,
.navbar .container-sm,
.navbar .container-xl {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
.navbar-brand {
  display: inline-block;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  margin-right: 1rem;
  font-size: 1.25rem;
  line-height: inherit;
  white-space: nowrap;
}
.navbar-brand:focus,
.navbar-brand:hover {
  text-decoration: none;
}
.navbar-nav {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}
.navbar-nav .dropdown-menu {
  position: static;
  float: none;
}
.navbar-text {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}
.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.25rem;
  line-height: 1;
  background-color: #fff0;
  border: 1px solid #fff0;
  border-radius: 0.5rem;
}
.navbar-toggler:focus,
.navbar-toggler:hover {
  text-decoration: none;
}
.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: "";
  background: 50%/100% 100% no-repeat;
}
.navbar-nav-scroll {
  max-height: 75vh;
  overflow-y: auto;
}
@media (max-width: 575.98px) {
  .navbar-expand-sm > .container,
  .navbar-expand-sm > .container-fluid,
  .navbar-expand-sm > .container-lg,
  .navbar-expand-sm > .container-md,
  .navbar-expand-sm > .container-sm,
  .navbar-expand-sm > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-sm > .container,
  .navbar-expand-sm > .container-fluid,
  .navbar-expand-sm > .container-lg,
  .navbar-expand-sm > .container-md,
  .navbar-expand-sm > .container-sm,
  .navbar-expand-sm > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
}
@media (max-width: 767.98px) {
  .navbar-expand-md > .container,
  .navbar-expand-md > .container-fluid,
  .navbar-expand-md > .container-lg,
  .navbar-expand-md > .container-md,
  .navbar-expand-md > .container-sm,
  .navbar-expand-md > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-md > .container,
  .navbar-expand-md > .container-fluid,
  .navbar-expand-md > .container-lg,
  .navbar-expand-md > .container-md,
  .navbar-expand-md > .container-sm,
  .navbar-expand-md > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
}
@media (max-width: 991.98px) {
  .navbar-expand-lg > .container,
  .navbar-expand-lg > .container-fluid,
  .navbar-expand-lg > .container-lg,
  .navbar-expand-lg > .container-md,
  .navbar-expand-lg > .container-sm,
  .navbar-expand-lg > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-lg > .container,
  .navbar-expand-lg > .container-fluid,
  .navbar-expand-lg > .container-lg,
  .navbar-expand-lg > .container-md,
  .navbar-expand-lg > .container-sm,
  .navbar-expand-lg > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
}
@media (max-width: 1199.98px) {
  .navbar-expand-xl > .container,
  .navbar-expand-xl > .container-fluid,
  .navbar-expand-xl > .container-lg,
  .navbar-expand-xl > .container-md,
  .navbar-expand-xl > .container-sm,
  .navbar-expand-xl > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xl > .container,
  .navbar-expand-xl > .container-fluid,
  .navbar-expand-xl > .container-lg,
  .navbar-expand-xl > .container-md,
  .navbar-expand-xl > .container-sm,
  .navbar-expand-xl > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
}
@media (max-width: 1399.98px) {
  .navbar-expand-xxl > .container,
  .navbar-expand-xxl > .container-fluid,
  .navbar-expand-xxl > .container-lg,
  .navbar-expand-xxl > .container-md,
  .navbar-expand-xxl > .container-sm,
  .navbar-expand-xxl > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 1400px) {
  .navbar-expand-xxl {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xxl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xxl > .container,
  .navbar-expand-xxl > .container-fluid,
  .navbar-expand-xxl > .container-lg,
  .navbar-expand-xxl > .container-md,
  .navbar-expand-xxl > .container-sm,
  .navbar-expand-xxl > .container-xl {
    flex-wrap: nowrap;
  }
  .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xxl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xxl .navbar-toggler {
    display: none;
  }
}
.navbar-expand {
  flex-flow: row nowrap;
  justify-content: flex-start;
}
.navbar-expand > .container,
.navbar-expand > .container-fluid,
.navbar-expand > .container-lg,
.navbar-expand > .container-md,
.navbar-expand > .container-sm,
.navbar-expand > .container-xl {
  padding-right: 0;
  padding-left: 0;
}
.navbar-expand .navbar-nav {
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}
.navbar-expand > .container,
.navbar-expand > .container-fluid,
.navbar-expand > .container-lg,
.navbar-expand > .container-md,
.navbar-expand > .container-sm,
.navbar-expand > .container-xl {
  flex-wrap: nowrap;
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}
.navbar-light .navbar-brand {
  color: rgb(0 0 0 / 0.9);
}
.navbar-light .navbar-brand:focus,
.navbar-light .navbar-brand:hover {
  color: rgb(0 0 0 / 0.9);
}
.navbar-light .navbar-nav .nav-link {
  color: rgb(0 0 0 / 0.5);
}
.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
  color: rgb(0 0 0 / 0.7);
}
.navbar-light .navbar-nav .nav-link.disabled {
  color: rgb(0 0 0 / 0.3);
}
.navbar-light .navbar-nav .active > .nav-link,
.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .show > .nav-link {
  color: rgb(0 0 0 / 0.9);
}
.navbar-light .navbar-toggler {
  color: rgb(0 0 0 / 0.5);
  border-color: rgb(0 0 0 / 0.1);
}
.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-light .navbar-text {
  color: rgb(0 0 0 / 0.5);
}
.navbar-light .navbar-text a {
  color: rgb(0 0 0 / 0.9);
}
.navbar-light .navbar-text a:focus,
.navbar-light .navbar-text a:hover {
  color: rgb(0 0 0 / 0.9);
}
.navbar-dark .navbar-brand {
  color: #fff;
}
.navbar-dark .navbar-brand:focus,
.navbar-dark .navbar-brand:hover {
  color: #fff;
}
.navbar-dark .navbar-nav .nav-link {
  color: rgb(255 255 255 / 0.5);
}
.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link:hover {
  color: rgb(255 255 255 / 0.75);
}
.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgb(255 255 255 / 0.25);
}
.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .nav-link.active,
.navbar-dark .navbar-nav .nav-link.show,
.navbar-dark .navbar-nav .show > .nav-link {
  color: #fff;
}
.navbar-dark .navbar-toggler {
  color: rgb(255 255 255 / 0.5);
  border-color: rgb(255 255 255 / 0.1);
}
.navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-dark .navbar-text {
  color: rgb(255 255 255 / 0.5);
}
.navbar-dark .navbar-text a {
  color: #fff;
}
.navbar-dark .navbar-text a:focus,
.navbar-dark .navbar-text a:hover {
  color: #fff;
}
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgb(0 0 0 / 0.125);
  border-radius: 0.5rem;
}
.card > hr {
  margin-right: 0;
  margin-left: 0;
}
.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: calc(0.5rem - 1px);
  border-top-right-radius: calc(0.5rem - 1px);
}
.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: calc(0.5rem - 1px);
  border-bottom-left-radius: calc(0.5rem - 1px);
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}
.card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 1.25rem;
}
.card-title {
  margin-bottom: 0.75rem;
}
.card-subtitle {
  margin-top: -0.375rem;
  margin-bottom: 0;
}
.card-text:last-child {
  margin-bottom: 0;
}
.card-link:hover {
  text-decoration: none;
}
.card-link + .card-link {
  margin-left: 1.25rem;
}
.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgb(0 0 0 / 0.03);
  border-bottom: 1px solid rgb(0 0 0 / 0.125);
}
.card-header:first-child {
  border-radius: calc(0.5rem - 1px) calc(0.5rem - 1px) 0 0;
}
.card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgb(0 0 0 / 0.03);
  border-top: 1px solid rgb(0 0 0 / 0.125);
}
.card-footer:last-child {
  border-radius: 0 0 calc(0.5rem - 1px) calc(0.5rem - 1px);
}
.card-header-tabs {
  margin-right: -0.625rem;
  margin-bottom: -0.75rem;
  margin-left: -0.625rem;
  border-bottom: 0;
}
.card-header-pills {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}
.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem;
  border-radius: calc(0.5rem - 1px);
}
.card-img,
.card-img-bottom,
.card-img-top {
  flex-shrink: 0;
  width: 100%;
}
.card-img,
.card-img-top {
  border-top-left-radius: calc(0.5rem - 1px);
  border-top-right-radius: calc(0.5rem - 1px);
}
.card-img,
.card-img-bottom {
  border-bottom-right-radius: calc(0.5rem - 1px);
  border-bottom-left-radius: calc(0.5rem - 1px);
}
.card-deck .card {
  margin-bottom: 15px;
}
@media (min-width: 576px) {
  .card-deck {
    display: flex;
    flex-flow: row wrap;
    margin-right: -15px;
    margin-left: -15px;
  }
  .card-deck .card {
    flex: 1 0 0%;
    margin-right: 15px;
    margin-bottom: 0;
    margin-left: 15px;
  }
}
.card-group > .card {
  margin-bottom: 15px;
}
@media (min-width: 576px) {
  .card-group {
    display: flex;
    flex-flow: row wrap;
  }
  .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-header,
  .card-group > .card:not(:last-child) .card-img-top {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-footer,
  .card-group > .card:not(:last-child) .card-img-bottom {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-header,
  .card-group > .card:not(:first-child) .card-img-top {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-footer,
  .card-group > .card:not(:first-child) .card-img-bottom {
    border-bottom-left-radius: 0;
  }
}
.card-columns .card {
  margin-bottom: 0.75rem;
}
@media (min-width: 576px) {
  .card-columns {
    -moz-column-count: 3;
    column-count: 3;
    -moz-column-gap: 1.25rem;
    column-gap: 1.25rem;
    orphans: 1;
    widows: 1;
  }
  .card-columns .card {
    display: inline-block;
    width: 100%;
  }
}
.accordion {
  overflow-anchor: none;
}
.accordion > .card {
  overflow: hidden;
}
.accordion > .card:not(:last-of-type) {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.accordion > .card:not(:first-of-type) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.accordion > .card > .card-header {
  border-radius: 0;
  margin-bottom: -1px;
}
.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  list-style: none;
  background-color: #e9e9e9;
  border-radius: 0.5rem;
}
.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}
.breadcrumb-item + .breadcrumb-item::before {
  float: left;
  padding-right: 0.5rem;
  color: #6c6c6c;
  content: "/";
}
.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: underline;
}
.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: none;
}
.breadcrumb-item.active {
  color: #6c6c6c;
}
.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.5rem;
}
.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: 0;
  line-height: 1.25;
  color: var(--color-11);
  background-color: var(--bgcards);
  border: 0 solid #dedede;
}
.page-link:hover {
  z-index: 2;
  color: #fff;
  text-decoration: none;
  background-color: var(--secb);
  border-color: #dedede;
}
.page-link:focus {
  z-index: 3;
  outline: 0;
  box-shadow: 0 0 0 0 rgb(60 139 198 / 0.25);
}
.page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.page-item:last-child .page-link {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.page-item.active .page-link {
  z-index: 3;
  color: #181818;
  background-color: var(--color-7);
  border-color: var(--color-7);
}
.page-item.disabled .page-link {
  color: rgb(116 124 136 / 0.5);
  pointer-events: none;
  cursor: auto;
  background-color: var(--color-3);
  border-color: #dedede;
}
.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
  line-height: 1.5;
}
.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 1rem;
  border-bottom-left-radius: 1rem;
}
.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 1rem;
}
.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
}
.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}
.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.5rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .badge {
    transition: none;
  }
}
a.badge:focus,
a.badge:hover {
  text-decoration: none;
}
.badge:empty {
  display: none;
}
.btn .badge {
  position: relative;
  top: -1px;
}
.badge-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem;
}
.badge-primary {
  color: #fff;
  background-color: var(--color-7);
}
a.badge-primary:focus,
a.badge-primary:hover {
  color: #fff;
  background-color: #2f70a0;
}
a.badge-primary.focus,
a.badge-primary:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(60 139 198 / 0.5);
}
.badge-primary1 {
  color: #212121;
  background-color: var(--color-10);
}
a.badge-primary1:focus,
a.badge-primary1:hover {
  color: #212121;
  background-color: #ee4b5d;
}
a.badge-primary1.focus,
a.badge-primary1:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(139 186 221 / 0.5);
}
.badge-primary2 {
  color: #fff;
  background-color: var(--color-6);
}
a.badge-primary2:focus,
a.badge-primary2:hover {
  color: #fff;
  background-color: #183951;
}
a.badge-primary2.focus,
a.badge-primary2:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(35 84 121 / 0.5);
}
.badge-secondary {
  color: #fff;
  background-color: var(--bgcards);
}
a.badge-secondary:focus,
a.badge-secondary:hover {
  color: #fff;
  background-color: #080c12;
}
a.badge-secondary.focus,
a.badge-secondary:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(24 35 53 / 0.5);
}
.badge-secondary1 {
  color: #fff;
  background-color: var(--secb);
}
a.badge-secondary1:focus,
a.badge-secondary1:hover {
  color: #fff;
  background-color: #0e1520;
}
a.badge-secondary1.focus,
a.badge-secondary1:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(30 44 67 / 0.5);
}
.badge-secondary2 {
  color: #fff;
  background-color: var(--color-3);
}
a.badge-secondary2:focus,
a.badge-secondary2:hover {
  color: #fff;
  background-color: #040609;
}
a.badge-secondary2.focus,
a.badge-secondary2:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(20 29 44 / 0.5);
}
.badge-major-color {
  color: #212121;
  background-color: #fff;
}
a.badge-major-color:focus,
a.badge-major-color:hover {
  color: #212121;
  background-color: #e6e6e6;
}
a.badge-major-color.focus,
a.badge-major-color:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(255 255 255 / 0.5);
}
.badge-success {
  color: #fff;
  background-color: #28a745;
}
a.badge-success:focus,
a.badge-success:hover {
  color: #fff;
  background-color: #1e7e34;
}
a.badge-success.focus,
a.badge-success:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(40 167 69 / 0.5);
}
.badge-info {
  color: #fff;
  background-color: #17a2b8;
}
a.badge-info:focus,
a.badge-info:hover {
  color: #fff;
  background-color: #117a8b;
}
a.badge-info.focus,
a.badge-info:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(23 162 184 / 0.5);
}
.badge-warning {
  color: #212121;
  background-color: #ffc107;
}
a.badge-warning:focus,
a.badge-warning:hover {
  color: #212121;
  background-color: #d39e00;
}
a.badge-warning.focus,
a.badge-warning:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(255 193 7 / 0.5);
}
.badge-danger {
  color: #fff;
  background-color: #dc3545;
}
a.badge-danger:focus,
a.badge-danger:hover {
  color: #fff;
  background-color: #bd2130;
}
a.badge-danger.focus,
a.badge-danger:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(220 53 69 / 0.5);
}
.badge-light {
  color: #212121;
  background-color: #ececec;
}
a.badge-light:focus,
a.badge-light:hover {
  color: #212121;
  background-color: #d3d3d3;
}
a.badge-light.focus,
a.badge-light:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(236 236 236 / 0.5);
}
.badge-dark {
  color: #fff;
  background-color: #343434;
}
a.badge-dark:focus,
a.badge-dark:hover {
  color: #fff;
  background-color: #1b1b1b;
}
a.badge-dark.focus,
a.badge-dark:focus {
  outline: 0;
  box-shadow: 0 0 0 0 rgb(52 52 52 / 0.5);
}
.jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  background-color: #e9e9e9;
  border-radius: 1rem;
}
@media (min-width: 576px) {
  .jumbotron {
    padding: 4rem 2rem;
  }
}
.jumbotron-fluid {
  padding-right: 0;
  padding-left: 0;
  border-radius: 0;
}
.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid #fff0;
  border-radius: 0.5rem;
}
.alert-heading {
  color: inherit;
}
.alert-link {
  font-weight: 700;
}
.alert-dismissible {
  padding-right: 4rem;
}
.alert-dismissible .close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 0.75rem 1.25rem;
  color: inherit;
}
.alert-primary {
  color: #1f4867;
  background-color: #d8e8f4;
  border-color: #c8dfef;
}
.alert-primary hr {
  border-top-color: #b4d4e9;
}
.alert-primary .alert-link {
  color: #132d40;
}
.alert-primary1 {
  color: #486173;
  background-color: #e8f1f8;
  border-color: #dfecf5;
}
.alert-primary1 hr {
  border-top-color: #cce0ef;
}
.alert-primary1 .alert-link {
  color: #344754;
}
.alert-primary2 {
  color: #122c3f;
  background-color: #d3dde4;
  border-color: #c1cfd9;
}
.alert-primary2 hr {
  border-top-color: #b1c3cf;
}
.alert-primary2 .alert-link {
  color: #071017;
}
.alert-secondary {
  color: #0c121c;
  background-color: #d1d3d7;
  border-color: #bec1c6;
}
.alert-secondary hr {
  border-top-color: #b0b4ba;
}
.alert-secondary .alert-link {
  color: #000;
}
.alert-secondary1 {
  color: #101723;
  background-color: #d2d5d9;
  border-color: #c0c4ca;
}
.alert-secondary1 hr {
  border-top-color: #b2b7be;
}
.alert-secondary1 .alert-link {
  color: #000;
}
.alert-secondary2 {
  color: #0a0f17;
  background-color: #d0d2d5;
  border-color: #bdc0c4;
}
.alert-secondary2 hr {
  border-top-color: #b0b3b8;
}
.alert-secondary2 .alert-link {
  color: #000;
}
.alert-major-color {
  color: #858585;
  background-color: #fff;
  border-color: #fff;
}
.alert-major-color hr {
  border-top-color: #f2f2f2;
}
.alert-major-color .alert-link {
  color: #6c6c6c;
}
.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}
.alert-success hr {
  border-top-color: #b1dfbb;
}
.alert-success .alert-link {
  color: #0b2e13;
}
.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}
.alert-info hr {
  border-top-color: #abdde5;
}
.alert-info .alert-link {
  color: #062c33;
}
.alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}
.alert-warning hr {
  border-top-color: #ffe8a1;
}
.alert-warning .alert-link {
  color: #533f03;
}
.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}
.alert-danger hr {
  border-top-color: #f1b0b7;
}
.alert-danger .alert-link {
  color: #491217;
}
.alert-light {
  color: #7b7b7b;
  background-color: #fbfbfb;
  border-color: #fafafa;
}
.alert-light hr {
  border-top-color: #ededed;
}
.alert-light .alert-link {
  color: #626262;
}
.alert-dark {
  color: #1b1b1b;
  background-color: #d6d6d6;
  border-color: #c6c6c6;
}
.alert-dark hr {
  border-top-color: #b9b9b9;
}
.alert-dark .alert-link {
  color: #020202;
}
@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}
.progress {
  display: flex;
  height: 1rem;
  overflow: hidden;
  line-height: 0;
  font-size: 0.75rem;
  background-color: #e9e9e9;
  border-radius: 0.5rem;
}
.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: var(--color-7);
  transition: width 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}
.progress-bar-striped {
  background-image: linear-gradient(
    45deg,
    rgb(255 255 255 / 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgb(255 255 255 / 0.15) 50%,
    rgb(255 255 255 / 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
}
.progress-bar-animated {
  animation: 1s linear infinite progress-bar-stripes;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    animation: none;
  }
}
.media {
  display: flex;
  align-items: flex-start;
}
.media-body {
  flex: 1;
}
.list-group {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: 0.5rem;
}
.list-group-item-action {
  width: 100%;
  color: #494949;
  text-align: inherit;
}
.list-group-item-action:focus,
.list-group-item-action:hover {
  z-index: 1;
  color: #494949;
  text-decoration: none;
  background-color: #ececec;
}
.list-group-item-action:active {
  color: var(--icol);
  background-color: #e9e9e9;
}
.list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  background-color: #fff;
  border: 1px solid rgb(0 0 0 / 0.125);
}
.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.list-group-item:last-child {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}
.list-group-item.disabled,
.list-group-item:disabled {
  color: #6c6c6c;
  pointer-events: none;
  background-color: #fff;
}
.list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: var(--color-7);
  border-color: var(--color-7);
}
.list-group-item + .list-group-item {
  border-top-width: 0;
}
.list-group-item + .list-group-item.active {
  margin-top: -1px;
  border-top-width: 1px;
}
.list-group-horizontal {
  flex-direction: row;
}
.list-group-horizontal > .list-group-item:first-child {
  border-bottom-left-radius: 0.5rem;
  border-top-right-radius: 0;
}
.list-group-horizontal > .list-group-item:last-child {
  border-top-right-radius: 0.5rem;
  border-bottom-left-radius: 0;
}
.list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item {
  border-top-width: 1px;
  border-left-width: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-left: -1px;
  border-left-width: 1px;
}
@media (min-width: 576px) {
  .list-group-horizontal-sm {
    flex-direction: row;
  }
  .list-group-horizontal-sm > .list-group-item:first-child {
    border-bottom-left-radius: 0.5rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 768px) {
  .list-group-horizontal-md {
    flex-direction: row;
  }
  .list-group-horizontal-md > .list-group-item:first-child {
    border-bottom-left-radius: 0.5rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    flex-direction: row;
  }
  .list-group-horizontal-lg > .list-group-item:first-child {
    border-bottom-left-radius: 0.5rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    flex-direction: row;
  }
  .list-group-horizontal-xl > .list-group-item:first-child {
    border-bottom-left-radius: 0.5rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 1400px) {
  .list-group-horizontal-xxl {
    flex-direction: row;
  }
  .list-group-horizontal-xxl > .list-group-item:first-child {
    border-bottom-left-radius: 0.5rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
.list-group-flush {
  border-radius: 0;
}
.list-group-flush > .list-group-item {
  border-width: 0 0 1px;
}
.list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}
.list-group-item-primary {
  color: #1f4867;
  background-color: #c8dfef;
}
.list-group-item-primary.list-group-item-action:focus,
.list-group-item-primary.list-group-item-action:hover {
  color: #1f4867;
  background-color: #b4d4e9;
}
.list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: #1f4867;
  border-color: #1f4867;
}
.list-group-item-primary1 {
  color: #486173;
  background-color: #dfecf5;
}
.list-group-item-primary1.list-group-item-action:focus,
.list-group-item-primary1.list-group-item-action:hover {
  color: #486173;
  background-color: #cce0ef;
}
.list-group-item-primary1.list-group-item-action.active {
  color: #fff;
  background-color: #486173;
  border-color: #486173;
}
.list-group-item-primary2 {
  color: #122c3f;
  background-color: #c1cfd9;
}
.list-group-item-primary2.list-group-item-action:focus,
.list-group-item-primary2.list-group-item-action:hover {
  color: #122c3f;
  background-color: #b1c3cf;
}
.list-group-item-primary2.list-group-item-action.active {
  color: #fff;
  background-color: #122c3f;
  border-color: #122c3f;
}
.list-group-item-secondary {
  color: #0c121c;
  background-color: #bec1c6;
}
.list-group-item-secondary.list-group-item-action:focus,
.list-group-item-secondary.list-group-item-action:hover {
  color: #0c121c;
  background-color: #b0b4ba;
}
.list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: #0c121c;
  border-color: #0c121c;
}
.list-group-item-secondary1 {
  color: #101723;
  background-color: #c0c4ca;
}
.list-group-item-secondary1.list-group-item-action:focus,
.list-group-item-secondary1.list-group-item-action:hover {
  color: #101723;
  background-color: #b2b7be;
}
.list-group-item-secondary1.list-group-item-action.active {
  color: #fff;
  background-color: #101723;
  border-color: #101723;
}
.list-group-item-secondary2 {
  color: #0a0f17;
  background-color: #bdc0c4;
}
.list-group-item-secondary2.list-group-item-action:focus,
.list-group-item-secondary2.list-group-item-action:hover {
  color: #0a0f17;
  background-color: #b0b3b8;
}
.list-group-item-secondary2.list-group-item-action.active {
  color: #fff;
  background-color: #0a0f17;
  border-color: #0a0f17;
}
.list-group-item-major-color {
  color: #858585;
  background-color: #fff;
}
.list-group-item-major-color.list-group-item-action:focus,
.list-group-item-major-color.list-group-item-action:hover {
  color: #858585;
  background-color: #f2f2f2;
}
.list-group-item-major-color.list-group-item-action.active {
  color: #fff;
  background-color: #858585;
  border-color: #858585;
}
.list-group-item-success {
  color: #155724;
  background-color: #c3e6cb;
}
.list-group-item-success.list-group-item-action:focus,
.list-group-item-success.list-group-item-action:hover {
  color: #155724;
  background-color: #b1dfbb;
}
.list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: #155724;
  border-color: #155724;
}
.list-group-item-info {
  color: #0c5460;
  background-color: #bee5eb;
}
.list-group-item-info.list-group-item-action:focus,
.list-group-item-info.list-group-item-action:hover {
  color: #0c5460;
  background-color: #abdde5;
}
.list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: #0c5460;
  border-color: #0c5460;
}
.list-group-item-warning {
  color: #856404;
  background-color: #ffeeba;
}
.list-group-item-warning.list-group-item-action:focus,
.list-group-item-warning.list-group-item-action:hover {
  color: #856404;
  background-color: #ffe8a1;
}
.list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: #856404;
  border-color: #856404;
}
.list-group-item-danger {
  color: #721c24;
  background-color: #f5c6cb;
}
.list-group-item-danger.list-group-item-action:focus,
.list-group-item-danger.list-group-item-action:hover {
  color: #721c24;
  background-color: #f1b0b7;
}
.list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: #721c24;
  border-color: #721c24;
}
.list-group-item-light {
  color: #7b7b7b;
  background-color: #fafafa;
}
.list-group-item-light.list-group-item-action:focus,
.list-group-item-light.list-group-item-action:hover {
  color: #7b7b7b;
  background-color: #ededed;
}
.list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: #7b7b7b;
  border-color: #7b7b7b;
}
.list-group-item-dark {
  color: #1b1b1b;
  background-color: #c6c6c6;
}
.list-group-item-dark.list-group-item-action:focus,
.list-group-item-dark.list-group-item-action:hover {
  color: #1b1b1b;
  background-color: #b9b9b9;
}
.list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: #1b1b1b;
  border-color: #1b1b1b;
}
.close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}
.close:hover {
  color: #000;
  text-decoration: none;
}
.close:not(:disabled):not(.disabled):focus,
.close:not(:disabled):not(.disabled):hover {
  opacity: 0.75;
}
button.close {
  padding: 0;
  background-color: #fff0;
  border: 0;
}
a.close.disabled {
  pointer-events: none;
}
.toast {
  flex-basis: 350px;
  max-width: 350px;
  font-size: 0.875rem;
  background-color: rgb(255 255 255 / 0.85);
  background-clip: padding-box;
  border: 1px solid rgb(0 0 0 / 0.1);
  box-shadow: 0 0.25rem 0.75rem rgb(0 0 0 / 0.1);
  opacity: 0;
  border-radius: 0.25rem;
}
.toast:not(:last-child) {
  margin-bottom: 0.75rem;
}
.toast.showing {
  opacity: 1;
}
.toast.show {
  display: block;
  opacity: 1;
}
.toast.hide {
  display: none;
}
.toast-header {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  color: #6c6c6c;
  background-color: rgb(255 255 255 / 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgb(0 0 0 / 0.05);
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.toast-body {
  padding: 0.75rem;
}
.modal-open {
  overflow: hidden;
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  display: none;
  height: 100%;
  overflow: hidden;
  outline: 0;
}
.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}
.modal-dialog-scrollable {
  display: flex;
  max-height: calc(100% - 1rem);
}
.modal-dialog-scrollable .modal-content {
  max-height: calc(100vh - 1rem);
  overflow: hidden;
}
.modal-dialog-scrollable .modal-footer,
.modal-dialog-scrollable .modal-header {
  flex-shrink: 0;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}
.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 1rem);
}
.modal-dialog-centered::before {
  display: block;
  height: calc(100vh - 1rem);
  height: -moz-min-content;
  height: min-content;
  content: "";
}
.modal-dialog-centered.modal-dialog-scrollable {
  flex-direction: column;
  justify-content: center;
  height: 100%;
}
.modal-dialog-centered.modal-dialog-scrollable .modal-content {
  max-height: none;
}
.modal-dialog-centered.modal-dialog-scrollable::before {
  content: none;
}
.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: var(--bgcards);
  background-clip: padding-box;

  border-radius: 0.5rem;
  outline: 0;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: 0.5;
}
.modal-header {
  display: flex;
  align-items: center;
  padding: 1rem 1rem;
  gap: 16px;
  border-bottom: 1px solid #404040;
}
.modal-header .close {
  padding: 1rem 1rem;
  margin: -1rem -1rem -1rem auto;
}
.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
  color: #838383;
}
.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}
.modal-footer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid #dedede;
  border-bottom-right-radius: calc(0.5rem - 1px);
  border-bottom-left-radius: calc(0.5rem - 1px);
}
.modal-footer > * {
  margin: 0.25rem;
}
.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}
@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }
  .modal-dialog-scrollable {
    max-height: calc(100% - 3.5rem);
  }
  .modal-dialog-scrollable .modal-content {
    max-height: calc(100vh - 3.5rem);
  }
  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }
  .modal-dialog-centered::before {
    height: calc(100vh - 3.5rem);
    height: -moz-min-content;
    height: min-content;
  }
  .modal-sm {
    max-width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    max-width: 800px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1140px;
  }
}
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: 0.9;
}
.tooltip .arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}
.tooltip .arrow::before {
  position: absolute;
  content: "";
  border-color: #fff0;
  border-style: solid;
}
.bs-tooltip-auto[x-placement^="top"],
.bs-tooltip-top {
  padding: 0.4rem 0;
}
.bs-tooltip-auto[x-placement^="top"] .arrow,
.bs-tooltip-top .arrow {
  bottom: 0;
}
.bs-tooltip-auto[x-placement^="top"] .arrow::before,
.bs-tooltip-top .arrow::before {
  top: 0;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #fff;
}
.bs-tooltip-auto[x-placement^="right"],
.bs-tooltip-right {
  padding: 0 0.4rem;
}
.bs-tooltip-auto[x-placement^="right"] .arrow,
.bs-tooltip-right .arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-auto[x-placement^="right"] .arrow::before,
.bs-tooltip-right .arrow::before {
  right: 0;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #fff;
}
.bs-tooltip-auto[x-placement^="bottom"],
.bs-tooltip-bottom {
  padding: 0.4rem 0;
}
.bs-tooltip-auto[x-placement^="bottom"] .arrow,
.bs-tooltip-bottom .arrow {
  top: 0;
}
.bs-tooltip-auto[x-placement^="bottom"] .arrow::before,
.bs-tooltip-bottom .arrow::before {
  bottom: 0;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #fff;
}
.bs-tooltip-auto[x-placement^="left"],
.bs-tooltip-left {
  padding: 0 0.4rem;
}
.bs-tooltip-auto[x-placement^="left"] .arrow,
.bs-tooltip-left .arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-auto[x-placement^="left"] .arrow::before,
.bs-tooltip-left .arrow::before {
  left: 0;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #fff;
}
.tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: #000;
  text-align: center;
  background-color: #fff;
  border-radius: 0.5rem;
}
.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgb(0 0 0 / 0.2);
  border-radius: 1rem;
}
.popover .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 1rem;
}
.popover .arrow::after,
.popover .arrow::before {
  position: absolute;
  display: block;
  content: "";
  border-color: #fff0;
  border-style: solid;
}
.bs-popover-auto[x-placement^="top"],
.bs-popover-top {
  margin-bottom: 0.5rem;
}
.bs-popover-auto[x-placement^="top"] > .arrow,
.bs-popover-top > .arrow {
  bottom: calc(-0.5rem - 1px);
}
.bs-popover-auto[x-placement^="top"] > .arrow::before,
.bs-popover-top > .arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgb(0 0 0 / 0.25);
}
.bs-popover-auto[x-placement^="top"] > .arrow::after,
.bs-popover-top > .arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}
.bs-popover-auto[x-placement^="right"],
.bs-popover-right {
  margin-left: 0.5rem;
}
.bs-popover-auto[x-placement^="right"] > .arrow,
.bs-popover-right > .arrow {
  left: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 1rem 0;
}
.bs-popover-auto[x-placement^="right"] > .arrow::before,
.bs-popover-right > .arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgb(0 0 0 / 0.25);
}
.bs-popover-auto[x-placement^="right"] > .arrow::after,
.bs-popover-right > .arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}
.bs-popover-auto[x-placement^="bottom"],
.bs-popover-bottom {
  margin-top: 0.5rem;
}
.bs-popover-auto[x-placement^="bottom"] > .arrow,
.bs-popover-bottom > .arrow {
  top: calc(-0.5rem - 1px);
}
.bs-popover-auto[x-placement^="bottom"] > .arrow::before,
.bs-popover-bottom > .arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgb(0 0 0 / 0.25);
}
.bs-popover-auto[x-placement^="bottom"] > .arrow::after,
.bs-popover-bottom > .arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}
.bs-popover-auto[x-placement^="bottom"] .popover-header::before,
.bs-popover-bottom .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid #f7f7f7;
}
.bs-popover-auto[x-placement^="left"],
.bs-popover-left {
  margin-right: 0.5rem;
}
.bs-popover-auto[x-placement^="left"] > .arrow,
.bs-popover-left > .arrow {
  right: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 1rem 0;
}
.bs-popover-auto[x-placement^="left"] > .arrow::before,
.bs-popover-left > .arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgb(0 0 0 / 0.25);
}
.bs-popover-auto[x-placement^="left"] > .arrow::after,
.bs-popover-left > .arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}
.popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-top-left-radius: calc(1rem - 1px);
  border-top-right-radius: calc(1rem - 1px);
}
.popover-header:empty {
  display: none;
}
.popover-body {
  padding: 0.5rem 0.75rem;
  color: var(--icol);
}
.carousel {
  position: relative;
}
.carousel.pointer-event {
  touch-action: pan-y;
}
.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}
.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}
.carousel-item-next,
.carousel-item-prev,
.carousel-item.active {
  display: block;
}
.active.carousel-item-right,
.carousel-item-next:not(.carousel-item-left) {
  transform: translateX(100%);
}
.active.carousel-item-left,
.carousel-item-prev:not(.carousel-item-right) {
  transform: translateX(-100%);
}
.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none;
}
.carousel-fade .carousel-item-next.carousel-item-left,
.carousel-fade .carousel-item-prev.carousel-item-right,
.carousel-fade .carousel-item.active {
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-right {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-left,
  .carousel-fade .active.carousel-item-right {
    transition: none;
  }
}
.carousel-control-next,
.carousel-control-prev {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  padding: 0;
  color: #fff;
  text-align: center;
  background: 0 0;
  border: 0;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-next,
  .carousel-control-prev {
    transition: none;
  }
}
.carousel-control-next:focus,
.carousel-control-next:hover,
.carousel-control-prev:focus,
.carousel-control-prev:hover {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}
.carousel-control-prev {
  left: 0;
}
.carousel-control-next {
  right: 0;
}
.carousel-control-next-icon,
.carousel-control-prev-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: 50%/100% 100% no-repeat;
}
.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
}
.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
}
.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 15;
  display: flex;
  justify-content: center;
  padding-left: 0;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none;
}
.carousel-indicators li {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border-top: 10px solid #fff0;
  border-bottom: 10px solid #fff0;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators li {
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}
.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
}
@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  border: 0.25em solid currentcolor;
  border-right-color: #fff0;
  border-radius: 50%;
  animation: 0.75s linear infinite spinner-border;
}
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}
@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}
.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  background-color: currentcolor;
  border-radius: 50%;
  opacity: 0;
  animation: 0.75s linear infinite spinner-grow;
}
.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}
@media (prefers-reduced-motion: reduce) {
  .spinner-border,
  .spinner-grow {
    animation-duration: 1.5s;
  }
}
.align-baseline {
  vertical-align: baseline !important;
}
.align-top {
  vertical-align: top !important;
}
.align-middle {
  vertical-align: middle !important;
}
.align-bottom {
  vertical-align: bottom !important;
}
.align-text-bottom {
  vertical-align: text-bottom !important;
}
.align-text-top {
  vertical-align: text-top !important;
}
.bg-primary {
  background-color: var(--color-7) !important;
}
a.bg-primary:focus,
a.bg-primary:hover,
button.bg-primary:focus,
button.bg-primary:hover {
  background-color: #2f70a0 !important;
}
.bg-primary1 {
  background-color: var(--color-10) !important;
}
a.bg-primary1:focus,
a.bg-primary1:hover,
button.bg-primary1:focus,
button.bg-primary1:hover {
  background-color: #ee4b5d !important;
}
.bg-primary2 {
  background-color: var(--color-6) !important;
}
a.bg-primary2:focus,
a.bg-primary2:hover,
button.bg-primary2:focus,
button.bg-primary2:hover {
  background-color: #183951 !important;
}
.bg-secondary {
  background-color: var(--bgcards) !important;
}
a.bg-secondary:focus,
a.bg-secondary:hover,
button.bg-secondary:focus,
button.bg-secondary:hover {
  background-color: #080c12 !important;
}
.bg-secondary1 {
  background-color: var(--secb) !important;
}
a.bg-secondary1:focus,
a.bg-secondary1:hover,
button.bg-secondary1:focus,
button.bg-secondary1:hover {
  background-color: #0e1520 !important;
}
.bg-secondary2 {
  background-color: var(--color-3) !important;
}
a.bg-secondary2:focus,
a.bg-secondary2:hover,
button.bg-secondary2:focus,
button.bg-secondary2:hover {
  background-color: #040609 !important;
}
.bg-major-color {
  background-color: #fff !important;
}
a.bg-major-color:focus,
a.bg-major-color:hover,
button.bg-major-color:focus,
button.bg-major-color:hover {
  background-color: #e6e6e6 !important;
}
.bg-success {
  background-color: #28a745 !important;
}
a.bg-success:focus,
a.bg-success:hover,
button.bg-success:focus,
button.bg-success:hover {
  background-color: #1e7e34 !important;
}
.bg-info {
  background-color: #17a2b8 !important;
}
a.bg-info:focus,
a.bg-info:hover,
button.bg-info:focus,
button.bg-info:hover {
  background-color: #117a8b !important;
}
.bg-warning {
  background-color: #ffc107 !important;
}
a.bg-warning:focus,
a.bg-warning:hover,
button.bg-warning:focus,
button.bg-warning:hover {
  background-color: #d39e00 !important;
}
.bg-danger {
  background-color: #dc3545 !important;
}
a.bg-danger:focus,
a.bg-danger:hover,
button.bg-danger:focus,
button.bg-danger:hover {
  background-color: #bd2130 !important;
}
.bg-light {
  background-color: #ececec !important;
}
a.bg-light:focus,
a.bg-light:hover,
button.bg-light:focus,
button.bg-light:hover {
  background-color: #d3d3d3 !important;
}
.bg-dark {
  background-color: #343434 !important;
}
a.bg-dark:focus,
a.bg-dark:hover,
button.bg-dark:focus,
button.bg-dark:hover {
  background-color: #1b1b1b !important;
}
.bg-white {
  background-color: #fff !important;
}
.bg-transparent {
  background-color: transparent !important;
}
.border {
  border: 1px solid #dedede !important;
}
.border-top {
  border-top: 1px solid #dedede !important;
}
.border-right {
  border-right: 1px solid #dedede !important;
}
.border-bottom {
  border-bottom: 1px solid #dedede !important;
}
.border-left {
  border-left: 1px solid #dedede !important;
}
.border-0 {
  border: 0 !important;
}
.border-top-0 {
  border-top: 0 !important;
}
.border-right-0 {
  border-right: 0 !important;
}
.border-bottom-0 {
  border-bottom: 0 !important;
}
.border-left-0 {
  border-left: 0 !important;
}
.border-primary {
  border-color: var(--color-7) !important;
}
.border-primary1 {
  border-color: var(--color-10) !important;
}
.border-primary2 {
  border-color: var(--color-6) !important;
}
.border-secondary {
  border-color: var(--bgcards) !important;
}
.border-secondary1 {
  border-color: var(--secb) !important;
}
.border-secondary2 {
  border-color: var(--color-3) !important;
}
.border-major-color {
  border-color: #fff !important;
}
.border-success {
  border-color: #28a745 !important;
}
.border-info {
  border-color: #17a2b8 !important;
}
.border-warning {
  border-color: #ffc107 !important;
}
.border-danger {
  border-color: #dc3545 !important;
}
.border-light {
  border-color: #ececec !important;
}
.border-dark {
  border-color: #343434 !important;
}
.border-white {
  border-color: #fff !important;
}
.rounded-sm {
  border-radius: 0.3rem !important;
}
.rounded {
  border-radius: 0.5rem !important;
}
.rounded-top {
  border-top-left-radius: 0.5rem !important;
  border-top-right-radius: 0.5rem !important;
}
.rounded-right {
  border-top-right-radius: 0.5rem !important;
  border-bottom-right-radius: 0.5rem !important;
}
.rounded-bottom {
  border-bottom-right-radius: 0.5rem !important;
  border-bottom-left-radius: 0.5rem !important;
}
.rounded-left {
  border-top-left-radius: 0.5rem !important;
  border-bottom-left-radius: 0.5rem !important;
}
.rounded-lg {
  border-radius: 1rem !important;
}
.rounded-circle {
  border-radius: 50% !important;
}
.rounded-pill {
  border-radius: 50rem !important;
}
.rounded-0 {
  border-radius: 0 !important;
}
.clearfix::after,
.original::after {
  display: block;
  clear: both;
  content: "";
}
.d-none {
  display: none !important;
}
.d-inline {
  display: inline !important;
}
.d-inline-block {
  display: inline-block !important;
}
.d-block {
  display: block !important;
}
.d-table {
  display: table !important;
}
.d-table-row {
  display: table-row !important;
}
.d-table-cell {
  display: table-cell !important;
}
.d-flex {
  display: flex !important;
}
.d-inline-flex {
  display: inline-flex !important;
}
@media (min-width: 576px) {
  .d-sm-none {
    display: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 1400px) {
  .d-xxl-none {
    display: none !important;
  }
  .d-xxl-inline {
    display: inline !important;
  }
  .d-xxl-inline-block {
    display: inline-block !important;
  }
  .d-xxl-block {
    display: block !important;
  }
  .d-xxl-table {
    display: table !important;
  }
  .d-xxl-table-row {
    display: table-row !important;
  }
  .d-xxl-table-cell {
    display: table-cell !important;
  }
  .d-xxl-flex {
    display: flex !important;
  }
  .d-xxl-inline-flex {
    display: inline-flex !important;
  }
}
@media print {
  .d-print-none {
    display: none !important;
  }
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
}
.embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}
.embed-responsive::before {
  display: block;
  content: "";
}
.embed-responsive .embed-responsive-item,
.embed-responsive embed,
.embed-responsive iframe,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}
.embed-responsive-21by9::before {
  padding-top: 42.85714286%;
}
.embed-responsive-16by9::before {
  padding-top: 56.25%;
}
.embed-responsive-4by3::before {
  padding-top: 75%;
}
.embed-responsive-1by1::before {
  padding-top: 100%;
}
.flex-row {
  flex-direction: row !important;
}
.flex-column {
  flex-direction: column !important;
}
.flex-row-reverse {
  flex-direction: row-reverse !important;
}
.flex-column-reverse {
  flex-direction: column-reverse !important;
}
.flex-wrap {
  flex-wrap: wrap !important;
}
.flex-nowrap {
  flex-wrap: nowrap !important;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}
.flex-fill {
  flex: 1 1 auto !important;
}
.flex-grow-0 {
  flex-grow: 0 !important;
}
.flex-grow-1 {
  flex-grow: 1 !important;
}
.flex-shrink-0 {
  flex-shrink: 0 !important;
}
.flex-shrink-1 {
  flex-shrink: 1 !important;
}
.justify-content-start {
  justify-content: flex-start !important;
}
.justify-content-end {
  justify-content: flex-end !important;
}
.justify-content-center {
  justify-content: center !important;
}
.justify-content-between {
  justify-content: space-between !important;
}
.justify-content-around {
  justify-content: space-around !important;
}
.align-items-start {
  align-items: flex-start !important;
}
.align-items-end {
  align-items: flex-end !important;
}
.align-items-center {
  align-items: center !important;
}
.align-items-baseline {
  align-items: baseline !important;
}
.align-items-stretch {
  align-items: stretch !important;
}
.align-content-start {
  align-content: flex-start !important;
}
.align-content-end {
  align-content: flex-end !important;
}
.align-content-center {
  align-content: center !important;
}
.align-content-between {
  align-content: space-between !important;
}
.align-content-around {
  align-content: space-around !important;
}
.align-content-stretch {
  align-content: stretch !important;
}
.align-self-auto {
  align-self: auto !important;
}
.align-self-start {
  align-self: flex-start !important;
}
.align-self-end {
  align-self: flex-end !important;
}
.align-self-center {
  align-self: center !important;
}
.align-self-baseline {
  align-self: baseline !important;
}
.align-self-stretch {
  align-self: stretch !important;
}
@media (min-width: 576px) {
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 768px) {
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 992px) {
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 1200px) {
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 1400px) {
  .flex-xxl-row {
    flex-direction: row !important;
  }
  .flex-xxl-column {
    flex-direction: column !important;
  }
  .flex-xxl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xxl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xxl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xxl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-xxl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xxl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xxl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xxl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xxl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-xxl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xxl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xxl-center {
    justify-content: center !important;
  }
  .justify-content-xxl-between {
    justify-content: space-between !important;
  }
  .justify-content-xxl-around {
    justify-content: space-around !important;
  }
  .align-items-xxl-start {
    align-items: flex-start !important;
  }
  .align-items-xxl-end {
    align-items: flex-end !important;
  }
  .align-items-xxl-center {
    align-items: center !important;
  }
  .align-items-xxl-baseline {
    align-items: baseline !important;
  }
  .align-items-xxl-stretch {
    align-items: stretch !important;
  }
  .align-content-xxl-start {
    align-content: flex-start !important;
  }
  .align-content-xxl-end {
    align-content: flex-end !important;
  }
  .align-content-xxl-center {
    align-content: center !important;
  }
  .align-content-xxl-between {
    align-content: space-between !important;
  }
  .align-content-xxl-around {
    align-content: space-around !important;
  }
  .align-content-xxl-stretch {
    align-content: stretch !important;
  }
  .align-self-xxl-auto {
    align-self: auto !important;
  }
  .align-self-xxl-start {
    align-self: flex-start !important;
  }
  .align-self-xxl-end {
    align-self: flex-end !important;
  }
  .align-self-xxl-center {
    align-self: center !important;
  }
  .align-self-xxl-baseline {
    align-self: baseline !important;
  }
  .align-self-xxl-stretch {
    align-self: stretch !important;
  }
}
.float-left {
  float: left !important;
}
.float-right {
  float: right !important;
}
.float-none {
  float: none !important;
}
@media (min-width: 576px) {
  .float-sm-left {
    float: left !important;
  }
  .float-sm-right {
    float: right !important;
  }
  .float-sm-none {
    float: none !important;
  }
}
@media (min-width: 768px) {
  .float-md-left {
    float: left !important;
  }
  .float-md-right {
    float: right !important;
  }
  .float-md-none {
    float: none !important;
  }
}
@media (min-width: 992px) {
  .float-lg-left {
    float: left !important;
  }
  .float-lg-right {
    float: right !important;
  }
  .float-lg-none {
    float: none !important;
  }
}
@media (min-width: 1200px) {
  .float-xl-left {
    float: left !important;
  }
  .float-xl-right {
    float: right !important;
  }
  .float-xl-none {
    float: none !important;
  }
}
@media (min-width: 1400px) {
  .float-xxl-left {
    float: left !important;
  }
  .float-xxl-right {
    float: right !important;
  }
  .float-xxl-none {
    float: none !important;
  }
}
.user-select-all {
  -webkit-user-select: all !important;
  -moz-user-select: all !important;
  user-select: all !important;
}
.user-select-auto {
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  user-select: auto !important;
}
.user-select-none {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  user-select: none !important;
}
.overflow-auto {
  overflow: auto !important;
}
.overflow-hidden {
  overflow: hidden !important;
}
.position-static {
  position: static !important;
}
.position-relative {
  position: relative !important;
}
.position-absolute {
  position: absolute !important;
}
.position-fixed {
  position: fixed !important;
}
.position-sticky {
  position: sticky !important;
}
.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}
.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}
@supports (position: sticky) {
  .sticky-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgb(0 0 0 / 0.075) !important;
}
.shadow {
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 0.15) !important;
}
.shadow-lg {
  box-shadow: 0 1rem 3rem rgb(0 0 0 / 0.175) !important;
}
.shadow-none {
  box-shadow: none !important;
}
.w-25 {
  width: 25% !important;
}
.w-50 {
  width: 50% !important;
}
.w-75 {
  width: 75% !important;
}
.w-100 {
  width: 100% !important;
}
.w-auto {
  width: auto !important;
}
.h-25 {
  height: 25% !important;
}
.h-50 {
  height: 50% !important;
}
.h-75 {
  height: 75% !important;
}
.h-100 {
  height: 100% !important;
}
.h-auto {
  height: auto !important;
}
.mw-100 {
  max-width: 100% !important;
}
.mh-100 {
  max-height: 100% !important;
}
.min-vw-100 {
  min-width: 100vw !important;
}
.min-vh-100 {
  min-height: 100vh !important;
}
.vw-100 {
  width: 100vw !important;
}
.vh-100 {
  height: 100vh !important;
}
.m-0 {
  margin: 0 !important;
}
.mt-0,
.my-0 {
  margin-top: 0 !important;
}
.mr-0,
.mx-0 {
  margin-right: 0 !important;
}
.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}
.ml-0,
.mx-0 {
  margin-left: 0 !important;
}
.m-1 {
  margin: 0.25rem !important;
}
.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}
.mr-1,
.mx-1 {
  margin-right: 0.25rem !important;
}
.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important;
}
.ml-1,
.mx-1 {
  margin-left: 0.25rem !important;
}
.m-2 {
  margin: 0.5rem !important;
}
.mt-2,
.my-2 {
  margin-top: 0.5rem !important;
}
.mr-2,
.mx-2 {
  margin-right: 0.5rem !important;
}
.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}
.ml-2,
.mx-2 {
  margin-left: 0.5rem !important;
}
.m-3 {
  margin: 1rem !important;
}
.mt-3,
.my-3 {
  margin-top: 1rem !important;
}
.mr-3,
.mx-3 {
  margin-right: 1rem !important;
}
.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}
.ml-3,
.mx-3 {
  margin-left: 1rem !important;
}
.m-4 {
  margin: 1.5rem !important;
}
.mt-4,
.my-4 {
  margin-top: 1.5rem !important;
}
.mr-4,
.mx-4 {
  margin-right: 1.5rem !important;
}
.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important;
}
.ml-4,
.mx-4 {
  margin-left: 1.5rem !important;
}
.m-5 {
  margin: 3rem !important;
}
.mt-5,
.my-5 {
  margin-top: 3rem !important;
}
.mr-5,
.mx-5 {
  margin-right: 3rem !important;
}
.mb-5,
.my-5 {
  margin-bottom: 3rem !important;
}
.ml-5,
.mx-5 {
  margin-left: 3rem !important;
}
.p-0 {
  padding: 0 !important;
}
.pt-0,
.py-0 {
  padding-top: 0 !important;
}
.pr-0,
.px-0 {
  padding-right: 0 !important;
}
.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}
.pl-0,
.px-0 {
  padding-left: 0 !important;
}
.p-1 {
  padding: 0.25rem !important;
}
.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}
.pr-1,
.px-1 {
  padding-right: 0.25rem !important;
}
.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important;
}
.pl-1,
.px-1 {
  padding-left: 0.25rem !important;
}
.p-2 {
  padding: 0.5rem !important;
}
.pt-2,
.py-2 {
  padding-top: 0.5rem !important;
}
.pr-2,
.px-2 {
  padding-right: 0.5rem !important;
}
.pb-2,
.py-2 {
  padding-bottom: 0.5rem !important;
}
.pl-2,
.px-2 {
  padding-left: 0.5rem !important;
}
.p-3 {
  padding: 1rem !important;
}
.pt-3,
.py-3 {
  padding-top: 1rem !important;
}
.pr-3,
.px-3 {
  padding-right: 1rem !important;
}
.pb-3,
.py-3 {
  padding-bottom: 1rem !important;
}
.pl-3,
.px-3 {
  padding-left: 1rem !important;
}
.p-4 {
  padding: 1.5rem !important;
}
.pt-4,
.py-4 {
  padding-top: 1.5rem !important;
}
.pr-4,
.px-4 {
  padding-right: 1.5rem !important;
}
.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important;
}
.pl-4,
.px-4 {
  padding-left: 1.5rem !important;
}
.p-5 {
  padding: 3rem !important;
}
.pt-5,
.py-5 {
  padding-top: 3rem !important;
}
.pr-5,
.px-5 {
  padding-right: 3rem !important;
}
.pb-5,
.py-5 {
  padding-bottom: 3rem !important;
}
.pl-5,
.px-5 {
  padding-left: 3rem !important;
}
.m-n1 {
  margin: -0.25rem !important;
}
.mt-n1,
.my-n1 {
  margin-top: -0.25rem !important;
}
.mr-n1,
.mx-n1 {
  margin-right: -0.25rem !important;
}
.mb-n1,
.my-n1 {
  margin-bottom: -0.25rem !important;
}
.ml-n1,
.mx-n1 {
  margin-left: -0.25rem !important;
}
.m-n2 {
  margin: -0.5rem !important;
}
.mt-n2,
.my-n2 {
  margin-top: -0.5rem !important;
}
.mr-n2,
.mx-n2 {
  margin-right: -0.5rem !important;
}
.mb-n2,
.my-n2 {
  margin-bottom: -0.5rem !important;
}
.ml-n2,
.mx-n2 {
  margin-left: -0.5rem !important;
}
.m-n3 {
  margin: -1rem !important;
}
.mt-n3,
.my-n3 {
  margin-top: -1rem !important;
}
.mr-n3,
.mx-n3 {
  margin-right: -1rem !important;
}
.mb-n3,
.my-n3 {
  margin-bottom: -1rem !important;
}
.ml-n3,
.mx-n3 {
  margin-left: -1rem !important;
}
.m-n4 {
  margin: -1.5rem !important;
}
.mt-n4,
.my-n4 {
  margin-top: -1.5rem !important;
}
.mr-n4,
.mx-n4 {
  margin-right: -1.5rem !important;
}
.mb-n4,
.my-n4 {
  margin-bottom: -1.5rem !important;
}
.ml-n4,
.mx-n4 {
  margin-left: -1.5rem !important;
}
.m-n5 {
  margin: -3rem !important;
}
.mt-n5,
.my-n5 {
  margin-top: -3rem !important;
}
.mr-n5,
.mx-n5 {
  margin-right: -3rem !important;
}
.mb-n5,
.my-n5 {
  margin-bottom: -3rem !important;
}
.ml-n5,
.mx-n5 {
  margin-left: -3rem !important;
}
.m-auto {
  margin: auto !important;
}
.mt-auto,
.my-auto {
  margin-top: auto !important;
}
.mr-auto,
.mx-auto {
  margin-right: auto !important;
}
.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}
.ml-auto,
.mx-auto {
  margin-left: auto !important;
}
@media (min-width: 576px) {
  .m-sm-0 {
    margin: 0 !important;
  }
  .mt-sm-0,
  .my-sm-0 {
    margin-top: 0 !important;
  }
  .mr-sm-0,
  .mx-sm-0 {
    margin-right: 0 !important;
  }
  .mb-sm-0,
  .my-sm-0 {
    margin-bottom: 0 !important;
  }
  .ml-sm-0,
  .mx-sm-0 {
    margin-left: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .mt-sm-1,
  .my-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mr-sm-1,
  .mx-sm-1 {
    margin-right: 0.25rem !important;
  }
  .mb-sm-1,
  .my-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-sm-1,
  .mx-sm-1 {
    margin-left: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.5rem !important;
  }
  .mt-sm-2,
  .my-sm-2 {
    margin-top: 0.5rem !important;
  }
  .mr-sm-2,
  .mx-sm-2 {
    margin-right: 0.5rem !important;
  }
  .mb-sm-2,
  .my-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-sm-2,
  .mx-sm-2 {
    margin-left: 0.5rem !important;
  }
  .m-sm-3 {
    margin: 1rem !important;
  }
  .mt-sm-3,
  .my-sm-3 {
    margin-top: 1rem !important;
  }
  .mr-sm-3,
  .mx-sm-3 {
    margin-right: 1rem !important;
  }
  .mb-sm-3,
  .my-sm-3 {
    margin-bottom: 1rem !important;
  }
  .ml-sm-3,
  .mx-sm-3 {
    margin-left: 1rem !important;
  }
  .m-sm-4 {
    margin: 1.5rem !important;
  }
  .mt-sm-4,
  .my-sm-4 {
    margin-top: 1.5rem !important;
  }
  .mr-sm-4,
  .mx-sm-4 {
    margin-right: 1.5rem !important;
  }
  .mb-sm-4,
  .my-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-sm-4,
  .mx-sm-4 {
    margin-left: 1.5rem !important;
  }
  .m-sm-5 {
    margin: 3rem !important;
  }
  .mt-sm-5,
  .my-sm-5 {
    margin-top: 3rem !important;
  }
  .mr-sm-5,
  .mx-sm-5 {
    margin-right: 3rem !important;
  }
  .mb-sm-5,
  .my-sm-5 {
    margin-bottom: 3rem !important;
  }
  .ml-sm-5,
  .mx-sm-5 {
    margin-left: 3rem !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .pt-sm-0,
  .py-sm-0 {
    padding-top: 0 !important;
  }
  .pr-sm-0,
  .px-sm-0 {
    padding-right: 0 !important;
  }
  .pb-sm-0,
  .py-sm-0 {
    padding-bottom: 0 !important;
  }
  .pl-sm-0,
  .px-sm-0 {
    padding-left: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .pt-sm-1,
  .py-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pr-sm-1,
  .px-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pb-sm-1,
  .py-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-sm-1,
  .px-sm-1 {
    padding-left: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.5rem !important;
  }
  .pt-sm-2,
  .py-sm-2 {
    padding-top: 0.5rem !important;
  }
  .pr-sm-2,
  .px-sm-2 {
    padding-right: 0.5rem !important;
  }
  .pb-sm-2,
  .py-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-sm-2,
  .px-sm-2 {
    padding-left: 0.5rem !important;
  }
  .p-sm-3 {
    padding: 1rem !important;
  }
  .pt-sm-3,
  .py-sm-3 {
    padding-top: 1rem !important;
  }
  .pr-sm-3,
  .px-sm-3 {
    padding-right: 1rem !important;
  }
  .pb-sm-3,
  .py-sm-3 {
    padding-bottom: 1rem !important;
  }
  .pl-sm-3,
  .px-sm-3 {
    padding-left: 1rem !important;
  }
  .p-sm-4 {
    padding: 1.5rem !important;
  }
  .pt-sm-4,
  .py-sm-4 {
    padding-top: 1.5rem !important;
  }
  .pr-sm-4,
  .px-sm-4 {
    padding-right: 1.5rem !important;
  }
  .pb-sm-4,
  .py-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-sm-4,
  .px-sm-4 {
    padding-left: 1.5rem !important;
  }
  .p-sm-5 {
    padding: 3rem !important;
  }
  .pt-sm-5,
  .py-sm-5 {
    padding-top: 3rem !important;
  }
  .pr-sm-5,
  .px-sm-5 {
    padding-right: 3rem !important;
  }
  .pb-sm-5,
  .py-sm-5 {
    padding-bottom: 3rem !important;
  }
  .pl-sm-5,
  .px-sm-5 {
    padding-left: 3rem !important;
  }
  .m-sm-n1 {
    margin: -0.25rem !important;
  }
  .mt-sm-n1,
  .my-sm-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-sm-n1,
  .mx-sm-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-sm-n1,
  .my-sm-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-sm-n1,
  .mx-sm-n1 {
    margin-left: -0.25rem !important;
  }
  .m-sm-n2 {
    margin: -0.5rem !important;
  }
  .mt-sm-n2,
  .my-sm-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-sm-n2,
  .mx-sm-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-sm-n2,
  .my-sm-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-sm-n2,
  .mx-sm-n2 {
    margin-left: -0.5rem !important;
  }
  .m-sm-n3 {
    margin: -1rem !important;
  }
  .mt-sm-n3,
  .my-sm-n3 {
    margin-top: -1rem !important;
  }
  .mr-sm-n3,
  .mx-sm-n3 {
    margin-right: -1rem !important;
  }
  .mb-sm-n3,
  .my-sm-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-sm-n3,
  .mx-sm-n3 {
    margin-left: -1rem !important;
  }
  .m-sm-n4 {
    margin: -1.5rem !important;
  }
  .mt-sm-n4,
  .my-sm-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-sm-n4,
  .mx-sm-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-sm-n4,
  .my-sm-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-sm-n4,
  .mx-sm-n4 {
    margin-left: -1.5rem !important;
  }
  .m-sm-n5 {
    margin: -3rem !important;
  }
  .mt-sm-n5,
  .my-sm-n5 {
    margin-top: -3rem !important;
  }
  .mr-sm-n5,
  .mx-sm-n5 {
    margin-right: -3rem !important;
  }
  .mb-sm-n5,
  .my-sm-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-sm-n5,
  .mx-sm-n5 {
    margin-left: -3rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mt-sm-auto,
  .my-sm-auto {
    margin-top: auto !important;
  }
  .mr-sm-auto,
  .mx-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-auto,
  .my-sm-auto {
    margin-bottom: auto !important;
  }
  .ml-sm-auto,
  .mx-sm-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 768px) {
  .m-md-0 {
    margin: 0 !important;
  }
  .mt-md-0,
  .my-md-0 {
    margin-top: 0 !important;
  }
  .mr-md-0,
  .mx-md-0 {
    margin-right: 0 !important;
  }
  .mb-md-0,
  .my-md-0 {
    margin-bottom: 0 !important;
  }
  .ml-md-0,
  .mx-md-0 {
    margin-left: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .mt-md-1,
  .my-md-1 {
    margin-top: 0.25rem !important;
  }
  .mr-md-1,
  .mx-md-1 {
    margin-right: 0.25rem !important;
  }
  .mb-md-1,
  .my-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-md-1,
  .mx-md-1 {
    margin-left: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.5rem !important;
  }
  .mt-md-2,
  .my-md-2 {
    margin-top: 0.5rem !important;
  }
  .mr-md-2,
  .mx-md-2 {
    margin-right: 0.5rem !important;
  }
  .mb-md-2,
  .my-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-md-2,
  .mx-md-2 {
    margin-left: 0.5rem !important;
  }
  .m-md-3 {
    margin: 1rem !important;
  }
  .mt-md-3,
  .my-md-3 {
    margin-top: 1rem !important;
  }
  .mr-md-3,
  .mx-md-3 {
    margin-right: 1rem !important;
  }
  .mb-md-3,
  .my-md-3 {
    margin-bottom: 1rem !important;
  }
  .ml-md-3,
  .mx-md-3 {
    margin-left: 1rem !important;
  }
  .m-md-4 {
    margin: 1.5rem !important;
  }
  .mt-md-4,
  .my-md-4 {
    margin-top: 1.5rem !important;
  }
  .mr-md-4,
  .mx-md-4 {
    margin-right: 1.5rem !important;
  }
  .mb-md-4,
  .my-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-md-4,
  .mx-md-4 {
    margin-left: 1.5rem !important;
  }
  .m-md-5 {
    margin: 3rem !important;
  }
  .mt-md-5,
  .my-md-5 {
    margin-top: 3rem !important;
  }
  .mr-md-5,
  .mx-md-5 {
    margin-right: 3rem !important;
  }
  .mb-md-5,
  .my-md-5 {
    margin-bottom: 3rem !important;
  }
  .ml-md-5,
  .mx-md-5 {
    margin-left: 3rem !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .pt-md-0,
  .py-md-0 {
    padding-top: 0 !important;
  }
  .pr-md-0,
  .px-md-0 {
    padding-right: 0 !important;
  }
  .pb-md-0,
  .py-md-0 {
    padding-bottom: 0 !important;
  }
  .pl-md-0,
  .px-md-0 {
    padding-left: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .pt-md-1,
  .py-md-1 {
    padding-top: 0.25rem !important;
  }
  .pr-md-1,
  .px-md-1 {
    padding-right: 0.25rem !important;
  }
  .pb-md-1,
  .py-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-md-1,
  .px-md-1 {
    padding-left: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.5rem !important;
  }
  .pt-md-2,
  .py-md-2 {
    padding-top: 0.5rem !important;
  }
  .pr-md-2,
  .px-md-2 {
    padding-right: 0.5rem !important;
  }
  .pb-md-2,
  .py-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-md-2,
  .px-md-2 {
    padding-left: 0.5rem !important;
  }
  .p-md-3 {
    padding: 1rem !important;
  }
  .pt-md-3,
  .py-md-3 {
    padding-top: 1rem !important;
  }
  .pr-md-3,
  .px-md-3 {
    padding-right: 1rem !important;
  }
  .pb-md-3,
  .py-md-3 {
    padding-bottom: 1rem !important;
  }
  .pl-md-3,
  .px-md-3 {
    padding-left: 1rem !important;
  }
  .p-md-4 {
    padding: 1.5rem !important;
  }
  .pt-md-4,
  .py-md-4 {
    padding-top: 1.5rem !important;
  }
  .pr-md-4,
  .px-md-4 {
    padding-right: 1.5rem !important;
  }
  .pb-md-4,
  .py-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-md-4,
  .px-md-4 {
    padding-left: 1.5rem !important;
  }
  .p-md-5 {
    padding: 3rem !important;
  }
  .pt-md-5,
  .py-md-5 {
    padding-top: 3rem !important;
  }
  .pr-md-5,
  .px-md-5 {
    padding-right: 3rem !important;
  }
  .pb-md-5,
  .py-md-5 {
    padding-bottom: 3rem !important;
  }
  .pl-md-5,
  .px-md-5 {
    padding-left: 3rem !important;
  }
  .m-md-n1 {
    margin: -0.25rem !important;
  }
  .mt-md-n1,
  .my-md-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-md-n1,
  .mx-md-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-md-n1,
  .my-md-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-md-n1,
  .mx-md-n1 {
    margin-left: -0.25rem !important;
  }
  .m-md-n2 {
    margin: -0.5rem !important;
  }
  .mt-md-n2,
  .my-md-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-md-n2,
  .mx-md-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-md-n2,
  .my-md-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-md-n2,
  .mx-md-n2 {
    margin-left: -0.5rem !important;
  }
  .m-md-n3 {
    margin: -1rem !important;
  }
  .mt-md-n3,
  .my-md-n3 {
    margin-top: -1rem !important;
  }
  .mr-md-n3,
  .mx-md-n3 {
    margin-right: -1rem !important;
  }
  .mb-md-n3,
  .my-md-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-md-n3,
  .mx-md-n3 {
    margin-left: -1rem !important;
  }
  .m-md-n4 {
    margin: -1.5rem !important;
  }
  .mt-md-n4,
  .my-md-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-md-n4,
  .mx-md-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-md-n4,
  .my-md-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-md-n4,
  .mx-md-n4 {
    margin-left: -1.5rem !important;
  }
  .m-md-n5 {
    margin: -3rem !important;
  }
  .mt-md-n5,
  .my-md-n5 {
    margin-top: -3rem !important;
  }
  .mr-md-n5,
  .mx-md-n5 {
    margin-right: -3rem !important;
  }
  .mb-md-n5,
  .my-md-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-md-n5,
  .mx-md-n5 {
    margin-left: -3rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mt-md-auto,
  .my-md-auto {
    margin-top: auto !important;
  }
  .mr-md-auto,
  .mx-md-auto {
    margin-right: auto !important;
  }
  .mb-md-auto,
  .my-md-auto {
    margin-bottom: auto !important;
  }
  .ml-md-auto,
  .mx-md-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 992px) {
  .m-lg-0 {
    margin: 0 !important;
  }
  .mt-lg-0,
  .my-lg-0 {
    margin-top: 0 !important;
  }
  .mr-lg-0,
  .mx-lg-0 {
    margin-right: 0 !important;
  }
  .mb-lg-0,
  .my-lg-0 {
    margin-bottom: 0 !important;
  }
  .ml-lg-0,
  .mx-lg-0 {
    margin-left: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .mt-lg-1,
  .my-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mr-lg-1,
  .mx-lg-1 {
    margin-right: 0.25rem !important;
  }
  .mb-lg-1,
  .my-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-lg-1,
  .mx-lg-1 {
    margin-left: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.5rem !important;
  }
  .mt-lg-2,
  .my-lg-2 {
    margin-top: 0.5rem !important;
  }
  .mr-lg-2,
  .mx-lg-2 {
    margin-right: 0.5rem !important;
  }
  .mb-lg-2,
  .my-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-lg-2,
  .mx-lg-2 {
    margin-left: 0.5rem !important;
  }
  .m-lg-3 {
    margin: 1rem !important;
  }
  .mt-lg-3,
  .my-lg-3 {
    margin-top: 1rem !important;
  }
  .mr-lg-3,
  .mx-lg-3 {
    margin-right: 1rem !important;
  }
  .mb-lg-3,
  .my-lg-3 {
    margin-bottom: 1rem !important;
  }
  .ml-lg-3,
  .mx-lg-3 {
    margin-left: 1rem !important;
  }
  .m-lg-4 {
    margin: 1.5rem !important;
  }
  .mt-lg-4,
  .my-lg-4 {
    margin-top: 1.5rem !important;
  }
  .mr-lg-4,
  .mx-lg-4 {
    margin-right: 1.5rem !important;
  }
  .mb-lg-4,
  .my-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-lg-4,
  .mx-lg-4 {
    margin-left: 1.5rem !important;
  }
  .m-lg-5 {
    margin: 3rem !important;
  }
  .mt-lg-5,
  .my-lg-5 {
    margin-top: 3rem !important;
  }
  .mr-lg-5,
  .mx-lg-5 {
    margin-right: 3rem !important;
  }
  .mb-lg-5,
  .my-lg-5 {
    margin-bottom: 3rem !important;
  }
  .ml-lg-5,
  .mx-lg-5 {
    margin-left: 3rem !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .pt-lg-0,
  .py-lg-0 {
    padding-top: 0 !important;
  }
  .pr-lg-0,
  .px-lg-0 {
    padding-right: 0 !important;
  }
  .pb-lg-0,
  .py-lg-0 {
    padding-bottom: 0 !important;
  }
  .pl-lg-0,
  .px-lg-0 {
    padding-left: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .pt-lg-1,
  .py-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pr-lg-1,
  .px-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pb-lg-1,
  .py-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-lg-1,
  .px-lg-1 {
    padding-left: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.5rem !important;
  }
  .pt-lg-2,
  .py-lg-2 {
    padding-top: 0.5rem !important;
  }
  .pr-lg-2,
  .px-lg-2 {
    padding-right: 0.5rem !important;
  }
  .pb-lg-2,
  .py-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-lg-2,
  .px-lg-2 {
    padding-left: 0.5rem !important;
  }
  .p-lg-3 {
    padding: 1rem !important;
  }
  .pt-lg-3,
  .py-lg-3 {
    padding-top: 1rem !important;
  }
  .pr-lg-3,
  .px-lg-3 {
    padding-right: 1rem !important;
  }
  .pb-lg-3,
  .py-lg-3 {
    padding-bottom: 1rem !important;
  }
  .pl-lg-3,
  .px-lg-3 {
    padding-left: 1rem !important;
  }
  .p-lg-4 {
    padding: 1.5rem !important;
  }
  .pt-lg-4,
  .py-lg-4 {
    padding-top: 1.5rem !important;
  }
  .pr-lg-4,
  .px-lg-4 {
    padding-right: 1.5rem !important;
  }
  .pb-lg-4,
  .py-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-lg-4,
  .px-lg-4 {
    padding-left: 1.5rem !important;
  }
  .p-lg-5 {
    padding: 3rem !important;
  }
  .pt-lg-5,
  .py-lg-5 {
    padding-top: 3rem !important;
  }
  .pr-lg-5,
  .px-lg-5 {
    padding-right: 3rem !important;
  }
  .pb-lg-5,
  .py-lg-5 {
    padding-bottom: 3rem !important;
  }
  .pl-lg-5,
  .px-lg-5 {
    padding-left: 3rem !important;
  }
  .m-lg-n1 {
    margin: -0.25rem !important;
  }
  .mt-lg-n1,
  .my-lg-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-lg-n1,
  .mx-lg-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-lg-n1,
  .my-lg-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-lg-n1,
  .mx-lg-n1 {
    margin-left: -0.25rem !important;
  }
  .m-lg-n2 {
    margin: -0.5rem !important;
  }
  .mt-lg-n2,
  .my-lg-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-lg-n2,
  .mx-lg-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-lg-n2,
  .my-lg-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-lg-n2,
  .mx-lg-n2 {
    margin-left: -0.5rem !important;
  }
  .m-lg-n3 {
    margin: -1rem !important;
  }
  .mt-lg-n3,
  .my-lg-n3 {
    margin-top: -1rem !important;
  }
  .mr-lg-n3,
  .mx-lg-n3 {
    margin-right: -1rem !important;
  }
  .mb-lg-n3,
  .my-lg-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-lg-n3,
  .mx-lg-n3 {
    margin-left: -1rem !important;
  }
  .m-lg-n4 {
    margin: -1.5rem !important;
  }
  .mt-lg-n4,
  .my-lg-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-lg-n4,
  .mx-lg-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-lg-n4,
  .my-lg-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-lg-n4,
  .mx-lg-n4 {
    margin-left: -1.5rem !important;
  }
  .m-lg-n5 {
    margin: -3rem !important;
  }
  .mt-lg-n5,
  .my-lg-n5 {
    margin-top: -3rem !important;
  }
  .mr-lg-n5,
  .mx-lg-n5 {
    margin-right: -3rem !important;
  }
  .mb-lg-n5,
  .my-lg-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-lg-n5,
  .mx-lg-n5 {
    margin-left: -3rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mt-lg-auto,
  .my-lg-auto {
    margin-top: auto !important;
  }
  .mr-lg-auto,
  .mx-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-auto,
  .my-lg-auto {
    margin-bottom: auto !important;
  }
  .ml-lg-auto,
  .mx-lg-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 1200px) {
  .m-xl-0 {
    margin: 0 !important;
  }
  .mt-xl-0,
  .my-xl-0 {
    margin-top: 0 !important;
  }
  .mr-xl-0,
  .mx-xl-0 {
    margin-right: 0 !important;
  }
  .mb-xl-0,
  .my-xl-0 {
    margin-bottom: 0 !important;
  }
  .ml-xl-0,
  .mx-xl-0 {
    margin-left: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .mt-xl-1,
  .my-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mr-xl-1,
  .mx-xl-1 {
    margin-right: 0.25rem !important;
  }
  .mb-xl-1,
  .my-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-xl-1,
  .mx-xl-1 {
    margin-left: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.5rem !important;
  }
  .mt-xl-2,
  .my-xl-2 {
    margin-top: 0.5rem !important;
  }
  .mr-xl-2,
  .mx-xl-2 {
    margin-right: 0.5rem !important;
  }
  .mb-xl-2,
  .my-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-xl-2,
  .mx-xl-2 {
    margin-left: 0.5rem !important;
  }
  .m-xl-3 {
    margin: 1rem !important;
  }
  .mt-xl-3,
  .my-xl-3 {
    margin-top: 1rem !important;
  }
  .mr-xl-3,
  .mx-xl-3 {
    margin-right: 1rem !important;
  }
  .mb-xl-3,
  .my-xl-3 {
    margin-bottom: 1rem !important;
  }
  .ml-xl-3,
  .mx-xl-3 {
    margin-left: 1rem !important;
  }
  .m-xl-4 {
    margin: 1.5rem !important;
  }
  .mt-xl-4,
  .my-xl-4 {
    margin-top: 1.5rem !important;
  }
  .mr-xl-4,
  .mx-xl-4 {
    margin-right: 1.5rem !important;
  }
  .mb-xl-4,
  .my-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-xl-4,
  .mx-xl-4 {
    margin-left: 1.5rem !important;
  }
  .m-xl-5 {
    margin: 3rem !important;
  }
  .mt-xl-5,
  .my-xl-5 {
    margin-top: 3rem !important;
  }
  .mr-xl-5,
  .mx-xl-5 {
    margin-right: 3rem !important;
  }
  .mb-xl-5,
  .my-xl-5 {
    margin-bottom: 3rem !important;
  }
  .ml-xl-5,
  .mx-xl-5 {
    margin-left: 3rem !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .pt-xl-0,
  .py-xl-0 {
    padding-top: 0 !important;
  }
  .pr-xl-0,
  .px-xl-0 {
    padding-right: 0 !important;
  }
  .pb-xl-0,
  .py-xl-0 {
    padding-bottom: 0 !important;
  }
  .pl-xl-0,
  .px-xl-0 {
    padding-left: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .pt-xl-1,
  .py-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pr-xl-1,
  .px-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pb-xl-1,
  .py-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-xl-1,
  .px-xl-1 {
    padding-left: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.5rem !important;
  }
  .pt-xl-2,
  .py-xl-2 {
    padding-top: 0.5rem !important;
  }
  .pr-xl-2,
  .px-xl-2 {
    padding-right: 0.5rem !important;
  }
  .pb-xl-2,
  .py-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-xl-2,
  .px-xl-2 {
    padding-left: 0.5rem !important;
  }
  .p-xl-3 {
    padding: 1rem !important;
  }
  .pt-xl-3,
  .py-xl-3 {
    padding-top: 1rem !important;
  }
  .pr-xl-3,
  .px-xl-3 {
    padding-right: 1rem !important;
  }
  .pb-xl-3,
  .py-xl-3 {
    padding-bottom: 1rem !important;
  }
  .pl-xl-3,
  .px-xl-3 {
    padding-left: 1rem !important;
  }
  .p-xl-4 {
    padding: 1.5rem !important;
  }
  .pt-xl-4,
  .py-xl-4 {
    padding-top: 1.5rem !important;
  }
  .pr-xl-4,
  .px-xl-4 {
    padding-right: 1.5rem !important;
  }
  .pb-xl-4,
  .py-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-xl-4,
  .px-xl-4 {
    padding-left: 1.5rem !important;
  }
  .p-xl-5 {
    padding: 3rem !important;
  }
  .pt-xl-5,
  .py-xl-5 {
    padding-top: 3rem !important;
  }
  .pr-xl-5,
  .px-xl-5 {
    padding-right: 3rem !important;
  }
  .pb-xl-5,
  .py-xl-5 {
    padding-bottom: 3rem !important;
  }
  .pl-xl-5,
  .px-xl-5 {
    padding-left: 3rem !important;
  }
  .m-xl-n1 {
    margin: -0.25rem !important;
  }
  .mt-xl-n1,
  .my-xl-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-xl-n1,
  .mx-xl-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-xl-n1,
  .my-xl-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-xl-n1,
  .mx-xl-n1 {
    margin-left: -0.25rem !important;
  }
  .m-xl-n2 {
    margin: -0.5rem !important;
  }
  .mt-xl-n2,
  .my-xl-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-xl-n2,
  .mx-xl-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-xl-n2,
  .my-xl-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-xl-n2,
  .mx-xl-n2 {
    margin-left: -0.5rem !important;
  }
  .m-xl-n3 {
    margin: -1rem !important;
  }
  .mt-xl-n3,
  .my-xl-n3 {
    margin-top: -1rem !important;
  }
  .mr-xl-n3,
  .mx-xl-n3 {
    margin-right: -1rem !important;
  }
  .mb-xl-n3,
  .my-xl-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-xl-n3,
  .mx-xl-n3 {
    margin-left: -1rem !important;
  }
  .m-xl-n4 {
    margin: -1.5rem !important;
  }
  .mt-xl-n4,
  .my-xl-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-xl-n4,
  .mx-xl-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-xl-n4,
  .my-xl-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-xl-n4,
  .mx-xl-n4 {
    margin-left: -1.5rem !important;
  }
  .m-xl-n5 {
    margin: -3rem !important;
  }
  .mt-xl-n5,
  .my-xl-n5 {
    margin-top: -3rem !important;
  }
  .mr-xl-n5,
  .mx-xl-n5 {
    margin-right: -3rem !important;
  }
  .mb-xl-n5,
  .my-xl-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-xl-n5,
  .mx-xl-n5 {
    margin-left: -3rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mt-xl-auto,
  .my-xl-auto {
    margin-top: auto !important;
  }
  .mr-xl-auto,
  .mx-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-auto,
  .my-xl-auto {
    margin-bottom: auto !important;
  }
  .ml-xl-auto,
  .mx-xl-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 1400px) {
  .m-xxl-0 {
    margin: 0 !important;
  }
  .mt-xxl-0,
  .my-xxl-0 {
    margin-top: 0 !important;
  }
  .mr-xxl-0,
  .mx-xxl-0 {
    margin-right: 0 !important;
  }
  .mb-xxl-0,
  .my-xxl-0 {
    margin-bottom: 0 !important;
  }
  .ml-xxl-0,
  .mx-xxl-0 {
    margin-left: 0 !important;
  }
  .m-xxl-1 {
    margin: 0.25rem !important;
  }
  .mt-xxl-1,
  .my-xxl-1 {
    margin-top: 0.25rem !important;
  }
  .mr-xxl-1,
  .mx-xxl-1 {
    margin-right: 0.25rem !important;
  }
  .mb-xxl-1,
  .my-xxl-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-xxl-1,
  .mx-xxl-1 {
    margin-left: 0.25rem !important;
  }
  .m-xxl-2 {
    margin: 0.5rem !important;
  }
  .mt-xxl-2,
  .my-xxl-2 {
    margin-top: 0.5rem !important;
  }
  .mr-xxl-2,
  .mx-xxl-2 {
    margin-right: 0.5rem !important;
  }
  .mb-xxl-2,
  .my-xxl-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-xxl-2,
  .mx-xxl-2 {
    margin-left: 0.5rem !important;
  }
  .m-xxl-3 {
    margin: 1rem !important;
  }
  .mt-xxl-3,
  .my-xxl-3 {
    margin-top: 1rem !important;
  }
  .mr-xxl-3,
  .mx-xxl-3 {
    margin-right: 1rem !important;
  }
  .mb-xxl-3,
  .my-xxl-3 {
    margin-bottom: 1rem !important;
  }
  .ml-xxl-3,
  .mx-xxl-3 {
    margin-left: 1rem !important;
  }
  .m-xxl-4 {
    margin: 1.5rem !important;
  }
  .mt-xxl-4,
  .my-xxl-4 {
    margin-top: 1.5rem !important;
  }
  .mr-xxl-4,
  .mx-xxl-4 {
    margin-right: 1.5rem !important;
  }
  .mb-xxl-4,
  .my-xxl-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-xxl-4,
  .mx-xxl-4 {
    margin-left: 1.5rem !important;
  }
  .m-xxl-5 {
    margin: 3rem !important;
  }
  .mt-xxl-5,
  .my-xxl-5 {
    margin-top: 3rem !important;
  }
  .mr-xxl-5,
  .mx-xxl-5 {
    margin-right: 3rem !important;
  }
  .mb-xxl-5,
  .my-xxl-5 {
    margin-bottom: 3rem !important;
  }
  .ml-xxl-5,
  .mx-xxl-5 {
    margin-left: 3rem !important;
  }
  .p-xxl-0 {
    padding: 0 !important;
  }
  .pt-xxl-0,
  .py-xxl-0 {
    padding-top: 0 !important;
  }
  .pr-xxl-0,
  .px-xxl-0 {
    padding-right: 0 !important;
  }
  .pb-xxl-0,
  .py-xxl-0 {
    padding-bottom: 0 !important;
  }
  .pl-xxl-0,
  .px-xxl-0 {
    padding-left: 0 !important;
  }
  .p-xxl-1 {
    padding: 0.25rem !important;
  }
  .pt-xxl-1,
  .py-xxl-1 {
    padding-top: 0.25rem !important;
  }
  .pr-xxl-1,
  .px-xxl-1 {
    padding-right: 0.25rem !important;
  }
  .pb-xxl-1,
  .py-xxl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-xxl-1,
  .px-xxl-1 {
    padding-left: 0.25rem !important;
  }
  .p-xxl-2 {
    padding: 0.5rem !important;
  }
  .pt-xxl-2,
  .py-xxl-2 {
    padding-top: 0.5rem !important;
  }
  .pr-xxl-2,
  .px-xxl-2 {
    padding-right: 0.5rem !important;
  }
  .pb-xxl-2,
  .py-xxl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-xxl-2,
  .px-xxl-2 {
    padding-left: 0.5rem !important;
  }
  .p-xxl-3 {
    padding: 1rem !important;
  }
  .pt-xxl-3,
  .py-xxl-3 {
    padding-top: 1rem !important;
  }
  .pr-xxl-3,
  .px-xxl-3 {
    padding-right: 1rem !important;
  }
  .pb-xxl-3,
  .py-xxl-3 {
    padding-bottom: 1rem !important;
  }
  .pl-xxl-3,
  .px-xxl-3 {
    padding-left: 1rem !important;
  }
  .p-xxl-4 {
    padding: 1.5rem !important;
  }
  .pt-xxl-4,
  .py-xxl-4 {
    padding-top: 1.5rem !important;
  }
  .pr-xxl-4,
  .px-xxl-4 {
    padding-right: 1.5rem !important;
  }
  .pb-xxl-4,
  .py-xxl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-xxl-4,
  .px-xxl-4 {
    padding-left: 1.5rem !important;
  }
  .p-xxl-5 {
    padding: 3rem !important;
  }
  .pt-xxl-5,
  .py-xxl-5 {
    padding-top: 3rem !important;
  }
  .pr-xxl-5,
  .px-xxl-5 {
    padding-right: 3rem !important;
  }
  .pb-xxl-5,
  .py-xxl-5 {
    padding-bottom: 3rem !important;
  }
  .pl-xxl-5,
  .px-xxl-5 {
    padding-left: 3rem !important;
  }
  .m-xxl-n1 {
    margin: -0.25rem !important;
  }
  .mt-xxl-n1,
  .my-xxl-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-xxl-n1,
  .mx-xxl-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-xxl-n1,
  .my-xxl-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-xxl-n1,
  .mx-xxl-n1 {
    margin-left: -0.25rem !important;
  }
  .m-xxl-n2 {
    margin: -0.5rem !important;
  }
  .mt-xxl-n2,
  .my-xxl-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-xxl-n2,
  .mx-xxl-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-xxl-n2,
  .my-xxl-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-xxl-n2,
  .mx-xxl-n2 {
    margin-left: -0.5rem !important;
  }
  .m-xxl-n3 {
    margin: -1rem !important;
  }
  .mt-xxl-n3,
  .my-xxl-n3 {
    margin-top: -1rem !important;
  }
  .mr-xxl-n3,
  .mx-xxl-n3 {
    margin-right: -1rem !important;
  }
  .mb-xxl-n3,
  .my-xxl-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-xxl-n3,
  .mx-xxl-n3 {
    margin-left: -1rem !important;
  }
  .m-xxl-n4 {
    margin: -1.5rem !important;
  }
  .mt-xxl-n4,
  .my-xxl-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-xxl-n4,
  .mx-xxl-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-xxl-n4,
  .my-xxl-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-xxl-n4,
  .mx-xxl-n4 {
    margin-left: -1.5rem !important;
  }
  .m-xxl-n5 {
    margin: -3rem !important;
  }
  .mt-xxl-n5,
  .my-xxl-n5 {
    margin-top: -3rem !important;
  }
  .mr-xxl-n5,
  .mx-xxl-n5 {
    margin-right: -3rem !important;
  }
  .mb-xxl-n5,
  .my-xxl-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-xxl-n5,
  .mx-xxl-n5 {
    margin-left: -3rem !important;
  }
  .m-xxl-auto {
    margin: auto !important;
  }
  .mt-xxl-auto,
  .my-xxl-auto {
    margin-top: auto !important;
  }
  .mr-xxl-auto,
  .mx-xxl-auto {
    margin-right: auto !important;
  }
  .mb-xxl-auto,
  .my-xxl-auto {
    margin-bottom: auto !important;
  }
  .ml-xxl-auto,
  .mx-xxl-auto {
    margin-left: auto !important;
  }
}
.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  pointer-events: auto;
  content: "";
  background-color: #fff0;
}
.text-monospace {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace !important;
}
.text-justify {
  text-align: justify !important;
}
.text-wrap {
  white-space: normal !important;
}
.text-nowrap {
  white-space: nowrap !important;
}
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-left {
  text-align: left !important;
}
.text-right {
  text-align: right !important;
}
.text-center {
  text-align: center !important;
}
@media (min-width: 576px) {
  .text-sm-left {
    text-align: left !important;
  }
  .text-sm-right {
    text-align: right !important;
  }
  .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .text-md-left {
    text-align: left !important;
  }
  .text-md-right {
    text-align: right !important;
  }
  .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .text-lg-left {
    text-align: left !important;
  }
  .text-lg-right {
    text-align: right !important;
  }
  .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .text-xl-left {
    text-align: left !important;
  }
  .text-xl-right {
    text-align: right !important;
  }
  .text-xl-center {
    text-align: center !important;
  }
}
@media (min-width: 1400px) {
  .text-xxl-left {
    text-align: left !important;
  }
  .text-xxl-right {
    text-align: right !important;
  }
  .text-xxl-center {
    text-align: center !important;
  }
}
.text-lowercase {
  text-transform: lowercase !important;
}
.text-uppercase {
  text-transform: uppercase !important;
}
.text-capitalize {
  text-transform: capitalize !important;
}
.font-weight-light {
  font-weight: 300 !important;
}
.font-weight-lighter {
  font-weight: lighter !important;
}
.font-weight-normal {
  font-weight: 400 !important;
}
.font-weight-bold {
  font-weight: 700 !important;
}
.font-weight-bolder {
  font-weight: bolder !important;
}
.font-italic {
  font-style: italic !important;
}
.text-white {
  color: #fff !important;
}
.text-primary {
  color: var(--color-7) !important;
}
a.text-primary:focus,
a.text-primary:hover {
  color: #29628c !important;
}
.text-primary1 {
  color: var(--color-10) !important;
}
a.text-primary1:focus,
a.text-primary1:hover {
  color: #5097cc !important;
}
.text-primary2 {
  color: var(--color-6) !important;
}
a.text-primary2:focus,
a.text-primary2:hover {
  color: #122b3e !important;
}
.text-secondary {
  color: var(--bgcards) !important;
}
a.text-secondary:focus,
a.text-secondary:hover {
  color: #000 !important;
}
.text-secondary1 {
  color: var(--secb) !important;
}
a.text-secondary1:focus,
a.text-secondary1:hover {
  color: #070a0e !important;
}
.text-secondary2 {
  color: var(--color-3) !important;
}
a.text-secondary2:focus,
a.text-secondary2:hover {
  color: #000 !important;
}
.text-major-color {
  color: #fff !important;
}
a.text-major-color:focus,
a.text-major-color:hover {
  color: #d9d9d9 !important;
}
.text-success {
  color: #28a745 !important;
}
a.text-success:focus,
a.text-success:hover {
  color: #19692c !important;
}
.text-info {
  color: #17a2b8 !important;
}
a.text-info:focus,
a.text-info:hover {
  color: #0f6674 !important;
}
.text-warning {
  color: #ffc107 !important;
}
a.text-warning:focus,
a.text-warning:hover {
  color: #ba8b00 !important;
}
.text-danger {
  color: #dc3545 !important;
}
a.text-danger:focus,
a.text-danger:hover {
  color: #a71d2a !important;
}
.text-light {
  color: #ececec !important;
}
a.text-light:focus,
a.text-light:hover {
  color: #c6c6c6 !important;
}
.text-dark {
  color: #343434 !important;
}
a.text-dark:focus,
a.text-dark:hover {
  color: #0e0e0e !important;
}
.text-body {
  color: var(--icol) !important;
}
.text-muted {
  color: var(--pt) !important;
}
.text-black-50 {
  color: rgb(0 0 0 / 0.5) !important;
}
.text-white-50 {
  color: rgb(255 255 255 / 0.5) !important;
}
.text-hide {
  font: 0/0 a;
  color: #fff0;
  text-shadow: none;
  background-color: #fff0;
  border: 0;
}
.text-decoration-none {
  text-decoration: none !important;
}
.text-break {
  word-break: break-word !important;
  word-wrap: break-word !important;
}
.text-reset {
  color: inherit !important;
}
.visible {
  visibility: visible !important;
}
.invisible {
  visibility: hidden !important;
}
@media print {
  *,
  ::after,
  ::before {
    text-shadow: none !important;
    box-shadow: none !important;
  }
  a:not(.btn) {
    text-decoration: underline;
  }
  abbr[title]::after {
    content: " (" attr(title) ")";
  }
  pre {
    white-space: pre-wrap !important;
  }
  blockquote,
  pre {
    border: 1px solid #ababab;
    page-break-inside: avoid;
  }
  img,
  tr {
    page-break-inside: avoid;
  }
  h2,
  h3,
  p {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  @page {
    size: a3;
  }
  body {
    min-width: 992px !important;
  }
  .container {
    min-width: 992px !important;
  }
  .navbar {
    display: none;
  }
  .badge {
    border: 1px solid #000;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table td,
  .table th {
    background-color: #fff !important;
  }
  .table-bordered td,
  .table-bordered th {
    border: 1px solid #dedede !important;
  }
  .table-dark {
    color: inherit;
  }
  .table-dark tbody + tbody,
  .table-dark td,
  .table-dark th,
  .table-dark thead th {
    border-color: #dedede;
  }
  .table .thead-dark th {
    color: inherit;
    border-color: #dedede;
  }
}
body,
html {
  font-size: 14px;
  font-family: Poppins, sans-serif;
  height: 100%;
}
body > .wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}
.navigation {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  overflow: auto;
}
.navigation::-webkit-scrollbar-track {
  background: 0 0;
}
.navigation::-webkit-scrollbar-thumb {
  background-color: #fff0;
}
.navigation::-webkit-scrollbar {
  background: 0 0;
}
.major-color {
  color: #fff;
}
#toast {
  position: fixed;
  bottom: 0;
  right: 1rem;
  z-index: 99;
  border-radius: 5px;
}
input:-webkit-autofill,
input:-webkit-autofill:focus {
  -webkit-transition: background-color 600000s 0s, color 600000s 0s;
  transition: background-color 600000s 0s, color 600000s 0s;
}
.dot + .dot:before {
  content: "";
  display: inline-block;
  width: 0.1rem;
  height: 0.1rem;
  background: var(--pt);
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 0.45rem;
  margin-left: 0.3rem;
}
.loading {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.loading:before {
  content: "";
  width: 3rem;
  height: 3rem;
  margin: 0.5rem auto;
  border-radius: 50%;
  display: block;
  position: relative;
  border: 10px solid;
  border-color: rgb(60 139 198 / 0.15) rgb(60 139 198 / 0.25)
    rgb(60 139 198 / 0.35) rgb(60 139 198 / 0.5);
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}
@keyframes rotation {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
body,
html {
  scrollbar-color: #474747 #070c13;
}
body > span.bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: block;
  bottom: 0;
  background-image: url(../images/body-bg.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  z-index: -1;
  opacity: 0.5;
}
::-webkit-scrollbar-track {
  background: #070c13;
}
::-webkit-scrollbar {
  width: 10px;
}
::-webkit-scrollbar-thumb {
  background: #474747;
  width: 10px;
}
a,
button {
  transition: all 0.3s ease-in-out;
}
b {
  font-weight: 500;
}
.text-title {
  text-transform: capitalize;
}
.headernav-btn {
  height: 100%;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 2.3rem;
  color: var(--color-11);
  padding: 0;
}
.headernav-btn i {
  font-size: 1.4rem;
}
.headernav-btn.notify:after {
  background: var(--color-7);
  content: "";
  display: inline-flex;
  position: absolute;
  border-radius: 50%;
  width: 0.3rem;
  height: 0.3rem;
  right: 1rem;
  top: 0.35rem;
  transition: background 0.2s;
}
.headernav-btn:hover {
  color: var(--color-7);
}
.headernav-btn:hover.notify:after {
  background: var(--color-10);
}
main {
  flex-grow: 1;
  position: relative;
  background: radial-gradient(
    150% 150% at 50% 20%,
    transparent 50%,
    var(--color-10) 100%,
    var(--color-6) 0
  );
  padding-bottom: 1rem;
}
main .main-inner {
  display: flex;
}
main .main-inner .content,
main .main-inner .sidebar {
  display: flex;
  flex-direction: column;
}
main .main-inner .content {
  flex-grow: 1;
}
main .main-inner .sidebar {
  width: 24%;
  min-width: 22rem;
  flex-shrink: 0;
}
main .main-inner.swap .content {
  margin-right: 0;
  margin-left: 1.7rem;
}
main .main-inner.swap .sidebar {
  width: 15%;
  min-width: 16rem;
}
header {
  padding: 0;
  border-bottom: 1px solid var(--secb);
  z-index: 88;
  position: relative;
}
header .component {
  display: flex;
  align-items: center;
  height: 4.3rem;
}
header.abs {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  background: 0 0;
}
header .logo {
  margin-right: 1rem;
}
.btn-major {
  position: relative;
  background: 0 0;
  border-color: rgb(60 139 198 / 0.5);
  color: var(--color-11);
  border-radius: 50rem;
}
.btn-major:before {
  background: linear-gradient(180deg, #fff0 0, rgb(35 84 121 / 0.1) 100%),
    rgb(60 139 198 / 0.05);
  box-shadow: inset 0 0 12px var(--color-6);
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  border-radius: 50rem;
  transition: box-shadow 0.3s;
}
.btn-major:hover {
  border-color: #5097cc;
  color: #fff;
}
.btn-major:hover:before {
  box-shadow: inset 0 0 12px var(--color-7);
}
.dropdown .dropdown-menu,
.dropup .dropdown-menu {
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 0.175);
}
.dropdown .dropdown-menu .dropdown-item,
.dropup .dropdown-menu .dropdown-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dropdown.width-limit .dropdown-menu,
.dropup.width-limit .dropdown-menu {
  max-width: 10rem;
}
.dropdown.height-limit .dropdown-menu,
.dropup.height-limit .dropdown-menu {
  max-height: 13rem;
  overflow-y: auto;
}
.logo {
  position: relative;
  display: inline-block;
}
.logo:after {
  position: absolute;
  content: "v2";
  color: var(--color-7);
  top: 0;
  right: -0.8rem;
  font-weight: 700;
  font-size: 0.6rem;
}
.logo img {
  height: 2.6rem;
}
.container {
  max-width: 1444px;
}
.container.min {
  max-width: 600px;
}
.container.med {
  max-width: 1000px;
}
.max-sm {
  max-width: 500px;
}
.modal-dialog .modal-content .modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  width: 2rem;
  height: 2rem;
  background: var(--secb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s, background 0.3s;
  cursor: pointer;
}
.modal-dialog .modal-content .modal-close:hover {
  background: var(--color-7);
  color: var(--bgcards);
}
#nav-menu-btn {
  display: none;
  padding-left: 0;
  padding-right: 1rem;
  width: unset;
}
#nav-menu {
  z-index: 9999;
}
#nav-menu > ul {
  margin: 0;
  padding: 0.4rem 0;
  list-style: none;
  display: flex;
  flex-grow: 1;
}
#nav-menu > ul > li {
  position: relative;
}
#nav-menu > ul > li.down > a:after {
  content: "\f282";
  font-family: "Font Awesome 6 Pro";
  margin-left: 0.2rem;
  height: 0.9rem;
  transition: transform 0.3s;
  font-size: 0.7rem;
  color: var(--pt);
}
#nav-menu > ul > li.down:hover > a {
  color: var(--color-6);
}
#nav-menu > ul > li.down:hover > a:after {
  transform: rotate(-180deg);
}
#nav-menu > ul > li.down:hover > ul {
  display: block;
}
#nav-menu > ul > li > a {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  font-size: 1.05rem;
  gap: 6px;
  border-radius: 8px;
  color: #878787;
}
#nav-menu > ul > li > a.active,
#nav-menu > ul > li > a:hover {
  color: var(--color-10);
}
#nav-menu > ul > li:hover > a {
  color: #fff;
  background: #1a1a1a;
}
#nav-menu > ul > li:hover > ul {
  display: block;
}
#nav-menu > ul > li > ul {
  position: absolute;
  border-radius: 0.5rem;
  margin: 0 0 0;
  list-style: none;
  display: none;
  background: var(--bgcards);
  z-index: 9999;
  right: 0;
  top: 100%;
  overflow: hidden;
  padding: 0.8rem;
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 0.175);
  width: 32rem;
  left: 0;
}
#nav-menu > ul > li > ul.c1 {
  width: 12rem;
}
#nav-menu > ul > li > ul.c1 > li {
  width: 100%;
}
#nav-menu > ul > li > ul > li {
  float: left;
  width: 33.33%;
}
#nav-menu > ul > li > ul > li > a {
  display: block;
  padding: 0.3rem 0.8rem;
  color: var(--icol);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border: 1px solid #fff0;
}
#nav-menu > ul > li > ul > li > a .active,
#nav-menu > ul > li > ul > li > a:hover {
  color: var(--color-10);
  background: var(--secb);
  border-radius: 0.5rem;
  border: 1px solid #48484845;
}
#nav-search-btn {
  margin-left: auto;
  border-radius: 30px;
}
#nav-search {
  flex-grow: 1;
  margin: 0 1rem;
}
#nav-search .search-inner {
  position: relative;
}
#nav-search .search-inner form {
  display: flex;
  align-items: center;
  z-index: 2;
  height: 2.6rem;
  position: relative;
  background: var(--bgcards);
  border-radius: 50rem;
  padding-left: 0.8rem;
  transition: box-shadow 0.4s;
}
#nav-search .search-inner form button,
#nav-search .search-inner form input {
  background: 0 0;
  border: none;
  outline: unset;
}
#nav-search .search-inner form input {
  flex-grow: 1;
  color: #fff;
}
#nav-search .search-inner form input::-moz-placeholder {
  color: var(--pt);
}
#nav-search .search-inner form input::placeholder {
  color: var(--pt);
}
#nav-search .search-inner form > a {
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  height: 1.85rem;
  border-radius: 50rem;
  margin-right: 0.3rem;
}
#nav-search .search-inner form > a > span {
  margin-left: 0.2rem;
}
#nav-search .search-inner .suggestion {
  display: none;
  top: 100%;
  width: 100%;
  position: absolute;
  background: var(--bgcards);
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 0.175);
  overflow: hidden;
  padding: 1rem 0 0;
  margin-top: -1rem;
  z-index: 1;
  border-radius: 0 0 0.5rem 0.5rem;
}
#nav-search .search-inner .suggestion > div:last-child {
  padding: 1rem;
}
.nav-btn {
  font-size: 1.3rem;
  display: flex;
  padding: 0;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
}
.nav-btn.new {
  position: relative;
}
.nav-btn.new:after {
  content: "";
  position: absolute;
  background: var(--color-7);
  border-radius: 50rem;
  display: block;
  width: 0.37rem;
  height: 0.37rem;
  top: 0.6rem;
  right: 0.6rem;
  transition: background 0.3s;
}
.nav-btn.new:hover:after {
  background: var(--color-10);
}
.nav-btn:hover {
  color: #fff;
}
.nav-user {
  display: flex;
}
.nav-user .u-notify .dropdown-menu {
  width: 320px;
}
.nav-user .u-notify .dropdown-menu .head {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.nav-user .u-notify .dropdown-menu .foot,
.nav-user .u-notify .dropdown-menu .head {
  padding: 1rem;
}
.nav-user .u-menu .dropdown-menu .dropdown-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.4rem;
  color: var(--icol);
  display: block;
}
.nav-user .u-menu .dropdown-menu .dropdown-item:nth-child(odd) {
  background: rgb(0 0 0 / 0.1);
}
.nav-user .u-menu .dropdown-menu .dropdown-item:nth-child(odd):hover {
  background: var(--secb);
}
.nav-user .u-menu .dropdown-menu .dropdown-item i {
  font-size: 1.1rem;
  width: 1.6rem;
}
.nav-user .u-menu .dropdown-menu .dropdown-item:hover {
  color: #fff;
}
section {
  margin-bottom: 2.5rem;
}
section .head {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}
section .head h1 {
  color: #fff;
}
section .head h2 {
  font-size: 1.65rem;
  display: flex;
  align-items: center;
  margin: 0;
  color: rgb(255 255 255 / 0.8);
}
section .head h2 span {
  margin-left: 0.4rem;
}
article .heading,
article .sub-heading {
  color: var(--color-11);
}
.tabs {
  display: flex;
  align-items: center;
}
.tabs .tab {
  cursor: pointer;
  transition: color 0.3s;
  font-size: 1.05rem;
  gap: 6px;
}
.tabs .tab + div {
  margin-left: 1rem;
}
.tabs .tab:hover {
  color: #fff;
}
.tabs .tab.active {
  color: var(--color-10);
}
.tabs .s-pagi {
  display: flex;
}
.tabs .s-pagi > div {
  cursor: pointer;
  transition: color 0.3s;
}
.tabs .s-pagi > div:hover {
  color: #fff;
}
.tabs .s-pagi > div + div {
  margin-left: 0.5rem;
}
.s-pagi.bottom .btns {
  display: flex;
  margin: 0 -0.5rem;
}
.s-pagi.bottom .btns .btn {
  margin: 0.5rem;
  background: var(--bgcards);
  flex-grow: 1;
  justify-content: center;
}
.poster {
  flex-shrink: 0;
  border-radius: 0.3rem;
  overflow: hidden;
  display: block;
}
.poster div {
  padding-bottom: 140%;
}
.poster div img {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
}
.original .unit {
  display: flex;
}
.original.card-xs {
  margin: 0 -0.2rem;
}
.original.card-xs .unit {
  width: 33.3333333333%;
  float: left;
  padding: 0.5rem;
}
.original.card-xs .unit .inner {
  border-radius: 0.5rem;

  background: var(--bgcards);
  transition: background 0.3s;
  display: flex;
  align-items: center;
  position: relative;
  padding: 1rem;
  width: 100%;
}
.original.card-xs .unit .inner .poster {
  width: 4rem;
  margin-right: 0.8rem;
}
.original.card-xs .unit .inner .info {
  width: 1px;
  flex-grow: 1;
}
.original.card-xs .unit .inner .info > a {
  color: #fff;
  font-size: 1.05rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  display: block;
  margin-bottom: 0.3rem;
}
.original.card-xs .unit .inner .info > a:hover {
  color: var(--color-10);
}
.original.card-xs .unit .inner .info .dropdown {
  margin-bottom: 0.3rem;
}
.original.card-xs .unit .inner .info .dropdown button {
  font-size: 0.95rem;
  padding: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
  color: var(--color-7);
}
.original.card-xs .unit .inner .info .dropdown button:hover {
  color: var(--color-10);
}
.original.card-xs .unit .inner .info .dropdown button:after {
  content: "\f0dd";
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  margin-left: 0.2rem;
  margin-top: -0.3rem;
  font-size: 0.8rem;
}
.original.card-xs .unit .inner .info .richdata {
  display: flex;
  align-items: center;
  font-size: 0.95rem;
}
.original.card-xs .unit .inner .info .richdata > :not(:last-child) {
  margin-right: 1rem;
  color: var(--pt);
  font-size: 0.9rem;
  white-space: nowrap;
}
.original.card-xs .unit .inner .info .richdata span:after {
  background: 0 0;
}
.original.card-xs .unit .inner .info .richdata .read-status {
  border: 0;
  padding: 0;
  background: 0 0;
  color: var(--pt);
}
.original.card-xs .unit .inner .info .richdata .read-status:hover {
  color: var(--color-10);
}
.original.card-xs .unit .inner .info .richdata .read-status:before {
  content: "\f06e";
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  font-size: 0.9rem;
}
.original.card-xs .unit .inner .info .richdata .read-status.unread:before {
  content: "\f06e";
  color: var(--color-10);
}
.original.card-xs .unit .inner:hover {
  background: #d33f50 !important;
}
.original.card-sm .unit {
  display: flex;
  align-items: center;
  position: relative;
  padding: 0.4rem;
  width: 100%;
}
.original.card-sm .unit:nth-child(odd) {
  background: rgb(0 0 0 / 0.1);
}
.original.card-sm .unit .info {
  flex-grow: 1;
  width: 100%;
}
.original.card-sm .unit .info > h6 {
  transition: color 0.3s;
  width: 100%;
  font-weight: 500;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  line-height: 1.5rem;
  max-height: 1.5rem;
  overflow: hidden;
  color: var(--color-11);
  margin-bottom: 0;
}
.original.card-sm .unit .info > span {
  color: var(--color-7);
}
.original.card-sm .unit .info > div span {
  font-size: 0.95rem;
  color: var(--pt);
  display: inline-flex;
  align-items: center;
}
.original.card-sm .unit .info > div span i {
  margin-right: 0.12rem;
}
.original.card-sm .unit .info > div span:not(:last-child):after {
  content: "";
  display: block;
  width: 2px;
  height: 2px;
  margin: 0 0.4rem;
  background: var(--pt);
}
.original.card-sm .unit .info > p {
  font-size: 0.95rem;
  margin-bottom: 0;
  color: var(--pt);
}
.original.card-sm .unit .info .dropdown .dropdown-menu {
  max-height: 250px;
  overflow: auto;
}
.original.card-sm .unit .info .dropdown .dropdown-item {
  padding: 0.4rem 1.2rem;
}
.original.card-sm .unit .info .dropdown > a {
  color: var(--pt);
  font-weight: 500;
}
.original.card-sm .unit .poster {
  width: 4.2rem;
  margin-right: 0.8rem;
}
.original.card-sm .unit:hover {
  background: var(--secb);
}
.original.card-sm .unit:hover .info > h6 {
  color: #fff;
}
.original.card-sm .unit.seen {
  filter: grayscale(1);
  opacity: 0.7;
}
.card-md .unit a {
  background: var(--secb);
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
}
.card-md .unit a .poster > div > img {
  transition: transform 0.3s;
}
.card-md .unit a > span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 2px 4px;
  text-align: center;
}
.card-md .unit a:hover {
  background: #171717;
  border-color: #2d4163;
  color: #fff;
}
.card-md .unit a:hover p {
  height: 3.5rem;
  background: var(--secb);
}
.card-md .unit a:hover .poster > div > img {
  transform: scale(1.05);
}
.original.card-lg {
  margin: 0 -0.5rem;
}
.original.card-lg .unit {
  padding: 0.5rem;
  width: 16.6%;
  float: left;
}
.original.card-lg .unit .inner {
  display: flex;
  background: var(--bgcards);
  border-radius: 0.5rem;
  transition: background 0.3s, border 0.3s;
  flex-grow: 1;
  width: 100%;
  flex-direction: column;
  height: 100%;
}
.original.card-lg .unit .inner .poster {
  width: 100%;
}
.original.card-lg .unit .inner .info {
  padding: 10px;
  flex-grow: 1;
  display: flex;
  transition: transform 0.3s;
  flex-direction: column;
  width: 100%;
}
.original.card-lg .unit .inner .info > div {
  display: flex;
  align-items: center;
}
.original.card-lg .unit .inner .info > div img {
  width: 1.1rem;
  height: 1.1rem;
}
.original.card-lg .unit .inner .info > div img:not(:last-child) {
  margin-right: 0.3rem;
}
.original.card-lg .unit .inner .info > div .type {
  color: var(--color-7);
}
.original.card-lg .unit .inner .info > div nav {
  margin-left: auto;
  font-size: 0.85rem;
  background: var(--color-3);
  border-radius: 0.5rem;
  position: relative;

  cursor: pointer;
  display: flex;
  overflow: hidden;
  opacity: 0.5;
  transition: opacity 0.2s;
}
.original.card-lg .unit .inner .info > div nav > span {
  padding: 0.1rem 0.5rem;
  width: 50%;
  z-index: 2;
  position: relative;
  color: var(--pt);
  transition: color 0.1s;
  display: inline-block;
  text-align: center;
}
.original.card-lg .unit .inner .info > div nav > span.active {
  color: rgb(255 255 255 / 0.9);
  background: #23334e;
}
.original.card-lg .unit .inner .info > a {
  color: #d1d1d1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-size: 1.05rem;
  line-height: 1.4;
  height: 3rem;
}
.original.card-lg .unit .inner .info > ul {
  list-style: none;
  display: flex;
  gap: 6px;
  margin: 4px 0 0 0;
  padding: 0;
}
.original.card-lg .unit .inner .info > ul li a {
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  margin-top: 0.3rem;
  padding: 2px 4px;
  overflow: hidden;
  flex-direction: column;
  background: #1a1a1a;
}

.original.card-lg .unit .inner .info > ul li a span {
  white-space: nowrap;
}
.original.card-lg .unit .inner .info > ul li a b {
  font-weight: 500;
}
.original.card-lg .unit .inner .info > ul li a:hover {
  background: #252525;
  color: #a9a9a9;
}
.original.card-lg .unit .inner:hover {
  background: #171717;
  border-color: #343434;
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 0.15);
}
.original.card-lg .unit .inner:hover .info nav {
  opacity: 1;
}
.original.card-lg.reading .unit .inner {
  position: relative;
  overflow: hidden;
}
.original.card-lg.reading .unit .inner > button {
  position: absolute;
  right: 0.5rem;
  top: -2.5rem;
  background: 0 0;
  border: 0;
  color: var(--pt);
  z-index: 4;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--bgc);
  transition: top 0.3s;
}
.original.card-lg.reading .unit .inner > button:hover {
  color: #fff;
  background: #131313c9;
}
.original.card-lg.reading .unit .inner .info {
  padding: 10px;
}
.original.card-lg.reading .unit .inner .info > div span {
  font-size: 0.9rem;
}
.original.card-lg.reading .unit .inner .info p {
  margin-top: 0.4rem;
  font-size: 0.9rem;
}
.original.card-lg.reading .unit .inner .info p > span {
  color: var(--color-7);
}
.original.card-lg.reading .unit .inner > p {
  height: 2px;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  display: block;
  margin: 0;
  z-index: 5;
  background: rgb(0 0 0 / 0.1);
}
.original.card-lg.reading .unit .inner > p > span {
  background: var(--color-7);
  display: block;
  height: 100%;
}
.original.card-lg.reading .unit .inner:hover > button {
  top: 0.5rem;
}
#top-trending {
  z-index: 2;
  position: relative;
  opacity: 1;
  padding: 0.1rem 0;
}
#top-trending .swiper .swiper-wrapper {
  width: unset;
}
#top-trending .swiper .swiper-wrapper .swiper-slide {
  padding: 0;
  gap: 10px;
  display: flex;
}
#top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner {
  display: flex;
  position: relative;
  transition: border 0.3s, background 0.3s, box-shadow 0.3s;
  background: var(--bgcards);
  border: 0;
  margin: 0.7rem;
  overflow: hidden;
  border-radius: 12px;
  cursor: pointer;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .poster
  div
  img {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
#top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner .bookmark {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 3;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .bookmark
  .dropleft
  .dropdown-menu {
  max-height: 220px;
  overflow: auto;
  margin-top: 1rem;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .bookmark
  .dropleft
  .dropdown-item {
  padding: 0.4rem 1rem;
}
#top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner .info {
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  width: 100%;
  overflow: hidden;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .above
  > span {
  color: #4caf50;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .above
  > a {
  margin-top: 4px;
  font-size: 1.3rem;
  z-index: 5;
  font-weight: 600;
  position: relative;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 90%;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .above
  > a:hover {
  color: var(--color-10);
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .below
  > span {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.1rem;
  max-height: 2.8rem;
  overflow: hidden;
  font-size: 12px;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .below
  > p {
  margin: 0.8rem 0;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .below
  > div {
  margin-top: 0.25rem;
  z-index: 5;
  position: relative;
  white-space: nowrap;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .below
  > div
  a {
  color: var(--color-10);
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .below
  > div
  a
  + a {
  margin-left: 0.6rem;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner
  .info
  .below
  > div
  a:hover {
  color: var(--color-7);
}
#top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner:hover {
  background: var(--secb);
  border-color: #464646;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner:hover
  .poster {
  transform: rotate(0) scale(1) translate(0, 0);
  opacity: 1;
}
#top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner:hover:before {
  background: #464646;
}
#top-trending
  .swiper
  .swiper-wrapper
  .swiper-slide
  .swiper-inner:hover
  .bookmark
  .dropleft
  .btn {
  font-size: 2rem;
  opacity: 1;
  transition: 0.33s ease;
}
#top-trending .trending-button-next,
#top-trending .trending-button-prev {
  position: absolute;
  top: 50%;
  transform: translateY(-60%);
  z-index: 5;
  transition: background 0.3s;
  background: var(--color-7);
  box-shadow: rgb(0 0 0 / 0.1) 0 1px 3px 0, rgb(0 0 0 / 0.06) 0 1px 2px 0;
  width: 2rem;
  transition: width 0.2s;
}
#top-trending .trending-button-next:after,
#top-trending .trending-button-prev:after {
  font-size: 1.5rem;
  font-family: "Font Awesome 6 Pro";
  transition: color 0.3s;
  padding: 0.4rem;
  transition: padding 0.3s;
  color: #fff;
}
#top-trending .trending-button-next:hover,
#top-trending .trending-button-prev:hover {
  width: 3rem;
  border-color: #474747;
}
#top-trending .trending-button-next:hover:after,
#top-trending .trending-button-prev:hover:after {
  color: #fff;
  padding: 0.6rem;
}
#top-trending .trending-button-next.swiper-button-disabled,
#top-trending .trending-button-prev.swiper-button-disabled {
  background: var(--bgcards);
}
#top-trending .trending-button-next {
  right: 0;
  border-radius: 0.5rem 0 0 0.5rem;
}
#top-trending .trending-button-next:after {
  content: "\f105";
}
#top-trending .trending-button-prev {
  left: 0;
  border-radius: 0 0.5rem 0.5rem 0;
  text-align: right;
}
#top-trending .trending-button-prev:after {
  content: "\f104";
}
i.flag.EN {
  background: url(../images/flags/en.svg) no-repeat center;
  background-size: contain;
  width: 1.3rem;
  height: 1.3rem;
  display: inline-block;
  vertical-align: middle;
}
i.flag.FR {
  background: url(../images/flags/fr.svg) no-repeat center;
  background-size: contain;
  width: 1.3rem;
  height: 1.3rem;
  display: inline-block;
  vertical-align: middle;
}
i.flag.ES {
  background: url(../images/flags/es.svg) no-repeat center;
  background-size: contain;
  width: 1.3rem;
  height: 1.3rem;
  display: inline-block;
  vertical-align: middle;
}
i.flag.ES-LA {
  background: url(../images/flags/es-la.svg) no-repeat center;
  background-size: contain;
  width: 1.3rem;
  height: 1.3rem;
  display: inline-block;
  vertical-align: middle;
}
i.flag.PT {
  background: url(../images/flags/pt.svg) no-repeat center;
  background-size: contain;
  width: 1.3rem;
  height: 1.3rem;
  display: inline-block;
  vertical-align: middle;
}
i.flag.PT-BR {
  background: url(../images/flags/pt-br.svg) no-repeat center;
  background-size: contain;
  width: 1.3rem;
  height: 1.3rem;
  display: inline-block;
  vertical-align: middle;
}
i.flag.JA {
  background: url(../images/flags/ja.svg) no-repeat center;
  background-size: contain;
  width: 1.3rem;
  height: 1.3rem;
  display: inline-block;
  vertical-align: middle;
}
i.flag + i {
  margin-left: 0.3rem;
}
.home-swiper {
  margin-bottom: 3rem;
}
.home-swiper .swiper {
  padding: 12px 0;
}
.home-swiper .swiper .swiper-wrapper .swiper-slide {
  width: 14.2857142857%;
  padding: 0 0.5rem;
}

.home-swiper .swiper .swiper-wrapper .swiper-slide > a {
  background: var(--secb);

  display: block;
  border-radius: 0.5rem;
}
.home-swiper .swiper .swiper-wrapper .swiper-slide > a .poster > div > img {
  transition: transform 0.3s;
  height: 100%;
}
.home-swiper .swiper .swiper-wrapper .swiper-slide > a > span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0.7rem 1rem;
  display: block;
  text-align: center;
}
.home-swiper .swiper .swiper-wrapper .swiper-slide > a:hover {
  background: #171717;
  border-color: #2e2e2e;
  color: #c1c1c1;
}
.home-swiper
  .swiper
  .swiper-wrapper
  .swiper-slide
  > a:hover
  .poster
  > div
  > img {
  transform: scale(1.05);
}
.home-swiper .swiper-pagination-progressbar {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
  margin-top: 2rem;
  height: 1px;
  background: var(--secb);
}
.home-swiper
  .swiper-pagination-progressbar
  .swiper-pagination-progressbar-fill {
  background: var(--color-10);
  border-radius: 0.5rem;
}
.home-swiper .swiper-pagination-bullets {
  justify-content: center;
  margin-top: 2rem;
  display: flex;
}
.home-swiper .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 2px;
  max-width: 3rem;
  width: 100%;
  border-radius: 0.5rem;
  background: var(--secb);
  height: 2px;
  opacity: 1;
  transition: background 0.3s;
}
.home-swiper .swiper-pagination-bullets .swiper-pagination-bullet:hover {
  background: var(--color-7);
}
.home-swiper
  .swiper-pagination-bullets
  .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--color-10);
}
.modal-dialog .modal-content .modal-close {
  background: 0 0;
  font-size: 1.8rem;
  color: var(--pt);
}
.modal-dialog .modal-content .modal-close:hover {
  background: 0 0;
  color: #fff;
}
.manga-detail {
  position: relative;
  background: var(--bgc);
  overflow: hidden;
  color: #fff;
  padding: 3rem 0 1rem; /*Changed from 3rem 0 13rem*/
}
.manga-detail .detail-bg {
  position: absolute;
  top: 0;
  height: 100%;
  left: 0;
  width: 100%;
  filter: grayscale(1);
}
.manga-detail .detail-bg > img {
  -o-object-fit: cover;
  object-fit: cover;
  filter: blur(12px);
  opacity: 0.08;
  width: 100%;
  height: 100%;
}
.manga-detail .main-inner {
  z-index: 2;
  position: relative;
}
.manga-detail .main-inner .content::after {
  display: block;
  clear: both;
  content: "";
}
.manga-detail .main-inner .content .poster {
  width: 15rem;
}
.manga-detail .main-inner .content .poster > div {
  box-shadow: rgb(35 84 121 / 0.6) 0 1px 3px 0, rgb(35 84 121 / 0.5) 0 1px 2px 0;
}
.manga-detail .main-inner .content .info {
  padding-left: 2rem;
}
.manga-detail .main-inner .content .info > p {
  margin-bottom: 0;
  text-transform: uppercase;
  color: var(--color-10);
  letter-spacing: 0.5rem;
}
.manga-detail .main-inner .content .info > h1 {
  font-size: 2rem;
  margin: 0.5rem 0;
  line-height: 2rem;
}
.manga-detail .main-inner .content .info > h6 {
  font-size: 0.95rem;
  font-weight: 300;
  color: var(--icol);
  line-height: 1.3rem;
  max-height: 2.6rem;
  overflow: auto;
}
.manga-detail .main-inner .content .info .min-info {
  font-weight: 300;
  margin-bottom: 1rem;
}
.manga-detail .main-inner .content .info .min-info > * {
  margin-right: 1.2rem;
  color: var(--icol);
}
.manga-detail .main-inner .content .info .min-info > * b {
  font-weight: 500;
}
.manga-detail .main-inner .content .info .min-info a:hover {
  color: #fff;
}
.manga-detail .main-inner .content .info .description {
  color: var(--pt);
}
.manga-detail .main-inner .content .info .description .text {
  color: var(--pt);
  font-weight: 300;
  margin-bottom: 0.5rem;
  line-height: 1.5rem;
}
.manga-detail .main-inner .content .info .description .text.short {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: 1.5rem;
  max-height: 4.5rem;
  overflow: hidden;
}
.manga-detail .main-inner .content .info .description .text.full {
  max-height: 4.5rem;
  overflow: auto;
}
.manga-detail .main-inner .content .info .readmore {
  color: var(--pt);
  padding: 0;
  border-bottom: 1px solid var(--color-10);
  border-radius: 0;
}
.manga-detail .main-inner .content .info .readmore:hover {
  color: #fff;
}
.manga-detail .main-inner .content .actions {
  display: flex;
  margin: 1.5rem 0;
  flex-direction: column;
  gap: 8px;
}
.manga-detail .main-inner .content .actions .btn {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  padding: 0.6rem 1.5rem;
  justify-content: center;
}
.manga-detail .main-inner .content .actions .btn > i {
  margin-left: 0.35rem;
}
.manga-detail .main-inner .content .actions .bookmark.dropright .dropdown-menu {
  top: -6rem;
}
.manga-detail .main-inner #info-rating-btn {
  display: none;
}
.manga-detail .main-inner .sidebar #info-rating.collapse {
  display: block;
}
.manga-detail .main-inner .sidebar .meta {
  margin-top: 1.5rem;
}
.manga-detail .main-inner .sidebar .meta > div {
  line-height: 1.9rem;
}
.manga-detail .main-inner .sidebar .meta > div span:first-child {
  color: var(--pt);
}
.manga-detail .main-inner .sidebar .rating-box {
  display: flex;
  align-items: center;

  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  margin-top: 1.7rem;
  background: var(--bgcards);
  transition: background 0.3s;
  justify-content: space-between;
}
.manga-detail .main-inner .sidebar .rating-box:hover {
  background: #d33f50;
}
.manga-detail .main-inner .sidebar .rating-box .score > div {
  font-size: 1.7rem;
  font-weight: 500;
  line-height: 1.7rem;
  position: relative;
}
.manga-detail .main-inner .sidebar .rating-box .score > div sup {
  font-size: 1rem;
}
.manga-detail .main-inner .sidebar .rating-box .score > span {
  color: var(--icol);
}
.manga-detail .main-inner .sidebar .rating-box .stars {
  display: flex;
  justify-items: center;
  align-items: center;
}
.manga-detail .main-inner .sidebar .rating-box .stars span {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.5rem;
  color: rgb(255 255 255 / 0.8);
  transition: all 0.3s;
}
.manga-detail .main-inner .sidebar .rating-box .stars span.active {
  color: #efc300;
}
.manga-detail .main-inner .sidebar .rating-box .stars:hover span.active {
  transform: scale(1.1);
}
.default-style {
  background: var(--bgcards);

  border-radius: 1rem;
}
.default-style .head {
  padding: 1rem;
  margin: 0;
}
.default-style .head h2 {
  font-size: 1.4rem;
}

.manga-bottom .content,
.manga-bottom .sidebar {
  border-radius: 1rem;
}
.manga-bottom .content section {
  box-shadow: rgb(35 84 121 / 0.1) 0 1px 3px 0,
    rgb(35 84 121 / 0.06) 0 1px 2px 0;
  border-radius: 1rem;
}
.manga-bottom .content .m-list {
  display: flex;
  flex-direction: column;
  position: relative;
}
.manga-bottom .content .m-list .chapvol-tab {
  display: flex;
  background: var(--secb);
  border-radius: 0.5rem;
  position: absolute;
  right: 0;
  bottom: 100%;
  margin-bottom: -3rem;
  z-index: -1;
  overflow: hidden;
  box-shadow: rgb(35 84 121 / 0.1) 0 1px 3px 0,
    rgb(35 84 121 / 0.06) 0 1px 2px 0;
}
.manga-bottom .content .m-list .chapvol-tab > a {
  cursor: pointer;
  padding: 0.6rem 0;
  display: inline-flex;
  color: #fff;
  text-transform: uppercase;
  transition: background 0.3s;
  letter-spacing: 0.2rem;
  padding-bottom: 3.5rem;
  width: 9rem;
  justify-content: center;
}
.manga-bottom .content .m-list .chapvol-tab > a:hover {
  background: #474747;
}
.manga-bottom .content .m-list .chapvol-tab > a.active {
  color: #fff;
  background: var(--color-7);
}
.manga-bottom .content .m-list .list-menu {
  display: flex;
  justify-content: space-between;
  padding: 1rem 0;
}
.manga-bottom .content .m-list .list-menu .dropdown .btn,
.manga-bottom .content .m-list .list-menu form {
  border-radius: 10px;
  display: inline-flex;
  align-items: center;
  height: 2.5rem;
}
.manga-bottom .content .m-list .list-menu .dropdown .btn {
  padding: 0.4rem 1rem;
}
.manga-bottom .content .m-list .list-menu .dropdown .btn i {
  margin-right: 0.3rem;
}
.manga-bottom .content .m-list .list-menu .dropdown .btn b {
  margin-left: 0.2rem;
  font-weight: 500;
}
.manga-bottom .content .m-list .list-menu form {
  background: #151515;
}
.manga-bottom .content .m-list .list-menu form input {
  border: 0;
  background: 0 0;
  color: #fff;
  width: 8.7rem;
  padding: 0.4rem 0 0.4rem 1rem;
}
.manga-bottom .content .m-list .list-menu form .btn {
  padding-left: 0.2rem;
}
.manga-bottom .content .m-list .content .message {
  text-align: center;
  padding: 1rem;
}
.manga-bottom .content .m-list .vol-list,
.manga-bottom .content .m-list ul {
  max-height: 35rem;
  overflow-y: auto;
}
.manga-bottom .content .m-list ul {
  list-style: none;
  margin: 0;
  padding: 0;
  flex-grow: 1;
  border-radius: 0;
  gap: 6px;
  display: flex;
  flex-direction: column;
}
.manga-bottom .content .m-list ul li {
  width: 100%;
}
.manga-bottom .content .m-list ul li:last-child a {
  border-radius: 0 0 1rem 1rem;
}
.manga-bottom .content .m-list ul li a {
  padding: 1rem 1.6rem;
  display: flex;
  justify-content: space-between;
  background: var(--color-3);
  color: var(--pt);
  align-items: center;
  flex-direction: row;
  border-radius: 6px;
}
.manga-bottom .content .m-list ul li a span:first-child {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.manga-bottom .content .m-list ul li a span:first-child:before {
  content: "\f04b";
  font-family: "Font Awesome 6 Pro";
  margin-right: 0;
  font-weight: 900;
  transition: margin 0.3s, opacity 0.3s, font-size 0.3s;
  opacity: 0;
  font-size: 0;
}
.manga-bottom .content .m-list ul li a span:last-child {
  width: fit-content;
  text-align: right;
}
.manga-bottom .content .m-list ul li a:visited {
  background: #121a27;
}
.manga-bottom .content .m-list ul li a:visited span {
  color: #51565f;
}
.manga-bottom .content .m-list ul li a:hover {
  background: #162131;
}
.manga-bottom .content .m-list ul li a:hover span {
  color: var(--color-7);
}
.manga-bottom .content .m-list ul li a:hover span:first-child:before {
  opacity: 1;
  margin-right: 0.5rem;
  font-size: 0.8rem;
}
.manga-bottom .content .m-list ul li + li {
  border-top: 1px solid var(--bgcards);
}
.manga-bottom .content .m-list .vol-list {
  padding: 1rem 0.5rem;
  background: var(--color-3);
}
.manga-bottom .content .m-list .vol-list .unit {
  width: 20%;
  float: left;
  padding: 0.5rem;
}
.manga-bottom .sidebar .m-related {
  background: var(--bgcards);
}
.manga-bottom .sidebar .m-related .head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.2rem;
  border-bottom: 1px solid var(--bgcards);
  margin: 0;
}
.manga-bottom .sidebar .m-related .head .dropdown > button {
  border: 0;
  border-radius: 50rem;
  padding: 0.3rem 1rem;
  height: 100%;
}
.manga-bottom .sidebar .m-related ul {
  list-style: none;
  padding: 0.5rem 0;
  margin: 0;
  overflow-y: auto;
  max-height: 10rem;
  background: var(--color-3);
  border-radius: 0 0 1rem 1rem;
}
.manga-bottom .sidebar .m-related ul li a {
  color: var(--icol);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  padding: 0.5rem 1.2rem;
}
.manga-bottom .sidebar .m-related ul li a:hover {
  color: var(--color-7);
}
.scroll-sm::-webkit-scrollbar {
  width: 8px;
}
#go-top {
  opacity: 0.8;
  color: #fff;
  transition: all 0.3s;
  position: fixed;
  bottom: 12px;
  right: 12px;
}
#go-top i {
  color: var(--color-10);
}
#go-top:hover {
  opacity: 1;
}
/* de base c'est 7rem */
main {
  padding-bottom: 0rem;
}
footer {
  position: relative;
}
footer .gotop {
  position: absolute;
  bottom: 100%;
  z-index: 5;
  width: 100%;
  text-align: center;
}

footer .inner {
  display: flex;
  justify-content: space-between;
  padding: 4rem 0;
  align-items: flex-end;
}
footer .inner nav {
  text-align: right;
}
footer .inner nav > a {
  color: #fff;
}
footer .inner nav > a:not(:first-child) {
  margin-left: 1rem;
  font-size: 1.15rem;
}
footer .inner nav > a:hover {
  color: var(--color-7);
}
footer .inner nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
  line-height: 2rem;
  display: flex;
}
footer .inner nav ul li:not(:last-child) {
  margin-right: 2rem;
}
footer .inner nav ul li a {
  color: var(--pt);
  font-size: 0.9rem;
}
footer .inner nav ul li a:hover {
  color: var(--color-7);
}
footer .inner div p {
  margin: 0.6rem 0 0 0;
  font-size: 0.9rem;
}
footer .abs-footer {
  border-top: 1px solid var(--secb);
  background: var(--bgcards);
}
footer .abs-footer .wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.95rem 0;
  font-size: 0.9rem;
  color: var(--pt);
}
.tooltipster-sidetip .tooltipster-box {
  width: 23rem;
  border: 0;
  background: 0 0;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content {
  border-radius: 0.5rem;
  background: var(--secb);
  color: var(--icol);
  padding: 0;
  overflow: hidden;
  padding: 1.9rem 1.5rem;
  position: relative;
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 0.175);
  border: 1px solid #474747;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content:before {
  background: radial-gradient(
    closest-side,
    rgb(60 139 198 / 0.2) 0,
    #fff0 100%
  );
  content: "";
  right: -22rem;
  top: -35rem;
  width: 50rem;
  height: 50rem;
  display: block;
  position: absolute;
  pointer-events: none;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content .bookmark {
  position: absolute;
  right: 0.5rem;
  top: 0.5rem;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content .bookmark .btn {
  color: var(--color-10);
  padding: 0;
}
.tooltipster-sidetip
  .tooltipster-box
  .tooltipster-content
  .bookmark
  .btn:hover {
  color: var(--color-10);
}
.tooltipster-sidetip
  .tooltipster-box
  .tooltipster-content
  .bookmark
  .dropdown-menu
  .dropdown-item {
  font-size: 0.95rem;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content span {
  color: var(--color-10);
  text-transform: uppercase;
  letter-spacing: 0.2rem;
  font-size: 0.95rem;
  font-weight: 500;
  display: block;
  margin-bottom: 0.8rem;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content > a {
  font-size: 1.2rem;
  color: #fff;
  font-weight: 500;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content > a:hover {
  opacity: 0.8;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content > p {
  font-size: 0.95rem;
  margin: 1rem 0;
  font-weight: 300;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content > p > b {
  font-weight: 500;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content > nav a {
  display: inline-block;
  background: var(--bgcards);
  padding: 0.2rem 0.8rem;
  border-radius: 50rem;
  font-size: 0.9rem;

  font-weight: 300;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content > nav a + a {
  margin-left: 0.1rem;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content > nav a:hover {
  background: var(--color-3);
  color: #fff;
}
body.read {
  --header-padding: 0;
  overflow-y: hidden;
}
body.read.header-hidden {
  --header-padding: 0rem;
}
body.read .viewing {
  margin-left: 1rem;
}
body.read .viewing b {
  white-space: nowrap;
}
body.read #show-ctrl-menu {
  margin-left: 1rem;
  white-space: nowrap;
}
body.read #show-ctrl-menu span {
  margin-left: 0.2rem;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.1rem;
}
body.read main {
  display: flex;
  width: 100%;
  height: 100vh;
  padding: 0;
  overflow: hidden;
}
body.read main .m-content {
  width: 1px;
  flex-grow: 1;
  position: relative;
  padding-top: var(--header-padding);
}
body.read main .m-content .message {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
body.read main .m-content .message .inner {
  background: var(--color-3);
  border-radius: 0.5rem;
  padding: 1rem;
  max-width: 20rem;
  text-align: center;
}
body.read main .m-content .loading:before {
  content: "";
  background: url(../images/loading.gif?2) no-repeat center;
  width: 50px;
  height: 50px;
  animation: unset;
  background-size: contain;
  border: none;
  opacity: 0.5;
}
body.read main .m-content #page-wrapper-x {
  width: 100%;
  height: 100%;
  overflow: auto;
  display: block;
  --number-nav-height: 0rem;
}
body.read main .m-content #page-wrapper-x.on-last-page {
  --number-nav-height: 3rem;
}
body.read main .m-content #page-wrapper-x .pages {
  width: 100%;
}
body.read main .m-content #page-wrapper-x .pages.longstrip {
  display: block;
}
body.read main .m-content #page-wrapper-x .pages.doublepage,
body.read main .m-content #page-wrapper-x .pages.singlepage {
  display: block;
  width: auto;
  height: calc(100% - var(--number-nav-height));
}
body.read main .m-content #page-wrapper-x .pages.doublepage .page,
body.read main .m-content #page-wrapper-x .pages.singlepage .page {
  display: flex;
  width: 100%;
  min-height: 100%;
  align-items: center;
}
body.read main .m-content #page-wrapper-x .pages.doublepage .page .img,
body.read main .m-content #page-wrapper-x .pages.singlepage .page .img {
  flex-grow: 1;
  flex-shrink: 0;
  flex-basis: fit-content;
}
body.read main .m-content #page-wrapper-x .pages.doublepage .page.fit-w .img,
body.read main .m-content #page-wrapper-x .pages.singlepage .page.fit-w .img {
  flex-shrink: 1;
}
body.read main .m-content #page-wrapper-x .pages.doublepage.swiper,
body.read main .m-content #page-wrapper-x .pages.singlepage.swiper {
  overflow: hidden;
}
body.read
  main
  .m-content
  #page-wrapper-x
  .pages.doublepage.swiper
  .page.fit-w
  .img,
body.read
  main
  .m-content
  #page-wrapper-x
  .pages.singlepage.swiper
  .page.fit-w
  .img {
  flex-shrink: 0;
}
body.read main .m-content #page-wrapper-x .pages.doublepage.swiper .page .img,
body.read main .m-content #page-wrapper-x .pages.singlepage.swiper .page .img {
  flex-basis: auto;
  display: flex;
  align-items: center;
}
body.read
  main
  .m-content
  #page-wrapper-x
  .pages.doublepage.swiper
  .page
  .img
  img,
body.read
  main
  .m-content
  #page-wrapper-x
  .pages.singlepage.swiper
  .page
  .img
  img {
  max-height: 100%;
}
body.read main .m-content #page-wrapper-x .page {
  margin: 0 auto;
  width: 100%;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  --max-height: calc(100vh - var(--header-padding) - var(--number-nav-height));
}
body.read main .m-content #page-wrapper-x .page .img {
  position: relative;
  display: block;
  min-width: 50px;
  min-height: 50px;
  width: 100%;
  text-align: center;
  pointer-events: none;
}
body.read main .m-content #page-wrapper-x .page .img:not(.loaded):after {
  content: "";
  background: url(../images/loading.gif?2) no-repeat center;
  width: 50px;
  height: 50px;
  animation: unset;
  background-size: contain;
  border: none;
  opacity: 0.5;
  display: block;
  margin: 0 auto;
}
body.read main .m-content #page-wrapper-x .page .img:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 5%;
  z-index: 9;
}
body.read main .m-content #page-wrapper-x .page .img.left {
  text-align: right;
}
body.read main .m-content #page-wrapper-x .page .img.right {
  text-align: left;
}
body.read main .m-content #page-wrapper-x .page .img.left:before {
  background: linear-gradient(90deg, #fff0 0, rgb(0 0 0 / 0.5) 100%);
  right: 0;
}
body.read main .m-content #page-wrapper-x .page .img.right:before {
  left: 0;
  background: linear-gradient(90deg, rgb(0 0 0 / 0.5) 0, #fff0 100%);
}
body.read main .m-content #page-wrapper-x .page img {
  margin-left: auto;
  margin-right: auto;
  -o-object-fit: contain;
  object-fit: contain;
  transition: all 0.3s;
}
body.read
  main
  .m-content
  #page-wrapper-x
  .page
  img.stretch:not(.fit-h):not(.limit-h) {
  min-width: 100%;
}
body.read main .m-content #page-wrapper-x .page img.fit-w {
  max-width: 100%;
  min-width: 0;
}
body.read main .m-content #page-wrapper-x .page img.fit-h {
  max-height: var(--max-height);
  min-height: 0;
}
body.read main .m-content .number-nav {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
body.read main .m-content .number-nav.ltr .rtl-icon {
  display: none;
}

body.read main .m-content .number-nav a,
.chapter-control .jb-btn {
  background: transparent;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 16px;
  color: #5f5f5f;
  gap: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid #1d1d1d;
}
body.read main .m-content .number-nav a:hover,
.chapter-control .jb-btn:hover {
  background: #242424;
  color: #fff;
}
body.read main .m-content .number-nav.abs {
  margin: 0;
  padding: 0;
  width: 100%;
  left: 0;
  bottom: 0;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  z-index: 10;
  height: 0;
  overflow: hidden;
  transition: height 0.2s;
}
body.read main .m-content .number-nav.abs.show {
  height: var(--number-nav-height);
}
body.read main .m-content .number-nav.abs a {
  height: 100%;
  flex-grow: 1;
  background: var(--color-6);
  font-weight: 500;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
  margin: 0;
  padding: 0;
}
body.read main .m-content .number-nav.abs a:hover {
  background: var(--color-7);
  color: #fff;
}
body.read main .m-content .number-nav.abs a + a {
  border-left: 2px solid var(--bgcards);
}
#progress-bar {
  position: absolute;
  z-index: 2;
  display: flex;
  transition: top 0.3s;
}
#progress-bar.bottom,
#progress-bar.top {
  right: 0;
  left: 0;
  height: 1.4rem;
}
#progress-bar.bottom > div,
#progress-bar.top > div {
  align-items: center;
  height: 0.3rem;
}
#progress-bar.bottom > div ul li + li,
#progress-bar.top > div ul li + li {
  margin-left: 0.1rem;
}
#progress-bar.bottom > div ul li > div,
#progress-bar.top > div ul li > div {
  left: 50%;
  transform: translate(-50%, 0);
}
#progress-bar.bottom:hover > div,
#progress-bar.top:hover > div {
  height: 100%;
}
#progress-bar.bottom:hover > div p,
#progress-bar.top:hover > div p {
  font-size: 1rem;
  padding: 0 0.5rem;
}
#progress-bar.bottom {
  bottom: 0;
  align-items: flex-end;
}
#progress-bar.bottom > div ul li > div {
  bottom: 100%;
  margin-bottom: 0.2rem;
}
#progress-bar.top {
  top: 0;
  top: var(--header-padding);
  align-items: flex-start;
}
#progress-bar.top > div ul li > div {
  top: 100%;
  margin-top: 0.2rem;
}
#progress-bar.left,
#progress-bar.right {
  top: 0;
  top: var(--header-padding);
  bottom: 0;
  width: 1.4rem;
}
#progress-bar.left > div,
#progress-bar.right > div {
  flex-direction: column;
  justify-content: center;
  width: 0.3rem;
}
#progress-bar.left > div ul,
#progress-bar.right > div ul {
  flex-direction: column;
}
#progress-bar.left > div ul li + li,
#progress-bar.right > div ul li + li {
  margin-top: 0.1rem;
}
#progress-bar.left > div ul li > div,
#progress-bar.right > div ul li > div {
  top: 50%;
  transform: translate(0, -50%);
}
#progress-bar.left:hover > div,
#progress-bar.right:hover > div {
  width: 100%;
}
#progress-bar.left:hover > div p,
#progress-bar.right:hover > div p {
  font-size: 1rem;
  padding: 0.5rem 0;
}
#progress-bar.left {
  left: 0;
}
#progress-bar.left > div ul li > div {
  left: 100%;
  margin-left: 0.2rem;
}
#progress-bar.right {
  right: 0;
  justify-content: flex-end;
}
#progress-bar.right > div ul li > div {
  right: 100%;
  margin-right: 0.2rem;
}
#progress-bar.bottom.rtl > div,
#progress-bar.top.rtl > div {
  flex-direction: row-reverse;
}
#progress-bar.bottom.rtl > div ul,
#progress-bar.top.rtl > div ul {
  flex-direction: row-reverse;
}
#progress-bar > div {
  display: flex;
  width: 100%;
  background: var(--bgc);
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 0.175);
  transition: height 0.3s, width 0.3s;
}
#progress-bar > div p {
  font-size: 0;
  margin: 0;
  transition: padding 0.3s, font-size 0.3s;
  margin: 0;
  text-align: center;
}
#progress-bar > div ul {
  display: flex;
  flex-grow: 1;
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
}
#progress-bar > div ul li {
  flex-grow: 1;
  cursor: pointer;
  position: relative;
  background: var(--bgcards);
  transition: background 0.3s;
}
#progress-bar > div ul li > div {
  position: absolute;
  display: block;
  background: var(--color-7);
  color: #fff;
  height: 1.6rem;
  border-radius: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s;
  line-height: 1rem;
  padding: 0.3rem 0.5rem;
  white-space: nowrap;
}
#progress-bar > div ul li:hover {
  background: var(--secb);
}
#progress-bar > div ul li:hover > div {
  opacity: 1;
}
#progress-bar > div ul li.active {
  background: var(--color-7);
}
#progress-bar > div ul li:not(.active ~ li, .active) {
  background: var(--color-6);
  transition: background 0.3s;
}
#progress-bar > div ul li:not(.active ~ li, .active):hover {
  background: rgb(60 139 198 / 0.8);
}
#ctrl-menu {
  background: var(--bgc);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s, margin 0.3s;
  margin-right: -22rem;
  width: 22rem;
  overflow-y: auto;
  overflow-x: visible;
  flex-shrink: 0;
  z-index: 150;
  position: relative;
  position: relative;
  padding: 1.5rem;
}
#ctrl-menu.active {
  margin-right: 0;
}
#ctrl-menu .head {
  display: flex;
  justify-content: space-between;
}
#ctrl-menu .head > a {
  color: #fff;
  font-size: 1.1rem;
  font-weight: 500;
  display: inline-block;
  padding-right: 0.3rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4rem;
  max-height: 3.4rem;
  overflow: hidden;
  background: none;
}
#ctrl-menu .head > a:hover {
  color: var(--color-10);
}
#ctrl-menu .panel-btn,
#ctrl-menu button,
#ctrl-menu > a {
  width: 100%;
  display: flex;
  align-items: center;
  border-radius: 0.5rem;
  border: 0;
  padding: 0.6rem 1.5rem;
  background: #191919;
  color: #868686;
  font-weight: 300;
  line-height: 1.9rem;
  white-space: nowrap;
}
#ctrl-menu .panel-btn.jb-btn,
#ctrl-menu button.jb-btn,
#ctrl-menu > a.jb-btn {
  justify-content: space-between;
}
#ctrl-menu .panel-btn.jb-btn:not(:last-child),
#ctrl-menu button.jb-btn:not(:last-child),
#ctrl-menu > a.jb-btn:not(:last-child) {
  margin-bottom: 0.5rem;
}
#ctrl-menu .panel-btn b,
#ctrl-menu button b,
#ctrl-menu > a b {
  color: #fff;
  font-weight: 400;
  transition: color 0.3s;
}
#ctrl-menu .panel-btn svg,
#ctrl-menu button svg,
#ctrl-menu > a svg {
  width: 1.3rem;
  height: 1.3rem;
  line-height: 1.9rem;
}
#ctrl-menu .panel-btn:focus-visible,
#ctrl-menu button:focus-visible,
#ctrl-menu > a:focus-visible {
  outline: 0;
}
#ctrl-menu .panel-btn:hover,
#ctrl-menu button:hover,
#ctrl-menu > a:hover {
  background: var(--secb);
  color: #fff;
}
#ctrl-menu .panel-btn.disabled,
#ctrl-menu .panel-btn:disabled,
#ctrl-menu button.disabled,
#ctrl-menu button:disabled,
#ctrl-menu > a.disabled,
#ctrl-menu > a:disabled {
  background: var(--color-3);
  color: rgb(116 124 136 / 0.5);
}
#ctrl-menu .panel-btn.active,
#ctrl-menu button.active,
#ctrl-menu > a.active {
  color: #fff !important;
  background-color: var(--color-7);
}
#ctrl-menu .chapvol-switch {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
}
#ctrl-menu .chapvol-switch div {
  text-align: left;
}
#ctrl-menu .chapvol-switch div p {
  margin-bottom: 0;
}
#ctrl-menu .chapvol-switch div b {
  font-size: 1.2rem;
  color: var(--color-10);
}
#ctrl-menu .chapvol-switch i {
  font-size: 1.4rem;
  transition: transform 0.7s;
}
#ctrl-menu .chapvol-switch:hover i {
  transform: rotate(360deg) scale(1.3);
}
#ctrl-menu > nav {
  display: flex;
  margin-bottom: 0.5rem;
}
#ctrl-menu > nav > button {
  justify-content: center;
}
#ctrl-menu > nav > button:first-child,
#ctrl-menu > nav > button:last-child {
  width: 4rem;
  padding: 1rem 0;
}
#ctrl-menu > nav > button:nth-child(2) {
  margin: 0 0.5rem;
  justify-content: space-between;
}
#ctrl-menu > nav .page-select {
  padding: 0.8rem 1.5rem;
}
#ctrl-menu .dropdown-menu {
  max-height: 250px;
  overflow: auto;
}
#ctrl-menu .btn-options > div {
  display: none;
}
.close-primary {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  flex-shrink: 0;
}
.sub-panel {
  background: var(--bgc);
  position: absolute;
  top: 0;
  top: var(--header-padding);
  bottom: 0;
  right: 0;
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 0.175);
  z-index: 160;
  width: 0;
  overflow-x: hidden;
  overflow-y: auto;
  transition: width 0.3s;
  border: 1px solid var(--bgcards);
  z-index: 9999;
}
.sub-panel .inner {
  width: 22rem;
  transition: width 0.3s;
}
.sub-panel.active {
  width: 22rem;
}
.sub-panel#comment-panel .inner {
  width: 100%;
}
.sub-panel#comment-panel.active {
  width: 900px;
  max-width: 100%;
}
.sub-panel .head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bgcards);
  padding: 0.5rem 1.5rem;
  height: 3.5rem;
  border-bottom: 1px solid var(--secb);
}
.sub-panel .head b {
  font-weight: 500;
}
.sub-panel .head form .form-group {
  margin-bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sub-panel .head form .form-group i {
  font-size: 0.9rem;
  color: var(--pt);
  margin-right: 0.3rem;
}
.sub-panel .head form .form-group input {
  background: 0 0;
  border: 0;
  padding: 0;
}
.sub-panel ul {
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
}
.sub-panel ul li a {
  padding: 1rem 1.6rem;
  background: var(--color-3);
  color: var(--pt);
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-bottom: 1px solid var(--bgcards);
}
.sub-panel ul li a:before {
  content: "\f04b";
  font-family: "Font Awesome 6 Pro";
  margin-right: 0;
  font-weight: 900;
  transition: margin 0.3s, opacity 0.3s, font-size 0.3s;
  opacity: 0;
  font-size: 0;
}
.sub-panel ul li a.active:before,
.sub-panel ul li a:hover:before {
  opacity: 1;
  margin-right: 0.5rem;
  font-size: 0.8rem;
}
.sub-panel ul li a:hover {
  background: #212121;
}
.sub-panel ul li a:hover span {
  color: var(--icol);
}
.sub-panel ul li a.active {
  background: var(--bgcards);
  color: var(--color-10);
}
.sub-panel ul li a.active:hover {
  background: var(--secb);
}
.sub-panel ul li a.active:hover span {
  color: var(--color-7);
}
.sub-panel ul li a:visited {
  background: #141414;
}
.sub-panel ul li a:visited:hover {
  background: #212121;
}
.advanced-settings .modal-dialog {
  max-width: 600px;
}
.advanced-settings h5 {
  color: #fff;
}
.advanced-settings .nav-tabs {
  border-radius: 0.3rem;
  overflow: hidden;
  margin: 1rem 0 1.5rem;
}
.advanced-settings .nav-tabs .nav-item {
  flex-grow: 1;
}
.advanced-settings .nav-tabs .nav-item + .nav-item {
  border-left: 1px solid var(--color-6);
}
.advanced-settings .nav-tabs .nav-item .nav-link {
  width: 100%;
  color: #fff;
  font-weight: 500;
  letter-spacing: 0.2rem;
  text-transform: uppercase;
  background: var(--secb);
  padding: 0.8rem 0;
}
.advanced-settings .nav-tabs .nav-item .nav-link:hover {
  background: #22314a;
}
.advanced-settings .nav-tabs .nav-item .nav-link.active {
  background: var(--color-7);
}
.advanced-settings .strip-margin {
  display: flex;
  align-items: center;
  padding: 0.5rem 0 0;
}
.advanced-settings .strip-margin label {
  margin-bottom: 0;
  flex-shrink: 0;
  margin-right: 1rem;
}
.advanced-settings .setting-tab {
  overflow: hidden;
  display: flex;
  align-items: stretch;
  margin: 0.5rem 0 1.3rem;
}
.advanced-settings .setting-tab > div {
  flex: 1;
  text-align: center;
  cursor: pointer;
  transition: background 0.3s, color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 3rem;
  background: var(--secb);
  font-weight: 300;
  border-radius: 0.3rem;
  border: 1px solid #474747;
  margin: 0 0.2rem;
}
.advanced-settings .setting-tab > div i {
  margin-right: 0.2rem;
}
.advanced-settings .setting-tab > div svg {
  fill: var(--icol);
  width: 1.2rem;
  height: 1.2rem;
  margin-right: 0.2rem;
  transition: fill 0.3s;
}
.advanced-settings .setting-tab > div:hover {
  color: #fff;
  background-color: #474747;
  border-color: #364f78;
}
.advanced-settings .setting-tab > div:hover svg {
  fill: #fff;
}
.advanced-settings .setting-tab > div.active {
  color: #fff;
  border-color: var(--color-7);
  background-color: #474747;
}
.advanced-settings .setting-tab > div.active i,
.advanced-settings .setting-tab > div.active svg {
  fill: #fff;
  color: #fff;
}
.advanced-settings .setting-tab > div.disabled {
  color: #494949;
  background-color: #252525;
  cursor: default;
}
.advanced-settings .setting-tab > div.disabled:hover {
  color: #494949 !important;
  background-color: #252525;
}
.advanced-settings .setting-tab.read-direction div b {
  display: none;
}
#filters {
  margin-bottom: 1rem;
}
#filters > div:last-child {
  margin-right: -1px;
  margin-left: -1px;
}
#filters > div:last-child::after {
  display: block;
  clear: both;
  content: "";
}
#filters > div:last-child > div {
  float: left;
}
#filters > div:last-child > div button {
  border-radius: 0.5rem;
  width: 100%;
  display: flex;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 2.6rem;
  align-items: center;
  padding: 0 0.8rem;
  border: 1px solid #333333;
}
#filters > div:last-child > div:last-child > button {
  display: flex;
  justify-content: center;
  text-transform: uppercase;
}
#filters > div:last-child > div:last-child > button i {
  margin-right: 0.2rem;
}
#filters > div:last-child > div.search {
  display: flex;
  align-items: center;
  position: relative;
}

#filters > div:last-child > div.search input {
  background: var(--bgcards);
  height: 100%;
  border-radius: 0.5rem;
  border: 1px solid #333333;
  height: 2.6rem;
}
#filters > div:last-child > div.search:after {
  content: "\f002";
  font-family: "Font Awesome 6 Pro";
  color: var(--pt);
  position: absolute;
  right: 1rem;
}
#filters > div:last-child > div .dropdown button {
  color: var(--pt);
  background: var(--bgcards);
  justify-content: space-between;
}
#filters > div:last-child > div .dropdown button span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#filters > div:last-child > div .dropdown button:after {
  content: "\f078";
  font-family: "Font Awesome 6 Pro";
  color: var(--pt);
  transition: transform 0.3s;
  font-size: 0.9rem;
}
#filters > div:last-child > div .dropdown button:hover {
  color: #fff;
  border-color: var(--color-7);
}
#filters > div:last-child > div .dropdown.show button {
  color: #fff;
  border-color: var(--color-7);
}
#filters > div:last-child > div .dropdown.show button:after {
  transform: rotate(180deg);
  color: #fff;
}
#filters > div:last-child > div .dropdown .dropdown-menu {
  padding: 0.5rem;
}
#filters > div:last-child > div .dropdown .dropdown-menu.lg {
  width: 500px;
}
#filters > div:last-child > div .dropdown .dropdown-menu.md {
  width: 350px;
}
#filters > div:last-child > div .dropdown .dropdown-menu.c1 li {
  width: 100%;
  float: left;
}
#filters > div:last-child > div .dropdown .dropdown-menu.c2 li {
  width: 50%;
  float: left;
}
#filters > div:last-child > div .dropdown .dropdown-menu.c3 li {
  width: 33.3333333333%;
  float: left;
}
#filters > div:last-child > div .dropdown .dropdown-menu.c4 li {
  width: 25%;
  float: left;
}

#filters > div:last-child > div .dropdown .dropdown-menu li {
  margin: 0;
  list-style: none;
  overflow: hidden;
  padding: 1.5px;
}
#filters > div:last-child > div .dropdown .dropdown-menu li label {
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0.2rem 0.5rem;
  color: var(--pt);
  width: 100%;
  border-radius: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
}
#filters > div:last-child > div .dropdown .dropdown-menu li:hover label {
  transition: background 0.3s, color 0.3s;
  background: var(--secb);
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li:hover
  > input[type="checkbox"]
  + label,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li:hover
  > input[type="checkbox"]
  + label:before,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li:hover
  > input[type="radio"]
  + label,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li:hover
  > input[type="radio"]
  + label:before {
  color: #fff;
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="checkbox"],
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="radio"] {
  display: none;
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="checkbox"]
  + label:before,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="radio"]
  + label:before {
  font-family: "Font Awesome 6 Pro";
  margin-right: 0.2rem;
  vertical-align: -0.1rem;
  transition: color 0.3s;
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input.exclude[type="checkbox"]
  + label:before,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="checkbox"]
  + label:before {
  content: "\f0c8";
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="checkbox"]:checked
  + label {
  color: #fff;
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="checkbox"]:checked
  + label:before {
  content: "\f0fe";
  font-weight: 900;
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input.exclude[type="checkbox"]:checked
  + label {
  color: rgb(116 124 136 / 0.5);
  background: var(--color-3);
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input.exclude[type="checkbox"]:checked
  + label:before {
  content: "\f146";
  color: rgb(116 124 136 / 0.5);
  font-weight: 900;
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="radio"]
  + label:before {
  content: "\f111";
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="radio"]:checked
  + label {
  color: var(--color-10);
  background: var(--secb);
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="radio"]:checked
  + label:before {
  content: "\f058";
  font-weight: 900;
}
#filters > div:last-child > div .dropdown .dropdown-menu li .active > label,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  .active
  > label:focus,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  .active
  > label:hover,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input:checked
  ~ label,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input:checked
  ~ label:focus,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input:checked
  ~ label:hover,
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  li
  > input[type="checkbox"]:checked
  + label {
  color: var(--color-10);
  background: var(--secb);
}
#filters > div:last-child > div .dropdown .dropdown-menu ul {
  padding: 0;
  margin: 0;
}
#filters > div:last-child > div .dropdown .dropdown-menu ul::after {
  display: block;
  clear: both;
  content: "";
}
#filters > div:last-child > div .dropdown .dropdown-menu ul.allgenre {
  padding-top: 0.3rem;
  margin-bottom: 0;
  margin-top: 0.4rem;
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  ul.allgenre
  li
  > input[type="checkbox"]:checked
  + label:before {
  content: "\f14a";
}
#filters > div:last-child > div .dropdown .dropdown-menu .genre-mode label {
  color: var(--color-7);
}
#filters
  > div:last-child
  > div
  .dropdown
  .dropdown-menu
  .genre-mode
  label:hover {
  color: #fff;
}
.pagination {
  margin-top: 2rem;
  justify-content: center;
}
.pagination .page-item .page-link {
  padding: 0;
  width: 2.6rem;
  height: 2.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}
.pagination .page-item + .page-item {
  margin-left: 0.2rem;
}
.user-panel .main-inner {
  margin-top: 2rem;
  border-radius: 0.5rem;
  position: relative;
}
.user-panel .main-inner:before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  border-radius: 0.5rem;
  z-index: -1;
}
.user-panel .main-inner .sidebar {
  width: 20rem;
  min-width: unset;
}
.user-panel .main-inner .content {
  margin: 0;
  margin-left: 1.5rem;
}
.user-panel .main-inner .content .items-list.lg .m-item .m-inner {
  box-shadow: rgb(0 0 0 / 0.05) 0 0 0 1px;
}
.user-panel .main-inner .content .items-list.lg .m-item .m-inner:hover {
  box-shadow: rgb(0 0 0 / 0.1) 0 4px 12px;
}
.user-panel #filters > div:last-child > div {
  width: 20%;
  padding: 2px;
}
.user-panel #filters > div:last-child > div:last-child {
  width: 40%;
}
.user-panel .original.card-lg.reading .unit {
  width: 20%;
}
.user-nav {
  list-style: none;
  margin: 0;
  padding: 0;
  border-radius: 0.5rem;
  overflow: hidden;
}
.user-nav li:nth-child(2n + 1) {
  background: #111111;
}
.user-nav li a {
  padding: 0.7rem 2rem;
  color: #747474;
  display: flex;
  font-size: 1.1rem;
  align-items: center;
  letter-spacing: 0.05rem;
}
.user-nav li a i {
  width: 1.7rem;
}
.user-nav li a:hover {
  background: var(--bgcards);
  color: #fff;
}
.user-nav li a.active {
  color: #fff;
  background: #121212;
}
#user-folders {
  margin: 1.5rem 0;
  display: flex;
  align-items: center;
  justify-content: stretch;
  border: 1px solid rgb(35 84 121 / 0.3);
  border-radius: 0.5rem;
}
#user-folders > div {
  height: 100%;
  flex-grow: 1;
}
#user-folders > div:first-child a {
  border-radius: 0.5rem 0 0 0.5rem;
}
#user-folders > div:last-child button {
  border-radius: 0 0.5rem 0.5rem 0;
}
#user-folders > div > a,
#user-folders > div > button {
  width: 100%;
  text-align: center;
  display: block;
  height: 100%;
  color: #fff;
  padding: 0.5rem 0;
  background: var(--secb);
  white-space: nowrap;
}
#user-folders > div > a > i,
#user-folders > div > button > i {
  font-size: 1.3rem;
  line-height: 0.7rem;
}
#user-folders > div > a.active,
#user-folders > div > button.active {
  color: #fff;
  background: var(--color-7);
}
#user-folders > div > a:hover,
#user-folders > div > button:hover {
  color: var(--color-7);
  background: var(--bgcards);
}
#user-folders > div#folder-manager > button {
  border: 0;
}
#user-folders > div#folder-manager .dropdown-menu {
  width: 320px;
}
#user-folders > div#folder-manager .dropdown-menu li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  cursor: pointer;
}
#user-folders > div#folder-manager .dropdown-menu li:not(:last-child):hover {
  background: var(--secb);
}
#user-folders
  > div#folder-manager
  .dropdown-menu
  li:not(:last-child):hover
  .actions {
  opacity: 1;
}
#user-folders > div#folder-manager .dropdown-menu li + li {
  border-top: 1px solid var(--bgcards);
}
#user-folders > div#folder-manager .dropdown-menu li .actions {
  margin-left: 0.5rem;
  transition: opacity 0.3s;
  display: flex;
  opacity: 0;
}
#user-folders > div#folder-manager .dropdown-menu li .actions > button {
  border: 0;
  background: 0 0;
  color: var(--pt);
}
#user-folders
  > div#folder-manager
  .dropdown-menu
  li
  .actions
  > button:last-child {
  padding-right: 0;
}
#user-folders > div#folder-manager .dropdown-menu li .actions > button:hover {
  color: var(--color-7);
}
#user-folders > div#folder-manager .dropdown-menu li .folder-edit {
  width: 100%;
  display: flex;
}
#user-folders > div#folder-manager .dropdown-menu li .folder-edit .actions {
  opacity: 1;
}
#user-folders > div#folder-manager .dropdown-menu li > a {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.9rem;
}
.nav-tabs.mal {
  border-radius: 0.5rem;
  overflow: hidden;
}
.nav-tabs.mal .nav-link {
  background: var(--bgcards);
  color: var(--icol);
  border-right: 1px solid var(--secb);
}
.nav-tabs.mal .nav-link:hover {
  background: var(--secb);
  color: #fff;
}
.nav-tabs.mal .nav-link.active {
  background: var(--color-7);
  color: #fff;
}
main.index .welcome-top {
  position: relative;
  background: linear-gradient(0deg, var(--bgc) 0, var(--bgcards) 100%);
  overflow: hidden;
  color: #fff;
  padding: 7rem 0 16rem;
}
main.index .welcome-top h1 {
  font-size: 3rem;
}
main.index .welcome-top .welcome-bg {
  position: absolute;
  top: 0;
  height: 100%;
  left: 0;
  width: 100%;
}
main.index .welcome-top .welcome-bg > img {
  -o-object-fit: cover;
  object-fit: cover;
  opacity: 0.2;
  width: 100%;
  height: 100%;
}
main.index .welcome-bottom {
  margin-top: -9rem;
  z-index: 3;
  position: relative;
  margin-bottom: 4rem;
}
main.index .welcome-bottom .bg-secondary {
  padding: 2.5rem;
  border-radius: 0.5rem;
}
#toast {
  z-index: 9999;
}
@media (max-width: 1587.9px) {
  body.read #nav-menu > ul > li:nth-child(n + 3) {
    display: none;
  }
}
@media (max-width: 1399.98px) {
  body,
  html {
    font-size: 14px;
  }
  #top-trending .swiper .swiper-wrapper .swiper-slide {
    width: 50%;
  }
  .original.card-lg .unit {
    width: 16.65%;
  }
}
@media (max-width: 1199.98px) {
  body,
  html {
    font-size: 13px;
  }
  #nav-menu-btn {
    display: flex;
  }
  #nav-menu > ul {
    position: absolute;
    left: 1rem;
    top: 4rem;
    display: none;
    flex-direction: column;
    background: var(--bgcards);

    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 0.175);
  }
  #nav-menu > ul > li {
    display: block !important;
  }
  #nav-menu > ul > li > a {
    padding: 0.8rem 1.4rem;
    font-size: 1.05rem;
    color: var(--icol);
  }
  #nav-menu > ul > li:hover > a {
    background: var(--color-3);
    color: var(--color-7);
  }
  #nav-menu > ul > li:hover > a:before {
    margin-left: 0.2rem;
    margin-right: 0.4rem;
    color: var(--color-7);
  }
  #nav-menu > ul > li:hover > ul {
    display: none;
  }
  #nav-menu > ul > li > ul {
    position: unset;
    display: none;
    box-shadow: unset;
    background: var(--color-3);
    margin-top: 0;
    border-radius: 0;
    border: 0;
    border-top: 1px solid var(--color-6);
    border-bottom: 1px solid var(--color-6);
    width: 100% !important;
    max-width: 290px !important;
  }
  #nav-menu > ul > li > ul > li {
    width: 50% !important;
  }
  #top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner .poster {
    opacity: 1;
  }
  #top-trending
    .swiper
    .swiper-wrapper
    .swiper-slide
    .swiper-inner:hover
    .poster {
    transform: rotate(16deg) scale(1.2) translate(1.4rem, 0.3rem);
  }
  .home-swiper .swiper .swiper-wrapper .swiper-slide {
    width: 16.6666666667%;
  }
  .original.card-lg.reading .unit,
  .user-panel .original.card-lg.reading .unit {
    width: 210px;
  }
  main {
    background: 0 0;
  }
  main .main-inner {
    flex-direction: column;
  }
  main .main-inner .sidebar {
    width: 100% !important;
    max-width: unset;
    margin-bottom: 1rem;
  }
  main .main-inner .content {
    width: 100%;
    margin: 0 !important;
  }
  .manga-detail .main-inner .sidebar #info-rating.collapse {
    display: flex;
  }
  .manga-detail .main-inner .sidebar .rating-box {
    margin-left: auto;
    min-width: 23rem;
    height: 6rem;
  }
  .manga-detail
    .main-inner
    .content
    .info
    .actions
    .bookmark.dropright
    .dropdown-menu {
    top: 100%;
    right: 0;
    left: auto;
    margin-top: 0.4rem;
  }
  body.read .nav-user .u-notify {
    display: none;
  }
  body.read header {
    right: 0 !important;
  }
  body.read main .m-content #page-wrapper-x.on-last-page {
    --number-nav-height: 5rem;
  }

  #filters > div:last-child > div {
    padding: 2px;
    width: 20%;
  }
  #filters > div:last-child > div:last-child {
    width: 40%;
  }
  .user-nav {
    display: flex;
    border: 1px solid rgb(35 84 121 / 0.3);
  }
  .user-nav li {
    flex-grow: 1;
  }
  .user-nav li a {
    padding: 0.7rem 0;
    justify-content: center;
  }
}
@media (max-width: 1024px) {
  body.read main.longstrip {
    overflow: unset;
    height: auto !important;
    max-height: unset !important;
  }
}
@media (max-width: 991.98px) {
  #top-trending .swiper .swiper-wrapper .swiper-slide {
    width: 100% !important;
  }
  .home-swiper .swiper .swiper-wrapper .swiper-slide {
    width: 20%;
  }
  footer .abs-footer .wrapper {
    flex-direction: column;
    text-align: center;
  }
  body.read {
    height: 100%;
    min-height: 100%;
  }
  body.read #nav-user .u-notify {
    display: block;
  }
  .user-nav li a span {
    display: none;
  }
  .user-nav li a i {
    width: unset;
  }
}
@media (max-width: 767.98px) {
  #top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner {
    margin: 0;
  }
  .dropdown.responsive.show:before {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgb(0 0 0 / 0.3);
    content: "";
    position: fixed;
    z-index: 49;
  }
  .dropdown.responsive .dropdown-menu {
    position: fixed;
    height: auto !important;
    top: 20vh;
    left: 10vw;
    width: 80vw !important;
    min-width: auto !important;
    max-height: 55vh !important;
    overscroll-behavior: contain;
    padding-top: 10px;
    padding-bottom: 10px;

    z-index: 50;
    overflow: auto;
  }
  .dropdown.responsive .dropdown-menu .dropdown-item,
  .dropdown.responsive .dropdown-menu li {
    width: 100% !important;
  }
  .dropdown.responsive .dropdown-menu .dropdown-item label,
  .dropdown.responsive .dropdown-menu li label {
    font-size: 1rem !important;
  }
  .home-swiper .swiper .swiper-wrapper .swiper-slide {
    width: 25%;
  }
  .original.card-lg .unit {
    width: 100%;
  }
  footer ul li a {
    font-size: 1rem;
  }
  .manga-detail .main-inner .content {
    flex-direction: column;
  }
  .manga-detail .main-inner .content .info {
    padding-left: 0;
    text-align: center;
    padding-top: 2rem;
  }
  .manga-detail .main-inner .content .info .actions {
    justify-content: center;
  }
  .manga-detail
    .main-inner
    .content
    .info
    .actions
    .btn.readnow
    span:first-child {
    display: none;
  }
  .manga-detail
    .main-inner
    .content
    .info
    .actions
    .btn.readnow
    span:nth-child(2) {
    display: block;
  }
  .manga-detail .main-inner .content .info .actions .bookmark button span {
    display: none;
  }
  .manga-detail .main-inner .content .info .actions .bookmark button > i {
    margin: 0;
  }
  .manga-detail
    .main-inner
    .content
    .info
    .actions
    .bookmark.dropright
    .dropdown-menu {
    bottom: 100%;
    left: auto;
    right: 0;
    top: unset;
    margin-bottom: 0.4rem;
  }
  .manga-detail .main-inner #info-rating-btn {
    font-size: 1.1rem;
    border: 1px dashed var(--secb);
    max-width: 20rem;
    margin: 2rem auto 0;
    background: var(--bgc);
    display: flex;
    align-items: center;
  }
  .manga-detail .main-inner #info-rating-btn:hover {
    color: #fff;
    background: var(--color-3);
  }
  .manga-detail .main-inner .sidebar {
    flex-direction: column-reverse;
  }
  .manga-detail .main-inner .sidebar #info-rating.collapse {
    display: block;
  }
  .manga-detail .main-inner .sidebar #info-rating.collapse:not(.show) {
    display: none;
  }
  .manga-detail .main-inner .sidebar .rating-box {
    margin-left: 0;
    width: 100%;
    min-width: unset;
  }
  #user-folders {
    overflow: auto;
  }
  #user-folders > div {
    width: auto;
  }
  #user-folders > div button,
  #user-folders > div > a {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  #user-folders::-webkit-scrollbar {
    height: 2px;
  }
  .manga-bottom .content .m-list .chapvol-tab {
    right: 50%;
    transform: translateX(50%);
  }
  .manga-bottom .content .m-list .vol-list .unit {
    width: 25%;
  }
  body.read #nav-search-btn {
    display: flex;
  }
  body.read #nav-search {
    position: fixed;
    left: 0;
    right: 0;
    margin: -5rem 0 0 0;
    padding: 1rem;
    top: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 3;
    opacity: 0;
    transition: margin 0.3s, opacity 0.3s;
  }
  body.read #nav-search:after {
    position: absolute;
    content: "";
    background: rgb(14 23 38 / 0.9);
    top: 0;
    height: 100%;
    left: 0;
    width: 100%;
    opacity: 0;
    transition: opacity 0.3s, display 0.3s;
  }
  body.read #nav-search.active {
    pointer-events: unset;
    margin-top: 0;
    opacity: 1;
  }
  body.read #nav-search.active:after {
    opacity: 1;
  }
  body.read #nav-search .search-inner form {
    height: 3.2rem;
  }
  body.read #nav-search .search-inner form > a {
    height: 2.4rem;
  }
  #filters > div:last-child > div,
  .user-panel #filters > div:last-child > div {
    padding: 2px;
    width: 33.3333333333%;
  }
  #filters > div:last-child > div:last-child,
  .user-panel #filters > div:last-child > div:last-child {
    width: 33.3333333333%;
  }
  main.index .welcome-top {
    padding: 3rem 0 12rem;
    text-align: center;
  }
  main.index .welcome-top h1 {
    font-size: 2.5rem;
  }
  main.index .welcome-top h2 {
    font-size: 1.5rem;
  }
}
@media (max-width: 639.9px) and (min-width: 480px) {
  .home-swiper .swiper .swiper-wrapper .swiper-slide {
    width: 33.3333333333%;
  }
  .manga-bottom .content .m-list .vol-list .unit {
    width: 33.3333333333%;
  }
}
@media (max-width: 575.98px) {
  body.read header .viewing > span {
    display: block;
    line-height: 1.3rem;
    height: 1.3rem;
  }
  body.read header .viewing > span:first-child {
    font-size: 0.85rem;
    color: rgb(116 124 136 / 0.8);
  }
  body.read header .viewing > span:last-child {
    font-weight: 500;
  }
  body.read header .viewing > span:last-child:after {
    content: " ";
  }
  section .head.long-tabs {
    flex-direction: column;
  }
  section .head.long-tabs .tabs {
    margin-top: 1rem;
  }
  section .head.long-tabs .tabs .s-pagi {
    margin-left: auto !important;
  }
  #nav-search-btn {
    display: flex;
  }
  #nav-search {
    position: fixed;
    left: 0;
    right: 0;
    margin: -5rem 0 0 0;
    padding: 1rem;
    top: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 3;
    opacity: 0;
    transition: margin 0.3s, opacity 0.3s;
  }
  #nav-search:after {
    position: absolute;
    content: "";
    background: rgb(14 23 38 / 0.9);
    top: 0;
    height: 100%;
    left: 0;
    width: 100%;
    opacity: 0;
    transition: opacity 0.3s, display 0.3s;
  }
  #nav-search.active {
    pointer-events: unset;
    margin-top: 0;
    opacity: 1;
  }
  #nav-search.active:after {
    opacity: 1;
  }
  #nav-search .search-inner form {
    height: 3.2rem;
  }
  #nav-search .search-inner form > a {
    height: 2.4rem;
  }
  #top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner .poster {
    position: absolute;
    right: 0;
  }
  #top-trending .swiper .swiper-wrapper .swiper-slide .swiper-inner .info {
    z-index: 7;
    position: relative;
  }
  #top-trending
    .swiper
    .swiper-wrapper
    .swiper-slide
    .swiper-inner
    .info
    .below
    > div
    a,
  #top-trending
    .swiper
    .swiper-wrapper
    .swiper-slide
    .swiper-inner
    .info
    .below
    > p {
    text-shadow: 1px 1px 3px #000;
  }
  #top-trending
    .swiper
    .swiper-wrapper
    .swiper-slide
    .swiper-inner
    .info
    .below
    > p {
    color: #fff;
  }
  .home-swiper .swiper .swiper-wrapper .swiper-slide {
    width: 33.3333333333%;
  }
  section .head.sm-column {
    flex-direction: column;
  }
  section .head.sm-column h2 {
    margin-bottom: 1rem;
  }
  section .head.sm-column .nav .nav-item {
    flex-grow: 1;
  }
  section .head.sm-column .nav .nav-item .nav-link {
    width: 100%;
    padding: 0.5rem 0;
  }
  section .head h2 {
    font-size: 1.3rem;
  }
  section .head h2 span {
    display: none;
  }
  .original.card-lg .unit .inner .info > div nav {
    width: 7rem;
  }
  .original.card-lg .unit .inner .info > div nav > span span {
    display: none;
  }

  footer .inner {
    flex-direction: column;
    align-items: center;
  }
  footer .inner > div {
    text-align: center;
  }
  footer .inner > div > p {
    display: none;
  }
  footer .inner nav {
    margin-top: 1.5rem;
    text-align: center;
  }
  footer .inner nav ul {
    margin-top: 0.5rem;
  }
  .manga-bottom .content .m-list ul li a span:last-child {
    white-space: nowrap;
    text-align: right;
    flex-shrink: 0;
  }
  .manga-bottom .content .m-list .vol-list .unit {
    width: 33.3333333333%;
  }
  body.read #show-ctrl-menu span {
    display: none;
  }
  .advanced-settings .setting-tab.page-layout > div {
    flex-direction: column;
    height: unset;
    padding: 0.8rem 0.3rem;
  }
  .advanced-settings .setting-tab.page-layout > div span {
    line-height: 1.2rem;
    margin-top: 0.5rem;
  }
}
@media (max-width: 410px) {
  .original.card-lg .unit .inner .info > div img:nth-child(n + 6) {
    display: none;
  }
  .manga-bottom .content .m-list .list-menu form input {
    width: 8.7rem;
  }
  .manga-bottom .content .m-list .list-menu .dropdown .btn span {
    display: none;
  }
  .manga-bottom .content .m-list .vol-list .unit {
    width: 50%;
  }
  #filters > div:last-child > div,
  .user-panel #filters > div:last-child > div {
    padding: 2px;
    width: 50%;
  }
  #filters > div:last-child > div:last-child,
  .user-panel #filters > div:last-child > div:last-child {
    width: 100%;
  }
}
@media (max-width: 370px) {
  .home-swiper .swiper .swiper-wrapper .swiper-slide {
    width: 50%;
  }
  .original.card-lg > div > div .info > ul li a span:last-child {
    display: none;
  }
  .original.card-lg > div > div .info div img:nth-child(n + 3) {
    display: none;
  }
  .advanced-settings .setting-tab.read-direction > div > span {
    display: none;
  }
  .advanced-settings .setting-tab.read-direction > div b {
    display: block;
  }
  .advanced-settings .setting-tab.progress-position div span {
    display: none;
  }
  .hide-370 {
    display: none;
  }
}
