<section class="home-swiper" id="most-viewed">
    <div class="head">
        <h2>
            <?php _e('Les Plus Populaires', 'child-mfire'); ?>
        </h2>
    </div>
<?php
// Query arguments for most viewed manga
$args = array(
    'post_type' => 'wp-manga',
    'posts_per_page' => 12,
    'meta_key' => '_wp_manga_views',
    'orderby' => 'meta_value_num',
    'order' => 'DESC'
);

$popular_manga = new WP_Query($args);
?>
    <div class="tab-content">
        <div class="swiper-container">
            <div class="swiper swiper-msv swiper-container-initialized swiper-container-horizontal">
                <div class="card-md swiper-wrapper most-viewed-wrapper original card-lg">
                    <?php if ($popular_manga->have_posts()) : ?>
                        <?php 
                        $posts = array();
                        while ($popular_manga->have_posts()) : $popular_manga->the_post();
                            ob_start();
                            ?>
                            <div class="swiper-slide unit">
                                <div class="inner">
                                    <a href="<?php the_permalink(); ?>" class="poster">
                                        <div class="poster-image-wrapper shine-effect">
                                            <?php
                                            // Get comic type and flag
                                            $types = wp_get_post_terms(get_the_ID(), 'wp-manga-type');
                                            $comic_type = !empty($types) ? $types[0]->name : '';
                                            $flag_path = get_stylesheet_directory_uri() . '/assets/images/flags/';
                                            
                                            $type_to_flag = [
                                                'manga'  => 'jp.svg',
                                                'manhwa' => 'kr.svg',
                                                'manhua' => 'cn.svg'
                                            ];
                                            $flag_image = isset($type_to_flag[strtolower($comic_type)]) 
                                                ? $flag_path . $type_to_flag[strtolower($comic_type)]
                                                : '';
                                            
                                            if($flag_image): ?>
                                            <div class="manga-flag .most" data-type="<?php echo esc_attr(strtolower($comic_type)); ?>">
                                                <img src="<?php echo esc_url($flag_image); ?>" alt="<?php echo esc_attr($comic_type); ?>" title="<?php echo esc_attr($comic_type); ?>" class="flag-icon">
                                            </div>
                                            <?php endif; ?>
                                            <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'manga_cover'); ?>" 
                                                 alt="<?php the_title(); ?>" 
                                                 style="height: 100%;object-fit: cover;"/>
                                        </div>
                                    </a>
                                    <div class="info">
                                        <a href="<?php the_permalink(); ?>" class="c-title"><?php the_title(); ?></a>
                                        <ul class="content" data-name="chap">
                                            <?php
                                            $chapters = get_latest_chapters(get_the_ID(), 2);
                                            if ($chapters) {
                                                foreach ($chapters as $chapter) { ?>
                                                    <li>
                                                        <a href="<?php echo esc_url(get_permalink(get_the_ID()) . $chapter->chapter_slug); ?>">
                                                            <span class="ch-num">Ch. <?php echo esc_html($chapter->chapter_name); ?></span>
                                                            <span class="ch-date"><?php echo esc_html(time_ago($chapter->date)); ?></span>
                                                        </a>
                                                    </li>
                                                <?php }
                                            } ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <?php
                            $posts[] = ob_get_clean();
                        endwhile;
                        
                        // Output posts twice for seamless loop
                        echo implode('', $posts);
                        echo implode('', $posts);
                        ?>
                    <?php endif; ?>
                    <?php wp_reset_postdata(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .swiper-msv {
        position: relative;
        overflow: hidden;
    }

    .swiper-slide.unit {
        width: auto;
        flex-shrink: 0;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    new Swiper('.swiper-msv', {
        slidesPerView: 'auto',
        loop: true,
        loopedSlides: <?php echo $popular_manga->post_count; ?>,
        autoplay: {
            delay: 2000,
            disableOnInteraction: false,
        },
        speed: 1000,
    });
});
</script>
</section>


