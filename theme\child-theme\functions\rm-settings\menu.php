<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

function enqueue_mfire_assets() {
    wp_enqueue_style('manga-admin-styles', get_stylesheet_directory_uri() . '/functions/assets/styles.css');
    wp_enqueue_script('manga-admin-scripts', get_stylesheet_directory_uri() . '/functions/assets/scripts.js', array('jquery'), null, true);

    // Localize script to pass AJAX URL and nonce to the admin script
    wp_localize_script('manga-admin-scripts', 'ajax_manga_params', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('load_manga_nonce')
    ));
}
add_action('admin_enqueue_scripts', 'enqueue_mfire_assets'); 

// Include callback files
require_once get_stylesheet_directory() . '/functions/rm-settings/parent-menu.php';
require_once get_stylesheet_directory() . '/functions/rm-settings/storage-settings.php';
require_once get_stylesheet_directory() . '/functions/rm-settings/chapter-reports.php';
require_once get_stylesheet_directory() . '/functions/rm-settings/chapter-reviews.php';
require_once get_stylesheet_directory() . '/functions/rm-settings/import-manga.php';
require_once get_stylesheet_directory() . '/functions/rm-settings/import-manga-logic.php';
require_once get_stylesheet_directory() . '/functions/rm-settings/import-chapters-logic.php';
require_once get_stylesheet_directory() . '/functions/rm-settings/requests.php';
require_once get_stylesheet_directory() . '/functions/rm-settings/export-manga.php';
require_once get_stylesheet_directory() . '/functions/rm-settings/sponsored-content.php';

function chapter_reviews_count() {
    global $wpdb;
    $prefix = $wpdb->prefix;

    // Query to count chapters that need review (where chapter_status = 3)
    $count = $wpdb->get_var( 
        $wpdb->prepare("SELECT COUNT(*) FROM {$prefix}manga_chapters WHERE chapter_status = %d", 3)
    );

    // Return the count wrapped in a span with 'awaiting-mod' class if greater than 0
    return $count > 0 ? '<span class="awaiting-mod">' . $count . '</span>' : '';
}

// Hook to add custom admin menu
add_action('admin_menu', 'custom_manga_menu');
function custom_manga_menu() {
    // Add parent menu
    add_menu_page(
        'Raijin Settings',            // Page title
        'Raijin Settings',            // Menu title
        'manage_options',        // Capability
        'mfire-settings',            // Menu slug
        'mfire_settings_callback',   // Callback function
        'dashicons-sos',                      // Icon URL (leave empty for default)
        3                        // Position in menu
    );

    add_submenu_page(
        'mfire-settings',            // Parent slug
        'Storage Settings',             // Page title
        'Storage Settings',             // Menu title
        'manage_options',        // Capability
        'storage-settings',             // Menu slug
        'storage_settings_callback'     // Callback function
    );

    // Add submenus
    add_submenu_page(
        'mfire-settings',            // Parent slug
        'Reports',             // Page title
        'Reports',             // Menu title
        'manage_options',        // Capability
        'chapter-reports',             // Menu slug
        'chapter_reports_callback'     // Callback function
    );

    // Add submenus
    add_submenu_page(
        'mfire-settings',            // Parent slug
        'Requests',             // Page title
        'Requests',             // Menu title
        'manage_options',        // Capability
        'requests',             // Menu slug
        'requests_callback'     // Callback function
    );

    add_submenu_page(
        'mfire-settings',            // Parent slug
        'Review',             // Page title
        'Review ' . chapter_reviews_count(), // Menu title with count
        'manage_options',        // Capability
        'chapter-reviews',             // Menu slug
        'chapter_reviews_callback'     // Callback function
    );

    // Add submenus
    add_submenu_page(
        'mfire-settings',            // Parent slug
        'Import Manga',             // Page title
        'Import Manga',             // Menu title
        'manage_options',        // Capability
        'import-manga',             // Menu slug
        'import_manga_callback'     // Callback function
    );

    add_submenu_page(
        'mfire-settings',            // Parent slug
        'Sponsored Content',             // Page title
        'Sponsored Content',             // Menu title
        'manage_options',        // Capability
        'sponsored-content-settings', // Menu slug
        'sponsored_content_settings_callback' // Callback function
    );
}

// Hook to add custom menu to admin toolbar
add_action('admin_bar_menu', 'add_chapter_reviews_to_toolbar', 999);

function add_chapter_reviews_to_toolbar($wp_admin_bar) {
    // Only show the toolbar menu if the user has permission to manage options
    if (!current_user_can('manage_options')) {
        return;
    }

    // Get chapter reviews count
    $count = chapter_reviews_count();

    // Add the Chapter Reviews item to the toolbar
    $wp_admin_bar->add_node(array(
        'id'    => 'chapter-reviews-toolbar', // Unique ID for the toolbar item
        'title' => 'Chapter Reviews ' . $count, // Title with count styled
        'href'  => admin_url('admin.php?page=chapter-reviews'), // Link to the reviews page
        'meta'  => array(
            'class' => 'chapter-reviews-toolbar'
        )
    ));
}

// --- Add cache settings to mfire-settings ---
add_filter('mfire_settings_fields', function($fields) {
    $fields['cache_section'] = array(
        'type' => 'section',
        'label' => __('Cache Settings', 'child-mfire'),
    );
    $fields['theme_cache_chapter_content'] = array(
        'type' => 'number',
        'label' => __('Chapter Content Cache (minutes)', 'child-mfire'),
        'default' => 5,
        'min' => 1,
    );
    $fields['theme_cache_popular_manga'] = array(
        'type' => 'number',
        'label' => __('Popular Manga Cache (minutes)', 'child-mfire'),
        'default' => 5,
        'min' => 1,
    );
    $fields['theme_cache_search'] = array(
        'type' => 'number',
        'label' => __('Search Cache (minutes)', 'child-mfire'),
        'default' => 2,
        'min' => 1,
    );
    $fields['theme_cache_recently_updated'] = array(
        'type' => 'number',
        'label' => __('Recently Updated Cache (minutes)', 'child-mfire'),
        'default' => 5,
        'min' => 1,
    );
    return $fields;
});
