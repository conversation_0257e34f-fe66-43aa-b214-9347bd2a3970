/* Premium chapter styling for manga details page - Higher specificity */
.list-body-hh .item a.cairo-premium,
a.cairo-premium {
    color: #ffc71e !important;
    background: #ffc71e24 !important;
    border: thin solid #ffc71e91 !important;
    border-radius: 6px !important;
    position: relative !important;
}

.list-body-hh .item a.cairo-premium:hover,
a.cairo-premium:hover {
    background: #ffc71e40 !important;
    border-color: #ffc71e !important;
    color: #ffc71e !important;
}

.list-body-hh .item a.cairo-premium::after,
a.cairo-premium::after {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    opacity: 0.8;
    color: #ffc71e;
}

.c-premium-tag {
    background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%) !important;
    color: white !important;
    padding: 2px 8px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3) !important;
}

.premium-ch {
    background: linear-gradient(135deg, #ffc71e 0%, #ffb347 100%) !important;
    color: #333 !important;
    padding: 0px 8px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(255, 199, 30, 0.3) !important;
}

.premium-chapter {
    position: relative;
}

.premium-chapter::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 199, 30, 0.1) 50%, transparent 70%);
    pointer-events: none;
    border-radius: 4px;
}

/* Hide premium content for non-subscribers */
body.premium-chapter-blocked .page-break {
    display: none !important;
}

body.premium-chapter-blocked .wp-manga-chapter-img {
    display: none !important;
}

body.premium-chapter-blocked .reading-content .page-break {
    display: none !important;
}