<?php

if( !get_option('enable_chapter_reports', 1) ) {
    return;
}

// Create reports table on plugin/theme activation
function create_reports_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'manga_reports';

    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        report_type varchar(10) NOT NULL,
        user_id bigint(20) UNSIGNED DEFAULT NULL,
        manga_name varchar(255) NOT NULL,
        chapter_number varchar(255) NOT NULL,
        message text NOT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
        PRIMARY KEY  (id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}
add_action('after_switch_theme', 'create_reports_table');

// Handle report submission
function handle_report_submission() {
    // Verify if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('Veuillez vous connecter');
        wp_die();
    }

    // Nonce verification for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'load_manga_nonce')) {
        wp_send_json_error('Une erreur s\'est produite lors du traitement de votre demande');
        wp_die();
    }

    // Rate limiting
    $user_id = get_current_user_id();
    $last_submission = get_user_meta($user_id, 'last_report_submission', true);
    $current_time = current_time('timestamp');

    // Check if the user submitted a report in the last 60 seconds
    if ($last_submission && ($current_time - $last_submission < 60)) {
        wp_send_json_error('Veuillez attendre avant de soumettre un autre signalement');
        wp_die();
    }

    // Store the current timestamp for the last submission
    update_user_meta($user_id, 'last_report_submission', $current_time);


    if (!isset($_POST['message'], $_POST['manga_name'], $_POST['chapter_number'], $_POST['report_type'])) {
        wp_send_json_error('Demande invalide.');
        wp_die();
    }

    // Sanitize and validate the input data
    $report_type = sanitize_text_field($_POST['report_type']);
    $user_id = get_current_user_id(); // Since we are restricting to logged-in users, get the user ID
    $manga_name = sanitize_text_field($_POST['manga_name']);
    $chapter_number = sanitize_text_field($_POST['chapter_number']);
    $message = sanitize_textarea_field($_POST['message']);

    global $wpdb;
    $table_name = $wpdb->prefix . 'manga_reports';
    $wpdb->insert($table_name, array(
        'report_type' => $report_type,
        'user_id' => $user_id,
        'manga_name' => $manga_name,
        'chapter_number' => $chapter_number,
        'message' => $message,
        'created_at' => current_time('mysql')
    ));

    if ($wpdb->insert_id) {
        wp_send_json_success('Votre signalement a été transmis avec succès');
    } else {
        wp_send_json_error('Une erreur s\'est produite lors du traitement de votre demande, réessayez plus tard.');
    }
    wp_die();
}
add_action('wp_ajax_submit_report', 'handle_report_submission');
add_action('wp_ajax_nopriv_submit_report', 'handle_report_submission');

// Handle bulk delete via AJAX
function handle_bulk_delete_reports() {
    check_ajax_referer('load_manga_nonce', 'nonce'); // Verify nonce for security

    if (isset($_POST['report_ids']) && is_array($_POST['report_ids'])) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'manga_reports';
        $report_ids = array_map('intval', $_POST['report_ids']); // Sanitize input

        // Prepare placeholders for SQL query
        $placeholders = implode(',', array_fill(0, count($report_ids), '%d'));

        // Perform the deletion
        $deleted = $wpdb->query($wpdb->prepare("DELETE FROM $table_name WHERE id IN ($placeholders)", $report_ids));

        if ($deleted) {
            wp_send_json_success('Les rapports sélectionnés ont été supprimés.');
        } else {
            wp_send_json_error('No reports were deleted. Please try again.');
        }
    } else {
        wp_send_json_error('No reports selected.');
    }
}
add_action('wp_ajax_bulk_delete_reports', 'handle_bulk_delete_reports');


function chapter_reports_callback() { 
    global $wpdb;
    $table_name = $wpdb->prefix . 'manga_reports';
    $reports = $wpdb->get_results("SELECT * FROM $table_name");
    ?>

    <div class="wrap">
        <h1>Manga Reports</h1>
        <form id="bulk-delete-form">
            <table class="widefat fixed">
                <thead>
                    <tr>
                        <th style="width: 20px;"><input type="checkbox" id="select_all_reports"></th>
                        <th style="width: 80px;">Type</th>
                        <th style="width: 80px;">User ID</th>
                        <th>Manga Name</th>
                        <th style="width: 150px;">Chapter Number</th>
                        <th>Message</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($reports)) : ?>
                        <?php foreach ($reports as $report) : ?>
                            <tr>
                                <td><input type="checkbox" name="report_ids[]" value="<?php echo esc_attr($report->id); ?>"></td>
                                <td><?php echo esc_html($report->report_type); ?></td>
                                <td><?php echo $report->user_id ? esc_html($report->user_id) : 'N/A'; ?></td>
                                <td><a href="#"><?php echo esc_html($report->manga_name); ?></a></td>
                                <td><a href="#"><?php echo esc_html($report->chapter_number); ?></a></td>
                                <td><?php echo esc_html($report->message); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <tr>
                            <td colspan="6">No reports found.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            <p>
                <button type="button" id="bulk-delete-button" class="button button-danger">Delete Selected Reports</button>
            </p>
        </form>
    </div>

    <script>
        jQuery(document).ready(function($) {
            $('#select_all_reports').on('click', function() {
                $('input[name="report_ids[]"]').prop('checked', this.checked);
            });

            $('#bulk-delete-button').on('click', function() {
                var reportIds = [];
                $('input[name="report_ids[]"]:checked').each(function() {
                    reportIds.push($(this).val());
                });

                if (reportIds.length > 0) {
                    if (confirm('Are you sure you want to delete the selected reports?')) {
                        $.ajax({
                            url: ajax_manga_params.ajax_url,
                            type: 'POST',
                            data: {
                                action: 'bulk_delete_reports',
                                nonce: ajax_manga_params.nonce,
                                report_ids: reportIds
                            },
                            success: function(response) {
                                if (response.success) {
                                    location.reload(); // Refresh the page to see updated reports
                                } else {
                                    alert(response.data); // Display error message
                                }
                            },
                            error: function() {
                                alert('An error occurred. Please try again.');
                            }
                        });
                    }
                } else {
                    alert('Please select at least one report to delete.');
                }
            });
        });
    </script>

    <?php 
}
