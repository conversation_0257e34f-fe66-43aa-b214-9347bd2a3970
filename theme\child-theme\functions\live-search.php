<?php 

function myplugin_enqueue_scripts() {
    wp_enqueue_script('live-search', get_stylesheet_directory_uri() . '/assets/js/live-search.js', array(), '1.0', true);
}
add_action('wp_enqueue_scripts', 'myplugin_enqueue_scripts');

function myplugin_register_rest_routes() {
    register_rest_route('myplugin/v1', '/search', array(
        'methods' => 'GET',
        'callback' => 'myplugin_search_callback',
        'permission_callback' => '__return_true',
    ));
}
add_action('rest_api_init', 'myplugin_register_rest_routes');

function myplugin_search_callback(WP_REST_Request $request) {
    $query = sanitize_text_field($request->get_param('query'));
    $args = array(
        'post_type' => 'wp-manga',
        's' => $query,
        'posts_per_page' => 5,
    );
    $query = new WP_Query($args);
    $results = array();

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $results[] = array(
                'title' => get_the_title(),
                'url' => get_permalink(),
                'thumbnail' => get_the_post_thumbnail_url(null, 'thumbnail'),
            );
        }
        wp_reset_postdata();
    }

    return $results;
}