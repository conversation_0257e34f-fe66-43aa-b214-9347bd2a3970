<?php if( get_option('allow_post', 1) ) : ?>

<main class="user-panel">
    <div class="container">
        <div class="main-inner">
            <aside class="sidebar">
                <ul class="user-nav">
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=account-settings'); ?>"><i class="fa-solid fa-user"></i> <span>إعدادات الحساب</span></a>
                    </li>
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=bookmark'); ?>"><i class="fa-solid fa-bookmark"></i> <span>المفضلة</span></a>
                    </li>

                    <li>
                        <a class="active" href="<?php echo site_url('/user/?tab=add-manga'); ?>"><i class="fa-solid fa-circle-plus"></i> <span>إضافة مانجا</span></a>
                    </li>
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=chapters'); ?>"><i class="fa-solid fa-book-open-reader"></i> <span>قائمة الفصول</span></a>
                    </li>
                    
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=reading'); ?>"><i class="fa-solid fa-clock-rotate-left"></i> <span>سجل القراءة</span></a>
                    </li>
                    <?php /*
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=notification'); ?>"><i class="fa-solid fa-bell"></i> <span>الإشعارات</span></a>
                    </li>
                    */ ?>
                </ul>
            </aside>

            <aside class="content">
                <section>
                    <div class="head">
                        <h2>إضافة مانجا</h2>
                    </div>
                    <div class="tab-content" data-name="import" style="display: block;">
                        <form id="addMangaForm" autocomplete="off" class="ajax" method="post" enctype="multipart/form-data">
                            <p class="text-muted">
                                <span class="text-danger">إقرأ قبل إضافة مانجا</span><br />
                                - لا تقم بكتابة إسم المؤلف و الرسام بالعربية<br />
                                - من فضلك لا تقم بنشر المانجا إن لم تكن ستنشر فصل معها في نفس اليوم<br />
                            </p>
                            <div class="form-group light">
                                <div class="control-name">إسم المانجا</div>
                                <div><input type="text" class="form-control" name="manga_name" placeholder="" /></div>
                            </div>
                            <div class="form-group light">
                                <div class="control-name">أسماء أخرى</div>
                                <div><input type="text" class="form-control" name="alternative" placeholder="جميع الأسماء الأخرى المتاحة بجميع اللغات" /></div>
                            </div>
                            <div class="form-group light">
                                <div class="control-name">القصة</div>
                                <div><textarea rows="3" class="form-control" name="summary" placeholder="حاول أن تجعلها مختصرة"></textarea></div>
                            </div>
                            <div class="form-group light">
                                <div class="control-name">المؤلف و الرسام (ضع فاصلة بين الأسماء)</div>
                                <div><input type="text" class="form-control" name="authors" placeholder="Name, Another Name" /></div>
                            </div>
                            <div class="form-group light">
                                <div class="control-name">سنة الإصدار</div>
                                <div><input type="text" class="form-control" name="release_year" placeholder="فقط السنة. بدون اليوم أو الشهر"  /></div>
                            </div>
                            <div class="form-group light">
                                <div class="control-name">النوع</div>
                                <div>
                                    <?php
                                    // Get the taxonomy terms for 'manga_type'
                                    $terms = get_terms(array(
                                        'taxonomy' => 'manga_type',
                                        'hide_empty' => false,
                                    ));

                                    // Check if terms exist
                                    if (!empty($terms) && !is_wp_error($terms)) {
                                        foreach ($terms as $term) {
                                            // Generate a unique ID for each radio button
                                            $term_id = esc_attr($term->term_id);
                                            $term_slug = esc_attr($term->slug);
                                            $term_name = esc_html($term->name);
                                            ?>
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input type="radio" class="custom-control-input" id="type_<?php echo $term_slug; ?>" name="manga_type" value="<?php echo $term_slug; ?>" />
                                                <label class="custom-control-label" for="type_<?php echo $term_slug; ?>"><?php echo $term_name; ?></label>
                                            </div>
                                            <?php
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="form-group light">
                                <div class="control-name">الحالة (حالة القصة وليس الترجمة)</div>
                                <div>
                                    <?php
                                    // Array of possible statuses corresponding to the meta values
                                    $statuses = array(
                                        'on-going' => 'مستمر',
                                        'end' => 'مكتمل',
                                        'canceled' => 'ملغي',
                                        'on-hold' => 'متوقف',
                                        'upcoming' => 'قادم قريبا'
                                    );

                                    // Loop through the statuses to create radio buttons
                                    foreach ($statuses as $key => $label) {
                                        // Generate a unique ID for each radio button
                                        $id = esc_attr("status_$key");
                                        ?>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" class="custom-control-input" id="<?php echo $id; ?>" name="manga_status" value="<?php echo esc_attr($key); ?>" />
                                            <label class="custom-control-label" for="<?php echo $id; ?>"><?php echo esc_html($label); ?></label>
                                        </div>
                                        <?php
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="form-group light">
                                <div class="control-name">التصنيفات</div>
                                <div>
                                    <?php
                                    // Get the taxonomy terms for 'wp-manga-genre'
                                    $genres = get_terms(array(
                                        'taxonomy' => 'wp-manga-genre',
                                        'hide_empty' => false,
                                    ));

                                    // Check if terms exist
                                    if (!empty($genres) && !is_wp_error($genres)) {
                                        foreach ($genres as $genre) {
                                            // Generate a unique ID for each checkbox
                                            $genre_slug = esc_attr($genre->slug);
                                            ?>
                                            <div class="custom-control custom-checkbox custom-control-inline">
                                                <input type="checkbox" class="custom-control-input" id="genre_<?php echo $genre_slug; ?>" name="manga_genres[]" value="<?php echo $genre_slug; ?>" />
                                                <label class="custom-control-label" for="genre_<?php echo $genre_slug; ?>"><?php echo esc_html($genre->name); ?></label>
                                            </div>
                                            <?php
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="form-group light">
                                <div class="control-name">هل يوجد محتوى للبالغين؟</div>
                                <div>
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" class="custom-control-input" id="adult_no" name="is_adult" value="no" checked />
                                        <label class="custom-control-label" for="adult_no">لا</label>
                                    </div>
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" class="custom-control-input" id="adult_yes" name="is_adult" value="yes" />
                                        <label class="custom-control-label" for="adult_yes">نعم</label>
                                    </div>
                                    <div class="form-text text-muted">
                                        - قم بتصنيف المانجا على أنها مخصصة للبالغين إذا كانت تحتوي على مشاهد دموية مبالغ فيها، أو تروج لأفكار إلحادية، أو تعمل على تطبيع المثلية الجنسية والانحراف، أو تحتوي على مشاهد إتشي مفرطة.
                                    </div>
                                </div>
                            </div>
                            <div class="form-group light">
                                <div class="control-name">الغلاف</div>
                                <div>
                                    <div class="custom-file">
                                        <input type="file" class="custom-file-input" id="imagefile" name="imagefile" accept=".jpg, .jpeg, .png, .webp"  onchange="previewImageAndFileName(event)" />
                                        <label class="custom-file-label" for="imagefile">إختيار الصورة</label>
                                    </div>
                                    <div class="form-text text-muted">
                                        الصيغ المدعومة: JPG, JPEG, PNG, WEBP
                                    </div>
                                    <div class="thumb-prev">
                                        <img id="imagePreview" class="rounded" src="" alt="Image Preview" style="display: none; max-width: 100%; margin-top: 10px;" />
                                    </div>
                                </div>
                            </div>

                            <?php wp_nonce_field('add_manga_action', 'add_manga_nonce'); ?>

                            <div class="form-group text-center mt-5">
                                <button type="submit" class="submit-manga btn w-100 mt-1 btn-lg btn-primary">إضافة المانجا</button>
                            </div>
                            <div class="loading" style="display: none;"></div>
                            <div id="responseMessage" class="alert" style="display: none;"></div>
                        </form>
                    </div>
                </section>
            </aside>

            <script>
                document.getElementById('addMangaForm').addEventListener('submit', function(event) {
                    event.preventDefault(); // Prevent the default form submission

                    const formData = new FormData(this); // Create a FormData object
                    formData.append('action', 'add_manga'); // Specify the action

                    // Show loading indicator
                    const loadingIndicator = document.querySelector('.loading');
                    loadingIndicator.style.display = 'block';

                    // Disable the submit button to prevent multiple submissions
                    const submitButton = this.querySelector('button[type="submit"]'); // Assuming the button is of type submit
                    submitButton.disabled = true;

                    fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                        method: 'POST',
                        body: formData,
                    })
                    .then(response => response.json())
                    .then(data => {
                        const responseMessage = document.getElementById('responseMessage');
                        responseMessage.style.display = 'block';
                        responseMessage.className = data.success ? 'alert alert-success' : 'alert alert-danger';
                        responseMessage.innerText = data.success ? data.data.message : (data.data.message || 'An unexpected error occurred.');

                        // Clear the form if submission is successful
                        if (data.success) {
                            this.reset(); // Clear form fields
                            document.getElementById('imagePreview').style.display = 'none'; // Hide image preview
                            const label = this.querySelector('input[type="file"]').nextElementSibling;
                            label.textContent = 'Choose image'; // Reset file input label
                        }
                    })
                    .catch(error => {
                        const responseMessage = document.getElementById('responseMessage');
                        responseMessage.style.display = 'block';
                        responseMessage.className = 'alert alert-danger';
                        responseMessage.innerText = 'An error occurred. Please try again.';
                    })
                    .finally(() => {
                        // Hide loading indicator
                        loadingIndicator.style.display = 'none';
                        // Re-enable the submit button
                        submitButton.disabled = false;
                    });
                });

                function previewImageAndFileName(event) {
                    const file = event.target.files[0];
                    const fileName = file ? file.name : 'Choose image';
                    
                    // Update label text with file name
                    const label = event.target.nextElementSibling;
                    label.textContent = fileName;

                    // Image preview logic
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const imagePreview = document.getElementById('imagePreview');
                            imagePreview.src = e.target.result;
                            imagePreview.style.display = 'block';
                        };
                        reader.readAsDataURL(file);
                    }
                }
            </script>

        </div>
    </div>
</main>

<?php else: 

nocache_headers(); // Ensure page is not cached
include( get_query_template( '404' ) ); // Load the 404 template
exit; // Stop execution of the page

endif; ?>