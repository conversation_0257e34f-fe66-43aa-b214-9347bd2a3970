<div id="page-wrapper-x">
    <div class="pages flex-col-cen">
    <div class="chapter-control">
            <div class="number-nav rtl">
                <a class="prev dir-rtl" 
                <?php if ($prev_chap): ?>
                    href="<?php echo esc_url($wp_manga_functions->build_chapter_url($manga_id, $prev_chap['chapter_slug'])); ?>"
                <?php else: ?>
                    href="<?php the_permalink(); ?>:;"
                <?php endif; ?>>
                    <i class="ltr-icon fa-light fa-arrow-left mr-1"></i>
                    Previous
                </a>
                <a href="<?php echo home_url(); ?>" class="jb-btn">
                    <i class="fa-light fa-lg fa-house"></i>
                    <span>Home</span>
                </a>
                <a class="next dir-rtl" 
                <?php if ($next_chap): ?>
                    href="<?php echo esc_url($wp_manga_functions->build_chapter_url($manga_id, $next_chap['chapter_slug'])); ?>"
                <?php else: ?>
                    href="<?php the_permalink(); ?>"
                <?php endif; ?>>
                     Next
                    <i class="ltr-icon fa-light fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
        <div id="ch-images" style="width: <?php echo isset($_COOKIE['chImageWidth']) ? esc_attr($_COOKIE['chImageWidth']) . 'px' : '722px'; ?>;">
            <?php do_action('wp_manga_chapter_content', $cur_chap, $manga_id); ?>
        </div>

        <div class="chapter-control">
            <div class="number-nav rtl">
                <a class="prev dir-rtl" 
                <?php if ($prev_chap): ?>
                    href="<?php echo esc_url($wp_manga_functions->build_chapter_url($manga_id, $prev_chap['chapter_slug'])); ?>"
                <?php else: ?>
                    href="<?php the_permalink(); ?>:;"
                <?php endif; ?>>
                    <i class="ltr-icon fa-light fa-arrow-left mr-1"></i>
                    Previous
                </a>
                <?php if( get_option('enable_chapter_reports', 1) ) : ?>
                    <button class="jb-btn" data-toggle="modal" data-target="#report">
                        <i class="fa-light fa-lg fa-triangle-exclamation"></i>
                        <span>Report</span>
                    </button>
                <?php endif; ?>
                <a class="next dir-rtl" 
                <?php if ($next_chap): ?>
                    href="<?php echo esc_url($wp_manga_functions->build_chapter_url($manga_id, $next_chap['chapter_slug'])); ?>"
                <?php else: ?>
                    href="<?php the_permalink(); ?>"
                <?php endif; ?>>
                     Next
                    <i class="ltr-icon fa-light fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>

        <div class="container w-full mb-4">
            <?php do_action( 'wp_manga_discussion' ); ?>
        </div>

    </div>

    <!-- New progress circle scroll-to-top -->
    <div class="progress-group">
        <div class="progress-wrap">
            <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
                <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
            </svg>
        </div>
    </div>
    <script>
document.addEventListener('DOMContentLoaded', function() {
    const progressWrap = document.querySelector('.progress-wrap');
    const progressPath = document.querySelector('.progress-wrap path');
    const pathLength = progressPath.getTotalLength();
    const pageWrapper = document.getElementById('page-wrapper-x');
    
    progressPath.style.transition = 'none';
    progressPath.style.strokeDasharray = pathLength + ' ' + pathLength;
    progressPath.style.strokeDashoffset = pathLength;
    progressPath.getBoundingClientRect();
    progressPath.style.transition = 'stroke-dashoffset 10ms linear';

    const updateProgress = () => {
        const scroll = pageWrapper.scrollTop;
        const height = pageWrapper.scrollHeight - pageWrapper.clientHeight;
        const progress = pathLength - (scroll * pathLength / height);
        progressPath.style.strokeDashoffset = progress;
        
        if(scroll > 50) {
            progressWrap.classList.add('active-progress');
        } else {
            progressWrap.classList.remove('active-progress');
        }
    }

    pageWrapper.addEventListener('scroll', updateProgress);

    progressWrap.addEventListener('click', (e) => {
        e.preventDefault();
        pageWrapper.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
});
    </script>
</div>