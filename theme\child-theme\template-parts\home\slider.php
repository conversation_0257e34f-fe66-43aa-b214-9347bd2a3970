<div id="top-trending">
    <div class="container" style="opacity: 0;">
        <div class="swiper trending swiper-container">
            <div class="swiper-wrapper">
                <?php
                global $wpdb;

                // Simplified meta query construction
                $meta_query = [
                    'relation' => 'AND',
                    ['key' => 'manga_banner', 'value' => '', 'compare' => '!=']
                ];

                if (!$show_adult) {
                    $meta_query[] = [
                        'key'     => 'manga_adult_content',
                        'value'   => 'yes',
                        'compare' => 'NOT LIKE'
                    ];
                }

                // Query arguments
                $args = [
                    'post_type'      => 'wp-manga',
                    'posts_per_page' => get_option('slider_settings'),
                    'post_status'    => ['publish', 'private'], // Added to include private posts
                    'meta_query'     => $meta_query,
                ];

                // Helper function to get flag image path
                function get_comic_flag_path($comic_type, $flag_path) {
                    $type_to_flag = [
                        'manga'  => 'jp.svg',
                        'manhwa' => 'kr.svg',
                        'manhua' => 'cn.svg'
                    ];
                    return isset($type_to_flag[strtolower($comic_type)]) 
                        ? $flag_path . $type_to_flag[strtolower($comic_type)]
                        : '';
                }

                $query = new WP_Query($args);
                if ($query->have_posts()) :
                    // Store all posts in an array first
                    $posts = array();
                    while ($query->have_posts()) : $query->the_post();
                        // Get post terms to check for sponsorship and comic type
                        $types = wp_get_post_terms(get_the_ID(), 'wp-manga-type');
                        
                        // Check if manga is sponsored
                        $is_sponsored = false;
                        if (!empty($types)) {
                            foreach ($types as $type) {
                                if (strtolower($type->name) === 'sponsored') {
                                    $is_sponsored = true;
                                    break;
                                }
                            }
                        }

                        // If post is private and NOT sponsored, skip it.
                        // We only want to show private posts if they are sponsored.
                        if (get_post_status(get_the_ID()) === 'private' && !$is_sponsored) {
                            continue;
                        }
                        
                        // Define comic_type from $types (needed later for flags, etc.)
                        // This was previously defined later, around line 77 of the original file state
                        $comic_type = !empty($types) ? $types[0]->name : '';

                        ob_start();
                        // Replace thumbnail with banner
                        $banner = get_post_meta(get_the_ID(), 'manga_banner', true);
                        $thumbnail = !empty($banner) ? $banner : get_the_post_thumbnail_url(get_the_ID(), 'manga_cover');
                        $title = get_the_title();
                        $content = wp_strip_all_tags(get_the_excerpt());
                        $content = wp_trim_words($content, 20, '...'); // Limit to 20 words
                        $permalink = get_permalink();
                        $manga_status = get_manga_status(get_the_ID());
                        // Normalize status text
                        $manga_status = str_replace(
                            ['on-going', 'On-going', 'end', 'Ongoing', 'Completed'], 
                            ['En cours', 'En cours', 'Terminé', 'En cours', 'Terminé'], 
                            $manga_status
                        );
                        $latest_chapter_query = $wpdb->get_results(
                            $wpdb->prepare(
                                "SELECT chapter_name 
                                FROM {$wpdb->prefix}manga_chapters 
                                WHERE post_id = %d AND chapter_status != 3  -- Exclude chapters with status 3
                                ORDER BY CAST(chapter_name AS UNSIGNED) DESC 
                                LIMIT 1",
                                get_the_ID()
                            )
                        );
                        $latest_chapter = !empty($latest_chapter_query) ? $latest_chapter_query[0]->chapter_name : 'N/A';
                        // Get comic type from taxonomy -- This logic is now handled above
                        // $types = wp_get_post_terms(get_the_ID(), 'wp-manga-type');
                        // $comic_type = !empty($types) ? $types[0]->name : '';
                        $flag_path = get_stylesheet_directory_uri() . '/assets/images/flags/';
                        
                        // Check if manga is sponsored -- This logic is now handled above
                        // $is_sponsored = false;
                        // if (!empty($types)) {
                        //     foreach ($types as $type) {
                        //         if (strtolower($type->name) === 'sponsored') {
                        //             $is_sponsored = true;
                        //             break;
                        //         }
                        //     }
                        // }

                        // Determine which flag to show
                        $flag_image = '';
                        switch(strtolower($comic_type)) {
                            case 'manga':
                                $flag_image = $flag_path . 'jp.svg';
                                break;
                            case 'manhwa':
                                $flag_image = $flag_path . 'kr.svg';
                                break;
                            case 'manhua':
                                $flag_image = $flag_path . 'cn.svg';
                                break;
                        }
                        ?>
                        <div class="swiper-slide">
                            <a href="<?php echo $is_sponsored ? 'https://www.polybuzz.ai/?utm_source=reaper-scans&utm_medium=paid_referral' : esc_url($permalink); ?>" 
                               <?php echo $is_sponsored ? 'target="_blank"' : ''; ?> 
                               class="swiper-inner">
                                <?php if ($is_sponsored): ?>
                                <div class="sponsored-badge">
                                    <span>Sponsored</span>
                                </div>
                                <?php endif; ?>
                                <div class="bookmark">
                                    <div class="dropleft width-limit" data-id="<?php echo get_the_ID(); ?>">
                                        <button class="btn bookmark-button" type="button" data-post-id="<?php echo get_the_ID(); ?>">
                                            <i class="fa-solid fa-circle-bookmark"></i>
                                        </button>
                                    </div>
                                </div>
                                <?php if($flag_image): ?>
                                <div class="flag-wrapper">
                                    <div class="slider-flag width-limit" data-type="<?php echo esc_attr(strtolower($comic_type)); ?>">
                                        <img src="<?php echo esc_url($flag_image); ?>" alt="<?php echo esc_attr($comic_type); ?>" title="<?php echo esc_attr($comic_type); ?>" class="flag-icon">
                                    </div>
                                </div>
                                <?php endif; ?>
                                <div class="banner-wrapper" style="background-image: url('<?php echo esc_url($thumbnail); ?>');">
                                    <div class="info">
                                        <div class="above">
                                            <?php if (!$is_sponsored): ?>
                                            <span class="status-wrapper">
                                                <span class="status-dot"></span>
                                                <?php echo esc_html($manga_status); ?>
                                            </span>
                                            <?php endif; ?>
                                            <div class="title-wrapper">
                                                <span class="unit"><?php echo esc_html($title); ?></span>
                                                <?php if ($is_sponsored): ?>
                                                    <span class="ad-badge">Ad</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="below">
                                            <span><?php echo esc_html($content); ?></span>
                                            <?php
                                            // Get rating data
                                            $total_votes = get_post_meta(get_the_ID(), '_manga_total_votes', true);
                                            $total_votes = !empty($total_votes) ? floatval($total_votes) : 0;
                                            $user_votes = get_post_meta(get_the_ID(), '_manga_user_votes', true);
                                            $vote_count = is_array($user_votes) ? count($user_votes) : 0;
                                            ?>
                                            <div class="slider-rating">
                                                <span><?php echo number_format($total_votes, 1); ?></span>
                                                <i class="fas fa-star"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <?php
                        $posts[] = ob_get_clean();
                    endwhile;
                    
                    // Output posts twice for seamless loop
                    echo implode('', $posts);
                    echo implode('', $posts);
                    
                    wp_reset_postdata(); 
                endif; 
                ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const trendingContainer = document.querySelector('#top-trending .container');
    const swiper = new Swiper('.trending', {
        slidesPerView: 'auto',
        spaceBetween: 0,
        loop: true,
        loopedSlides: <?php echo $query->post_count; ?>,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        speed: 1000,
        on: {
            init: function () {
                trendingContainer.style.transition = 'opacity 0.3s ease';
                trendingContainer.style.opacity = '1';
            }
        }
    });
});
</script>


