<main class="user-panel">
    <div class="container">
        <div class="main-inner">
            <aside class="sidebar">
                <ul class="user-nav">
                    <li>
                        <a class="active" href="<?php echo site_url('/user/?tab=account-settings'); ?>"><i class="fa-solid fa-user"></i> <span>Compte</span></a>
                    </li>
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=bookmark'); ?>"><i class="fa-solid fa-bookmark"></i> <span>Favoris</span></a>
                    </li>

                    <?php /* if( get_option('allow_post', 1) ) : ?>
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=add-manga'); ?>"><i class="fa-solid fa-circle-plus"></i> <span>Ajouter un manga</span></a>
                    </li>
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=chapters'); ?>"><i class="fa-solid fa-book-open-reader"></i> <span>Liste des chapitres</span></a>
                    </li>
                    <?php endif; */ ?>

                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=reading'); ?>"><i class="fa-solid fa-clock-rotate-left"></i> <span>Historique</span></a>
                    </li>
                    <?php /*
                    <li>
                        <a class="" href="<?php echo site_url('/user/?tab=notification'); ?>"><i class="fa-solid fa-bell"></i> <span>الإشعارات</span></a>
                    </li>
                    */ ?>
                </ul>
            </aside>
            <aside class="content">
                <section class="max-sm">
                    <div class="head"><h2>Paramètres du compte</h2></div>
                    <div id="response-message" class="alert" style="display: none;"></div>
                    <form id="profile-form" autocomplete="off" class="ajax" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="update_user_profile" />
                        <div class="form-group">
                            <label class="form-label">Photo de profil</label>
                            <div class="profile-picture-section">
                                <div class="current-avatar mb-3">
                                    <?php
                                    $profile_pic = get_user_meta( $current_user->ID, 'profile_picture', true );
                                    $avatar_url = $profile_pic ? esc_url( $profile_pic ) : get_avatar_url( $current_user->ID, 80 );
                                    ?>
                                    <img id="current-profile-pic" src="<?php echo $avatar_url; ?>" alt="Photo de profil actuelle" style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; border: 2px solid #ddd;">
                                </div>
                                <div class="upload-avatar">
                                    <input type="file" name="profile_picture" id="profile-picture-input" accept="image/*" style="display: none;">
                                    <button type="button" id="change-profile-pic" class="btn btn-secondary btn-sm">
                                        <i class="fa-solid fa-camera"></i> Changer la photo
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <input type="text" class="form-control" name="name" value="<?php echo esc_attr( $current_user->display_name ); ?>" placeholder="Nom d'utilisateur" /> 
                        </div>
                        <div class="form-group">
                            <input type="email" class="form-control" name="email" value="<?php echo esc_attr( $current_user->user_email ); ?>" placeholder="Adresse email" />
                        </div>
                        <button class="btn p-0 collapsed" type="button" data-toggle="collapse" data-target="#changepass" aria-controls="changepass" aria-expanded="false">
                            <i class="fa-solid fa-key fa-sm"></i> Changer le mot de passe
                        </button>
                        <div class="collapse" id="changepass">
                            <div class="form-group mt-3">
                                <input type="password" class="form-control" name="password" placeholder="Nouveau mot de passe" />
                            </div>
                            <div class="form-group mb-0">
                                <input type="password" class="form-control" name="password_confirmation" placeholder="Confirmer le mot de passe" />
                            </div>
                        </div>
                        <div>
                            <button id="profile-form-submit" type="button" class="submit btn w-100 mt-3 btn-lg btn-primary">Sauvegarder <i class="fa-solid fa-check"></i></button>
                        </div>
                        <div class="loading" style="display: none;"></div>
                    </form>
                </section>
            </aside>
        </div>
    </div>
</main>